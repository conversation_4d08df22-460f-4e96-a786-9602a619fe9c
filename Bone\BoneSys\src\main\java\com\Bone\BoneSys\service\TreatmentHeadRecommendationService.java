package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.exception.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 治疗头推荐服务
 * 负责治疗头可用性检查和智能推荐
 */
@Service
public class TreatmentHeadRecommendationService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadRecommendationService.class);
    
    // 治疗头可用性标准
    private static final int MIN_BATTERY_LEVEL = 60; // 最低电量要求60%
    private static final int FULL_BATTERY_LEVEL = 100; // 满电状态
    
    // 指示灯颜色定义
    private static final int LIGHT_COLOR_ORANGE = 1; // 橙色
    private static final int LIGHT_COLOR_BLUE = 2;   // 蓝色
    private static final int LIGHT_COLOR_GREEN = 3;  // 绿色
    
    @Autowired
    private HardwareService hardwareService;
    
    /**
     * 检查治疗头可用性并生成推荐
     */
    public TreatmentHeadAvailabilityResponse checkAvailabilityAndRecommend(TreatmentHeadAvailabilityRequest request) 
            throws SerialCommunicationException {
        
        // 0. 向后兼容性处理
        handleBackwardCompatibility(request);
        
        // 1. 输入验证
        try {
            validateRequest(request);
        } catch (InvalidRequestException e) {
            logger.error("Request validation failed: {}", e.getUserMessage());
            return createErrorResponse(e);
        }
        
        int totalRequired = request.calculateTotalRequiredCount();
        int shallowRequired = request.getShallowPatchCount();
        int deepRequired = request.getDeepPatchCount();
        
        logger.info("Checking treatment head availability - Total: {}, Shallow: {}, Deep: {}, Mode: {}", 
                   totalRequired, shallowRequired, deepRequired, request.getTreatmentMode());
        
        // 2. 同步治疗头数据（带错误处理）
        List<TreatmentHeadInfo> allHeads;
        try {
            allHeads = hardwareService.syncAllTreatmentHeads();
            if (allHeads == null || allHeads.isEmpty()) {
                logger.warn("No treatment heads found from hardware");
                return createNoHeadsAvailableResponse(request);
            }
        } catch (SerialCommunicationException e) {
            logger.error("Failed to sync treatment heads from hardware: {}", e.getMessage());
            return createHardwareCommunicationErrorResponse(e);
        } catch (Exception e) {
            logger.error("Unexpected error during treatment head sync: {}", e.getMessage(), e);
            return createUnexpectedErrorResponse(e);
        }
        
        // 3. 分别筛选浅部和深部可用治疗头
        List<TreatmentHeadInfo> shallowAvailableHeads;
        List<TreatmentHeadInfo> deepAvailableHeads;
        try {
            shallowAvailableHeads = filterShallowAvailableHeads(allHeads);
            deepAvailableHeads = filterDeepAvailableHeads(allHeads);
        } catch (Exception e) {
            logger.error("Error filtering available treatment heads: {}", e.getMessage(), e);
            return createFilteringErrorResponse(e);
        }
        
        // 4. 检查各类型数量是否充足并生成详细信息
        AvailabilityDetail availabilityDetail = checkSufficiency(
            shallowAvailableHeads.size(), shallowRequired,
            deepAvailableHeads.size(), deepRequired
        );
        
        // 5. 生成推荐列表和优化的响应消息
        List<TreatmentHeadRecommendation> recommendations = new ArrayList<>();
        String responseMessage;
        
        if (availabilityDetail.isOverallSufficient()) {
            try {
                // 数量充足，生成推荐
                recommendations = generateRecommendationsForMixedPatches(
                    shallowAvailableHeads, deepAvailableHeads, request);
                
                // 点亮推荐治疗头的指示灯（带错误处理）
                try {
                    lightUpRecommendedHeads(recommendations);
                } catch (Exception e) {
                    // 指示灯控制失败不影响推荐结果
                    logger.warn("Light control failed but recommendation continues: {}", e.getMessage());
                    LightControlException lightException = new LightControlException(
                        "Light control failed: " + e.getMessage(), e);
                    // 可以选择记录但不抛出异常
                }
                
                // 生成成功推荐的详细消息
                responseMessage = generateSuccessMessage(recommendations, request.getBodyPartPatches());
                
                logger.info("Treatment head recommendation successful: {} heads recommended for {} body parts", 
                           recommendations.size(), request.getBodyPartPatches() != null ? request.getBodyPartPatches().size() : 0);
                           
            } catch (Exception e) {
                logger.error("Error generating recommendations: {}", e.getMessage(), e);
                return createRecommendationGenerationErrorResponse(e, availabilityDetail);
            }
        } else {
            // 数量不足，记录详细信息
            responseMessage = availabilityDetail.getDetailedMessage();
            
            logger.warn("Insufficient treatment heads: shallow={}/{}, deep={}/{}", 
                       shallowAvailableHeads.size(), shallowRequired,
                       deepAvailableHeads.size(), deepRequired);
                       
            // 可以选择抛出InsufficientTreatmentHeadsException或继续返回响应
            // 这里选择继续返回响应，让调用者决定如何处理
        }
        
        // 合并所有可用治疗头用于响应
        List<TreatmentHeadInfo> allAvailableHeads = new ArrayList<>();
        allAvailableHeads.addAll(shallowAvailableHeads);
        allAvailableHeads.addAll(deepAvailableHeads);
        
        // 创建增强的响应对象
        TreatmentHeadAvailabilityResponse response = new TreatmentHeadAvailabilityResponse(
            availabilityDetail.isOverallSufficient(),
            allAvailableHeads.size(),
            totalRequired,
            allAvailableHeads,
            recommendations,
            responseMessage,
            availabilityDetail
        );
        
        // 记录详细的响应信息
        logResponseDetails(response, request);
        
        return response;
    }
    
    /**
     * 筛选可用的治疗头
     * 条件：(充电中且电量>60%) 或 (充电完成且电量=100%) 且 符合贴片类型要求
     */
    private List<TreatmentHeadInfo> filterAvailableHeads(List<TreatmentHeadInfo> allHeads, String patchType) {
        return allHeads.stream()
            .filter(head -> isHeadAvailable(head) && isHeadMatchPatchType(head, patchType))
            .collect(Collectors.toList());
    }
    
    /**
     * 筛选可用的浅部治疗头（上层1-10槽位）
     */
    private List<TreatmentHeadInfo> filterShallowAvailableHeads(List<TreatmentHeadInfo> allHeads) {
        return allHeads.stream()
            .filter(head -> isHeadAvailable(head) && isShallowHead(head))
            .collect(Collectors.toList());
    }

    /**
     * 筛选可用的深部治疗头（下层1-10槽位）
     */
    private List<TreatmentHeadInfo> filterDeepAvailableHeads(List<TreatmentHeadInfo> allHeads) {
        return allHeads.stream()
            .filter(head -> isHeadAvailable(head) && isDeepHead(head))
            .collect(Collectors.toList());
    }
    
    /**
     * 检查充足性并生成详细信息
     */
    private AvailabilityDetail checkSufficiency(int shallowAvailable, int shallowRequired,
                                               int deepAvailable, int deepRequired) {
        boolean shallowSufficient = shallowAvailable >= shallowRequired;
        boolean deepSufficient = deepAvailable >= deepRequired;
        
        String detailedMessage = generateDetailedAvailabilityMessage(
            shallowAvailable, shallowRequired, shallowSufficient,
            deepAvailable, deepRequired, deepSufficient
        );
        
        return new AvailabilityDetail(
            shallowAvailable, shallowRequired, shallowSufficient,
            deepAvailable, deepRequired, deepSufficient,
            detailedMessage
        );
    }
    
    /**
     * 生成详细的可用性消息
     */
    private String generateDetailedAvailabilityMessage(int shallowAvailable, int shallowRequired, boolean shallowSufficient,
                                                      int deepAvailable, int deepRequired, boolean deepSufficient) {
        StringBuilder messageBuilder = new StringBuilder();
        
        // 总体状态
        boolean overallSufficient = shallowSufficient && deepSufficient;
        if (overallSufficient) {
            messageBuilder.append("✅ 治疗头数量充足");
        } else {
            messageBuilder.append("❌ 治疗头数量不足");
        }
        
        // 详细分类信息
        messageBuilder.append(" - ");
        
        // 浅部治疗头信息
        if (shallowRequired > 0) {
            messageBuilder.append(String.format("浅部(上仓1-10号): %d/%d", shallowAvailable, shallowRequired));
            messageBuilder.append(shallowSufficient ? "✓" : "✗");
            
            if (deepRequired > 0) {
                messageBuilder.append("，");
            }
        }
        
        // 深部治疗头信息
        if (deepRequired > 0) {
            messageBuilder.append(String.format("深部(下仓11-20号): %d/%d", deepAvailable, deepRequired));
            messageBuilder.append(deepSufficient ? "✓" : "✗");
        }
        
        // 如果都不需要
        if (shallowRequired == 0 && deepRequired == 0) {
            messageBuilder.append("无需治疗头");
        }
        
        // 不足时的建议
        if (!overallSufficient) {
            messageBuilder.append("。建议：");
            List<String> suggestions = new ArrayList<>();
            
            if (!shallowSufficient) {
                int shortage = shallowRequired - shallowAvailable;
                suggestions.add(String.format("等待%d个上仓治疗头充电完成", shortage));
            }
            
            if (!deepSufficient) {
                int shortage = deepRequired - deepAvailable;
                suggestions.add(String.format("等待%d个下仓治疗头充电完成", shortage));
            }
            
            messageBuilder.append(String.join("或", suggestions));
        }
        
        return messageBuilder.toString();
    }
    
    /**
     * 生成推荐成功的详细消息
     */
    private String generateSuccessMessage(List<TreatmentHeadRecommendation> recommendations,
                                        List<BodyPartPatchRequest> bodyPartPatches) {
        if (recommendations.isEmpty()) {
            return "无推荐治疗头";
        }
        
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append(String.format("✅ 成功推荐%d个治疗头", recommendations.size()));
        
        // 按身体部位分组统计
        Map<String, List<TreatmentHeadRecommendation>> groupedByBodyPart = recommendations.stream()
            .collect(Collectors.groupingBy(TreatmentHeadRecommendation::getTargetBodyPart));
        
        if (groupedByBodyPart.size() > 1) {
            messageBuilder.append("，分配如下：");
            
            for (int i = 0; i < bodyPartPatches.size(); i++) {
                BodyPartPatchRequest patch = bodyPartPatches.get(i);
                List<TreatmentHeadRecommendation> recs = groupedByBodyPart.get(patch.getBodyPart());
                
                if (recs != null && !recs.isEmpty()) {
                    String colorName = recs.get(0).getLightColorName();
                    String patchTypeDesc = "SHALLOW".equalsIgnoreCase(patch.getPatchType()) ? "浅部" : "深部";
                    List<Integer> headNumbers = recs.stream()
                        .map(TreatmentHeadRecommendation::getHeadNumber)
                        .sorted()
                        .collect(Collectors.toList());
                    
                    messageBuilder.append(String.format(" %s(%s贴片)→%s指示灯(治疗头%s)",
                        patch.getBodyPart(), patchTypeDesc, colorName, headNumbers));
                    
                    if (i < bodyPartPatches.size() - 1) {
                        messageBuilder.append("；");
                    }
                }
            }
        }
        
        return messageBuilder.toString();
    }
    
    /**
     * 判断治疗头是否可用
     */
    private boolean isHeadAvailable(TreatmentHeadInfo head) {
        String status = head.getStatus();
        int batteryLevel = head.getBatteryLevel();
        
        // 充电中且电量大于60%
        if ("CHARGING".equals(status) && batteryLevel > MIN_BATTERY_LEVEL) {
            return true;
        }
        
        // 充电完成且电量为100%
        if ("CHARGED".equals(status) && batteryLevel == FULL_BATTERY_LEVEL) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断治疗头是否匹配贴片类型
     * 浅部贴片(SHALLOW)：使用上仓治疗头(1-10)
     * 深部贴片(DEEP)：使用下仓治疗头(11-20)
     */
    private boolean isHeadMatchPatchType(TreatmentHeadInfo head, String patchType) {
        if (patchType == null) {
            return true; // 如果没有指定贴片类型，所有治疗头都可用
        }
        
        switch (patchType.toUpperCase()) {
            case "SHALLOW":
                return isShallowHead(head);
            case "DEEP":
                return isDeepHead(head);
            default:
                logger.warn("Unknown patch type: {}, allowing all heads", patchType);
                return true;
        }
    }
    
    /**
     * 判断是否为浅部治疗头（基于仓位类型）
     */
    private boolean isShallowHead(TreatmentHeadInfo head) {
        return head.isShallowCompartment();
    }

    /**
     * 判断是否为深部治疗头（基于仓位类型）
     */
    private boolean isDeepHead(TreatmentHeadInfo head) {
        return head.isDeepCompartment();
    }
    
    /**
     * 生成基于身体部位的治疗头推荐列表
     */
    private List<TreatmentHeadRecommendation> generateRecommendationsForMixedPatches(
            List<TreatmentHeadInfo> shallowAvailableHeads, 
            List<TreatmentHeadInfo> deepAvailableHeads,
            TreatmentHeadAvailabilityRequest request) {
        
        List<TreatmentHeadRecommendation> recommendations = new ArrayList<>();
        
        // 获取身体部位贴片需求列表
        List<BodyPartPatchRequest> bodyPartPatches = request.getBodyPartPatches();
        if (bodyPartPatches == null || bodyPartPatches.isEmpty()) {
            // 向后兼容：如果没有新格式数据，使用旧的推荐逻辑
            return generateLegacyRecommendations(shallowAvailableHeads, deepAvailableHeads, request);
        }
        
        // 按身体部位分配治疗头
        Map<String, List<TreatmentHeadInfo>> allocation = allocateHeadsByBodyPart(
            shallowAvailableHeads, deepAvailableHeads, bodyPartPatches);
        
        // 验证分配结果
        if (!validateAllocation(allocation, bodyPartPatches)) {
            logger.error("Treatment head allocation validation failed");
            return recommendations; // 返回空列表
        }
        
        // 为每个身体部位生成推荐
        int[] lightColors = {LIGHT_COLOR_ORANGE, LIGHT_COLOR_GREEN, LIGHT_COLOR_BLUE}; // 橙绿蓝顺序
        String[] lightColorNames = {"橙色", "绿色", "蓝色"};
        int globalPriority = 1;
        
        for (int bodyPartIndex = 0; bodyPartIndex < bodyPartPatches.size(); bodyPartIndex++) {
            BodyPartPatchRequest patch = bodyPartPatches.get(bodyPartIndex);
            List<TreatmentHeadInfo> allocatedHeads = allocation.get(patch.getBodyPart());
            
            // 为该身体部位分配颜色
            int colorIndex = bodyPartIndex % lightColors.length;
            int lightColor = lightColors[colorIndex];
            String lightColorName = lightColorNames[colorIndex];
            
            // 为该部位的每个治疗头生成推荐
            for (TreatmentHeadInfo head : allocatedHeads) {
                String reason = generateRecommendationReason(head, globalPriority, 
                                                           patch.getPatchType(), patch.getBodyPart());
                String compartmentType = head.isShallowCompartment() ? "上层浅部" : "下层深部";
                
                TreatmentHeadRecommendation recommendation = new TreatmentHeadRecommendation(
                    head.getHeadNumber(),
                    head.getSlotNumber(),
                    head.getBatteryLevel(),
                    head.getUsageCount(),
                    "AVAILABLE", // 临时状态，实际应该从head获取
                    lightColor,
                    lightColorName,
                    globalPriority,
                    reason,
                    compartmentType,
                    patch.getBodyPart() // 设置目标身体部位
                );
                
                recommendations.add(recommendation);
                globalPriority++;
            }
            
            logger.debug("Generated {} recommendations for body part: {} with color: {}", 
                        allocatedHeads.size(), patch.getBodyPart(), lightColorName);
        }
        
        logger.info("Generated {} total recommendations for {} body parts", 
                   recommendations.size(), bodyPartPatches.size());
        
        return recommendations;
    }
    
    /**
     * 向后兼容的推荐生成方法
     */
    private List<TreatmentHeadRecommendation> generateLegacyRecommendations(
            List<TreatmentHeadInfo> shallowAvailableHeads, 
            List<TreatmentHeadInfo> deepAvailableHeads,
            TreatmentHeadAvailabilityRequest request) {
        
        List<TreatmentHeadRecommendation> recommendations = new ArrayList<>();
        int[] lightColors = {LIGHT_COLOR_ORANGE, LIGHT_COLOR_GREEN, LIGHT_COLOR_BLUE};
        String[] lightColorNames = {"橙色", "绿色", "蓝色"};
        int priorityCounter = 1;
        
        // 处理浅部贴片需求
        int shallowRequired = request.getShallowPatchCount();
        if (shallowRequired > 0) {
            List<TreatmentHeadInfo> sortedShallowHeads = sortHeadsByPriority(shallowAvailableHeads);
            
            for (int i = 0; i < Math.min(shallowRequired, sortedShallowHeads.size()); i++) {
                TreatmentHeadInfo head = sortedShallowHeads.get(i);
                int colorIndex = (priorityCounter - 1) % lightColors.length;
                
                TreatmentHeadRecommendation recommendation = new TreatmentHeadRecommendation(
                    head.getHeadNumber(),
                    head.getSlotNumber(),
                    head.getBatteryLevel(),
                    head.getUsageCount(),
                    "AVAILABLE",
                    lightColors[colorIndex],
                    lightColorNames[colorIndex],
                    priorityCounter,
                    generateRecommendationReason(head, priorityCounter, "SHALLOW", "未指定"),
                    "上仓(浅部)",
                    "未指定"
                );
                
                recommendations.add(recommendation);
                priorityCounter++;
            }
        }
        
        // 处理深部贴片需求
        int deepRequired = request.getDeepPatchCount();
        if (deepRequired > 0) {
            List<TreatmentHeadInfo> sortedDeepHeads = sortHeadsByPriority(deepAvailableHeads);
            
            for (int i = 0; i < Math.min(deepRequired, sortedDeepHeads.size()); i++) {
                TreatmentHeadInfo head = sortedDeepHeads.get(i);
                int colorIndex = (priorityCounter - 1) % lightColors.length;
                
                TreatmentHeadRecommendation recommendation = new TreatmentHeadRecommendation(
                    head.getHeadNumber(),
                    head.getSlotNumber(),
                    head.getBatteryLevel(),
                    head.getUsageCount(),
                    "AVAILABLE",
                    lightColors[colorIndex],
                    lightColorNames[colorIndex],
                    priorityCounter,
                    generateRecommendationReason(head, priorityCounter, "DEEP", "未指定"),
                    "下层深部",
                    "未指定"
                );
                
                recommendations.add(recommendation);
                priorityCounter++;
            }
        }
        
        return recommendations;
    }
    
    /**
     * 按优先级排序治疗头：电量高优先，使用次数少优先
     */
    private List<TreatmentHeadInfo> sortHeadsByPriority(List<TreatmentHeadInfo> heads) {
        return heads.stream()
            .sorted((h1, h2) -> {
                // 首先按电量降序
                int batteryCompare = Integer.compare(h2.getBatteryLevel(), h1.getBatteryLevel());
                if (batteryCompare != 0) {
                    return batteryCompare;
                }
                // 电量相同时按使用次数升序
                int usageCompare = Integer.compare(h1.getUsageCount(), h2.getUsageCount());
                if (usageCompare != 0) {
                    return usageCompare;
                }
                // 最后按编号升序（稳定排序）
                return Integer.compare(h1.getHeadNumber(), h2.getHeadNumber());
            })
            .collect(Collectors.toList());
    }
    
    /**
     * 按身体部位分配治疗头
     * 为每个身体部位分配对应数量和类型的治疗头
     */
    private Map<String, List<TreatmentHeadInfo>> allocateHeadsByBodyPart(
            List<TreatmentHeadInfo> shallowAvailable, 
            List<TreatmentHeadInfo> deepAvailable,
            List<BodyPartPatchRequest> bodyPartPatches) {
        
        Map<String, List<TreatmentHeadInfo>> allocation = new HashMap<>();
        
        // 排序可用治疗头
        List<TreatmentHeadInfo> sortedShallow = sortHeadsByPriority(shallowAvailable);
        List<TreatmentHeadInfo> sortedDeep = sortHeadsByPriority(deepAvailable);
        
        int shallowIndex = 0;
        int deepIndex = 0;
        
        // 为每个身体部位分配治疗头
        for (BodyPartPatchRequest patch : bodyPartPatches) {
            List<TreatmentHeadInfo> allocatedHeads = new ArrayList<>();
            
            for (int i = 0; i < patch.getPatchCount(); i++) {
                if ("SHALLOW".equalsIgnoreCase(patch.getPatchType())) {
                    if (shallowIndex < sortedShallow.size()) {
                        allocatedHeads.add(sortedShallow.get(shallowIndex++));
                    } else {
                        logger.warn("Not enough shallow heads for body part: {}", patch.getBodyPart());
                        break;
                    }
                } else if ("DEEP".equalsIgnoreCase(patch.getPatchType())) {
                    if (deepIndex < sortedDeep.size()) {
                        allocatedHeads.add(sortedDeep.get(deepIndex++));
                    } else {
                        logger.warn("Not enough deep heads for body part: {}", patch.getBodyPart());
                        break;
                    }
                }
            }
            
            allocation.put(patch.getBodyPart(), allocatedHeads);
            logger.debug("Allocated {} heads for body part: {}", allocatedHeads.size(), patch.getBodyPart());
        }
        
        return allocation;
    }
    
    /**
     * 验证分配结果的正确性
     */
    private boolean validateAllocation(Map<String, List<TreatmentHeadInfo>> allocation,
                                     List<BodyPartPatchRequest> bodyPartPatches) {
        for (BodyPartPatchRequest patch : bodyPartPatches) {
            List<TreatmentHeadInfo> allocatedHeads = allocation.get(patch.getBodyPart());
            
            if (allocatedHeads == null || allocatedHeads.size() != patch.getPatchCount()) {
                logger.error("Allocation validation failed for body part: {}, expected: {}, actual: {}", 
                           patch.getBodyPart(), patch.getPatchCount(), 
                           allocatedHeads != null ? allocatedHeads.size() : 0);
                return false;
            }
            
            // 验证分配的治疗头类型是否正确
            for (TreatmentHeadInfo head : allocatedHeads) {
                boolean typeMatch = ("SHALLOW".equalsIgnoreCase(patch.getPatchType()) && isShallowHead(head)) ||
                                  ("DEEP".equalsIgnoreCase(patch.getPatchType()) && isDeepHead(head));
                
                if (!typeMatch) {
                    logger.error("Head type mismatch for body part: {}, head: {}, expected type: {}", 
                               patch.getBodyPart(), head.getHeadNumber(), patch.getPatchType());
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 生成治疗头推荐列表（向后兼容方法）
     */
    @Deprecated
    private List<TreatmentHeadRecommendation> generateRecommendations(List<TreatmentHeadInfo> availableHeads, 
                                                                     TreatmentHeadAvailabilityRequest request) {
        
        List<TreatmentHeadInfo> sortedHeads = sortHeadsByPriority(availableHeads);
        List<TreatmentHeadRecommendation> recommendations = new ArrayList<>();
        int[] lightColors = {LIGHT_COLOR_ORANGE, LIGHT_COLOR_GREEN, LIGHT_COLOR_BLUE};
        String[] lightColorNames = {"橙色", "绿色", "蓝色"};
        
        for (int i = 0; i < Math.min(request.getRequiredCount(), sortedHeads.size()); i++) {
            TreatmentHeadInfo head = sortedHeads.get(i);
            int lightColor = lightColors[i % lightColors.length];
            String lightColorName = lightColorNames[i % lightColorNames.length];
            
            String reason = generateRecommendationReason(head, i + 1, request.getPatchType());
            String compartmentType = head.getHeadNumber() <= 10 ? "上仓(浅部)" : "下仓(深部)";
            
            TreatmentHeadRecommendation recommendation = new TreatmentHeadRecommendation(
                head.getHeadNumber(),
                head.getSlotNumber(),
                head.getBatteryLevel(),
                head.getUsageCount(),
                head.getStatus(),
                lightColor,
                lightColorName,
                i + 1,
                reason,
                compartmentType
            );
            
            recommendations.add(recommendation);
        }
        
        return recommendations;
    }
    
    /**
     * 生成推荐理由
     */
    private String generateRecommendationReason(TreatmentHeadInfo head, int priority, String patchType) {
        return generateRecommendationReason(head, priority, patchType, null);
    }
    
    /**
     * 生成推荐理由（包含身体部位信息）
     */
    private String generateRecommendationReason(TreatmentHeadInfo head, int priority, 
                                              String patchType, String bodyPart) {
        StringBuilder reason = new StringBuilder();
        reason.append("优先级").append(priority).append(": ");
        
        // 添加身体部位信息
        if (bodyPart != null && !"未指定".equals(bodyPart)) {
            reason.append("用于").append(bodyPart).append("，");
        }
        
        // 添加贴片类型信息
        if (patchType != null) {
            String patchTypeDesc = "SHALLOW".equalsIgnoreCase(patchType) ? "浅部贴片" : "深部贴片";
            String compartmentDesc = head.getHeadNumber() <= 10 ? "上仓" : "下仓";
            reason.append(patchTypeDesc).append("(").append(compartmentDesc).append("), ");
        }
        
        // 添加电量信息
        if (head.getBatteryLevel() == FULL_BATTERY_LEVEL) {
            reason.append("电量满格");
        } else {
            reason.append("电量").append(head.getBatteryLevel()).append("%");
        }
        
        // 添加使用次数信息
        reason.append(", 使用").append(head.getUsageCount()).append("次");
        
        if (head.getUsageCount() < 50) {
            reason.append("(较新)");
        } else if (head.getUsageCount() < 200) {
            reason.append("(正常)");
        } else {
            reason.append("(使用较多)");
        }
        
        return reason.toString();
    }
    
    /**
     * 生成身体部位颜色映射表
     * 用于前端显示部位选择时的颜色提示
     */
    public Map<String, String> generateBodyPartColorMapping(List<BodyPartPatchRequest> bodyPartPatches) {
        Map<String, String> colorMapping = new HashMap<>();
        String[] lightColorNames = {"橙色", "绿色", "蓝色"};
        
        for (int i = 0; i < bodyPartPatches.size(); i++) {
            String bodyPart = bodyPartPatches.get(i).getBodyPart();
            int colorIndex = i % lightColorNames.length;
            colorMapping.put(bodyPart, lightColorNames[colorIndex]);
        }
        
        return colorMapping;
    }
    
    /**
     * 验证指示灯颜色分配的正确性
     */
    private boolean validateLightColorAllocation(List<TreatmentHeadRecommendation> recommendations,
                                               List<BodyPartPatchRequest> bodyPartPatches) {
        // 检查同一身体部位的所有治疗头是否使用相同颜色
        Map<String, Integer> bodyPartColorMap = new HashMap<>();
        
        for (TreatmentHeadRecommendation rec : recommendations) {
            String bodyPart = rec.getTargetBodyPart();
            int lightColor = rec.getLightColor();
            
            if (bodyPartColorMap.containsKey(bodyPart)) {
                if (bodyPartColorMap.get(bodyPart) != lightColor) {
                    logger.error("Inconsistent light color for body part: {}, expected: {}, actual: {}", 
                               bodyPart, bodyPartColorMap.get(bodyPart), lightColor);
                    return false;
                }
            } else {
                bodyPartColorMap.put(bodyPart, lightColor);
            }
        }
        
        // 检查颜色分配是否按照部位顺序正确分配
        String[] lightColorNames = {"橙色", "绿色", "蓝色"};
        for (int i = 0; i < bodyPartPatches.size(); i++) {
            String bodyPart = bodyPartPatches.get(i).getBodyPart();
            int expectedColorIndex = i % lightColorNames.length;
            int expectedColor = expectedColorIndex + 1; // 颜色值从1开始
            
            Integer actualColor = bodyPartColorMap.get(bodyPart);
            if (actualColor == null || actualColor != expectedColor) {
                logger.error("Incorrect color assignment for body part: {}, expected: {}, actual: {}", 
                           bodyPart, expectedColor, actualColor);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 点亮推荐治疗头的指示灯
     */
    private void lightUpRecommendedHeads(List<TreatmentHeadRecommendation> recommendations) {
        try {
            if (recommendations.isEmpty()) {
                logger.info("No recommendations to light up");
                return;
            }
            
            // 验证颜色分配
            List<BodyPartPatchRequest> bodyPartPatches = getCurrentBodyPartPatches(recommendations);
            if (!validateLightColorAllocation(recommendations, bodyPartPatches)) {
                logger.warn("Light color allocation validation failed, but proceeding with lighting");
            }
            
            List<TreatmentHeadLightRequest> lightRequests = recommendations.stream()
                .map(rec -> new TreatmentHeadLightRequest(rec.getHeadNumber(), rec.getLightColor()))
                .collect(Collectors.toList());
            
            logger.info("Lighting up {} recommended treatment heads with body part colors", lightRequests.size());
            
            // 按身体部位分组记录日志
            Map<String, List<TreatmentHeadRecommendation>> groupedByBodyPart = recommendations.stream()
                .collect(Collectors.groupingBy(TreatmentHeadRecommendation::getTargetBodyPart));
            
            for (Map.Entry<String, List<TreatmentHeadRecommendation>> entry : groupedByBodyPart.entrySet()) {
                String bodyPart = entry.getKey();
                List<TreatmentHeadRecommendation> recs = entry.getValue();
                String colorName = recs.get(0).getLightColorName();
                List<Integer> headNumbers = recs.stream()
                    .map(TreatmentHeadRecommendation::getHeadNumber)
                    .collect(Collectors.toList());
                
                logger.info("Body part: {} → {} ({} heads: {})", 
                           bodyPart, colorName, headNumbers.size(), headNumbers);
            }
            
            hardwareService.setTreatmentHeadLights(lightRequests);
            
        } catch (Exception e) {
            logger.error("Failed to light up recommended treatment heads", e);
            // 不抛出异常，因为指示灯失败不应该影响推荐结果
        }
    }
    
    /**
     * 从推荐结果中提取身体部位贴片需求（用于验证）
     */
    private List<BodyPartPatchRequest> getCurrentBodyPartPatches(List<TreatmentHeadRecommendation> recommendations) {
        Map<String, BodyPartPatchRequest> bodyPartMap = new HashMap<>();
        
        for (TreatmentHeadRecommendation rec : recommendations) {
            String bodyPart = rec.getTargetBodyPart();
            if (!bodyPartMap.containsKey(bodyPart)) {
                // 根据治疗头类型推断贴片类型
                String patchType = isShallowHead(createTreatmentHeadInfo(rec)) ? "SHALLOW" : "DEEP";
                bodyPartMap.put(bodyPart, new BodyPartPatchRequest(bodyPart, patchType, 0));
            }
            // 增加该部位的贴片数量
            BodyPartPatchRequest patch = bodyPartMap.get(bodyPart);
            patch.setPatchCount(patch.getPatchCount() + 1);
        }
        
        return new ArrayList<>(bodyPartMap.values());
    }
    
    /**
     * 从推荐结果创建TreatmentHeadInfo（用于类型判断）
     */
    private TreatmentHeadInfo createTreatmentHeadInfo(TreatmentHeadRecommendation rec) {
        TreatmentHeadInfo head = new TreatmentHeadInfo();
        head.setHeadNumber(rec.getHeadNumber());
        head.setSlotNumber(rec.getSlotNumber());
        head.setBatteryLevel(rec.getBatteryLevel());
        head.setUsageCount(rec.getUsageCount());
        return head;
    }

    
    /**
     * 关闭指定治疗头的指示灯
     */
    public void turnOffSpecificLights(List<Integer> headNumbers) {
        if (headNumbers == null || headNumbers.isEmpty()) {
            return;
        }
        
        try {
            logger.info("Turning off lights for {} treatment heads: {}", headNumbers.size(), headNumbers);
            
            hardwareService.turnOffTreatmentHeadLights(headNumbers);
            
            logger.info("Completed turning off lights for specified treatment heads");
            
        } catch (Exception e) {
            logger.error("Failed to turn off specific treatment head lights", e);
        }
    }
    
    /**
     * 验证请求参数
     */
    private void validateRequest(TreatmentHeadAvailabilityRequest request) throws InvalidRequestException {
        List<String> errors = new ArrayList<>();
        
        if (request == null) {
            throw new InvalidRequestException("请求对象不能为空");
        }
        
        // 验证治疗模式
        if (request.getTreatmentMode() == null || request.getTreatmentMode().trim().isEmpty()) {
            errors.add("治疗模式不能为空");
        } else if (!"ON_SITE".equalsIgnoreCase(request.getTreatmentMode()) && 
                   !"TAKE_AWAY".equalsIgnoreCase(request.getTreatmentMode())) {
            errors.add("治疗模式必须为 ON_SITE 或 TAKE_AWAY");
        }
        
        // 验证贴片需求
        if (request.getBodyPartPatches() != null && !request.getBodyPartPatches().isEmpty()) {
            for (int i = 0; i < request.getBodyPartPatches().size(); i++) {
                BodyPartPatchRequest patch = request.getBodyPartPatches().get(i);
                validatePatchRequest(patch, i + 1, errors);
            }
        } else {
            // 检查是否使用旧格式
            if (request.getRequiredCount() <= 0) {
                errors.add("贴片需求列表不能为空，或者需要设置有效的需求数量");
            }
        }
        
        if (!errors.isEmpty()) {
            throw new InvalidRequestException(errors);
        }
    }
    
    /**
     * 验证单个贴片需求
     */
    private void validatePatchRequest(BodyPartPatchRequest patch, int index, List<String> errors) {
        if (patch == null) {
            errors.add(String.format("第%d个贴片需求不能为空", index));
            return;
        }
        
        if (patch.getBodyPart() == null || patch.getBodyPart().trim().isEmpty()) {
            errors.add(String.format("第%d个贴片需求的身体部位不能为空", index));
        }
        
        if (patch.getPatchType() == null || 
            (!"SHALLOW".equalsIgnoreCase(patch.getPatchType()) && !"DEEP".equalsIgnoreCase(patch.getPatchType()))) {
            errors.add(String.format("第%d个贴片需求的贴片类型必须为 SHALLOW 或 DEEP", index));
        }
        
        if (patch.getPatchCount() < 1 || patch.getPatchCount() > 4) {
            errors.add(String.format("第%d个贴片需求的贴片数量必须在1-4之间", index));
        }
    }
    
    /**
     * 创建错误响应
     */
    private TreatmentHeadAvailabilityResponse createErrorResponse(InvalidRequestException e) {
        return new TreatmentHeadAvailabilityResponse(
            false, 0, 0, new ArrayList<>(), new ArrayList<>(), 
            e.getUserMessage(), null
        );
    }
    
    /**
     * 创建无治疗头可用的响应
     */
    private TreatmentHeadAvailabilityResponse createNoHeadsAvailableResponse(TreatmentHeadAvailabilityRequest request) {
        AvailabilityDetail detail = new AvailabilityDetail(
            0, request.getShallowPatchCount(), false,
            0, request.getDeepPatchCount(), false,
            "❌ 无可用治疗头，请检查硬件连接或等待治疗头充电完成"
        );
        
        return new TreatmentHeadAvailabilityResponse(
            false, 0, request.calculateTotalRequiredCount(), 
            new ArrayList<>(), new ArrayList<>(), 
            detail.getDetailedMessage(), detail
        );
    }
    
    /**
     * 创建硬件通信错误响应
     */
    private TreatmentHeadAvailabilityResponse createHardwareCommunicationErrorResponse(SerialCommunicationException e) {
        String userMessage = "硬件通信失败，请检查设备连接后重试";
        String detailedMessage = "❌ 硬件通信异常：" + e.getMessage() + "。请检查串口连接和设备状态。";
        
        return new TreatmentHeadAvailabilityResponse(
            false, 0, 0, new ArrayList<>(), new ArrayList<>(), 
            detailedMessage, null
        );
    }
    
    /**
     * 创建意外错误响应
     */
    private TreatmentHeadAvailabilityResponse createUnexpectedErrorResponse(Exception e) {
        String userMessage = "系统发生意外错误，请稍后重试";
        String detailedMessage = "❌ 系统异常：" + e.getMessage() + "。请联系技术支持。";
        
        return new TreatmentHeadAvailabilityResponse(
            false, 0, 0, new ArrayList<>(), new ArrayList<>(), 
            detailedMessage, null
        );
    }
    
    /**
     * 创建筛选错误响应
     */
    private TreatmentHeadAvailabilityResponse createFilteringErrorResponse(Exception e) {
        String detailedMessage = "❌ 治疗头筛选过程发生错误：" + e.getMessage() + "。请重试或联系技术支持。";
        
        return new TreatmentHeadAvailabilityResponse(
            false, 0, 0, new ArrayList<>(), new ArrayList<>(), 
            detailedMessage, null
        );
    }
    
    /**
     * 创建推荐生成错误响应
     */
    private TreatmentHeadAvailabilityResponse createRecommendationGenerationErrorResponse(Exception e, AvailabilityDetail detail) {
        String detailedMessage = "❌ 推荐生成过程发生错误：" + e.getMessage() + "。虽然治疗头数量充足，但无法生成推荐。";
        
        return new TreatmentHeadAvailabilityResponse(
            false, detail.getTotalAvailable(), detail.getTotalRequired(), 
            new ArrayList<>(), new ArrayList<>(), 
            detailedMessage, detail
        );
    }
    
    /**
     * 重试指示灯控制（如果HardwareService调用失败）
     */
    public void retryLightControl(List<TreatmentHeadRecommendation> recommendations, int maxRetries) {
        if (recommendations == null || recommendations.isEmpty() || maxRetries <= 0) {
            return;
        }
        
        logger.info("Retrying light control for {} recommendations, max retries: {}", 
                   recommendations.size(), maxRetries);
        
        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                lightUpRecommendedHeads(recommendations);
                logger.info("Light control retry {} successful", retry + 1);
                return;
                
            } catch (Exception e) {
                logger.warn("Light control retry {} failed: {}", retry + 1, e.getMessage());
                
                if (retry < maxRetries - 1) {
                    try {
                        Thread.sleep(1000 * (retry + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        logger.error("Light control failed after {} retries", maxRetries);
    }
    
    /**
     * 记录详细的响应信息
     */
    private void logResponseDetails(TreatmentHeadAvailabilityResponse response, 
                                   TreatmentHeadAvailabilityRequest request) {
        logger.info("=== Treatment Head Recommendation Response ===");
        logger.info("Request Mode: {}", request.getTreatmentMode());
        logger.info("Overall Sufficient: {}", response.isSufficient());
        logger.info("Total Available: {}, Total Required: {}", response.getAvailableCount(), response.getRequiredCount());
        
        if (request.getBodyPartPatches() != null) {
            logger.info("Body Part Requirements:");
            for (BodyPartPatchRequest patch : request.getBodyPartPatches()) {
                logger.info("  - {}: {} x {} patches", 
                           patch.getBodyPart(), patch.getPatchCount(), patch.getPatchType());
            }
        }
        
        if (response.getAvailabilityDetail() != null) {
            AvailabilityDetail detail = response.getAvailabilityDetail();
            logger.info("Availability Detail: Shallow {}/{}, Deep {}/{}", 
                       detail.getShallowAvailable(), detail.getShallowRequired(),
                       detail.getDeepAvailable(), detail.getDeepRequired());
        }
        
        if (response.isSufficient() && !response.getRecommendations().isEmpty()) {
            logger.info("Recommendations Generated:");
            Map<String, List<TreatmentHeadRecommendation>> groupedRecs = response.getRecommendationsByBodyPart();
            for (Map.Entry<String, List<TreatmentHeadRecommendation>> entry : groupedRecs.entrySet()) {
                String bodyPart = entry.getKey();
                List<TreatmentHeadRecommendation> recs = entry.getValue();
                String colorName = recs.get(0).getLightColorName();
                List<Integer> headNumbers = recs.stream()
                    .map(TreatmentHeadRecommendation::getHeadNumber)
                    .sorted()
                    .collect(Collectors.toList());
                
                logger.info("  - {}: {} ({} heads: {})", bodyPart, colorName, headNumbers.size(), headNumbers);
            }
        }
        
        logger.info("Response Message: {}", response.getMessage());
        logger.info("===============================================");
    }
    
    /**
     * 生成推荐统计信息
     */
    public Map<String, Object> generateRecommendationStatistics(TreatmentHeadAvailabilityResponse response) {
        Map<String, Object> statistics = new HashMap<>();
        
        statistics.put("totalRecommendations", response.getRecommendations().size());
        statistics.put("sufficient", response.isSufficient());
        statistics.put("availableCount", response.getAvailableCount());
        statistics.put("requiredCount", response.getRequiredCount());
        
        if (response.getAvailabilityDetail() != null) {
            AvailabilityDetail detail = response.getAvailabilityDetail();
            statistics.put("shallowAvailable", detail.getShallowAvailable());
            statistics.put("shallowRequired", detail.getShallowRequired());
            statistics.put("shallowSufficient", detail.isShallowSufficient());
            statistics.put("deepAvailable", detail.getDeepAvailable());
            statistics.put("deepRequired", detail.getDeepRequired());
            statistics.put("deepSufficient", detail.isDeepSufficient());
        }
        
        // 按身体部位统计
        Map<String, List<TreatmentHeadRecommendation>> groupedRecs = response.getRecommendationsByBodyPart();
        Map<String, Object> bodyPartStats = new HashMap<>();
        for (Map.Entry<String, List<TreatmentHeadRecommendation>> entry : groupedRecs.entrySet()) {
            String bodyPart = entry.getKey();
            List<TreatmentHeadRecommendation> recs = entry.getValue();
            
            Map<String, Object> partStats = new HashMap<>();
            partStats.put("count", recs.size());
            partStats.put("color", recs.get(0).getLightColorName());
            partStats.put("headNumbers", recs.stream()
                .map(TreatmentHeadRecommendation::getHeadNumber)
                .sorted()
                .collect(Collectors.toList()));
            
            bodyPartStats.put(bodyPart, partStats);
        }
        statistics.put("bodyPartStatistics", bodyPartStats);
        
        return statistics;
    }
    
    /**
     * 为推荐的治疗头发送治疗参数
     */
    public boolean sendTreatmentParametersToRecommended(List<TreatmentHeadRecommendation> recommendations,
                                                       TreatmentParamsRequest treatmentParams,
                                                       String treatmentMode) throws SerialCommunicationException {
        
        logger.info("Sending treatment parameters to {} recommended heads, mode: {}", 
                   recommendations.size(), treatmentMode);
        
        try {
            // 发送治疗参数到硬件
            boolean success = hardwareService.sendTreatmentParams(treatmentParams);
            
            if (success) {
                if ("ON_SITE".equals(treatmentMode)) {
                    // 现场治疗模式：发送参数后立即开始治疗
                    for (TreatmentHeadRecommendation rec : recommendations) {
                        hardwareService.startTreatment(
                            rec.getHeadNumber(),
                            treatmentParams.getDurationMinutes(),
                            treatmentParams.getIntensity(),
                            treatmentParams.getFrequency()
                        );
                    }
                    logger.info("Started treatment on {} heads for ON_SITE mode", recommendations.size());
                    
                } else if ("TAKE_AWAY".equals(treatmentMode)) {
                    // 取走治疗模式：只发送参数，不立即开始治疗
                    logger.info("Treatment parameters sent to {} heads for TAKE_AWAY mode", recommendations.size());
                }
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("Failed to send treatment parameters to recommended heads", e);
            throw new SerialCommunicationException("Failed to send treatment parameters", e);
        }
    }
    

    
    /**
     * 处理向后兼容性
     */
    private void handleBackwardCompatibility(TreatmentHeadAvailabilityRequest request) {
        if (request.isLegacyFormat()) {
            logger.info("Detected legacy format request: {}", request.getCompatibilityInfo());
            
            // 自动转换旧格式到新格式
            request.convertLegacyToNewFormat();
            
            logger.info("Legacy format converted successfully. New format: {}", 
                       request.getEffectiveBodyPartPatches());
        }
    }
    
    /**
     * 创建向后兼容的响应
     * 确保旧版本客户端能正确解析响应
     */
    private TreatmentHeadAvailabilityResponse createBackwardCompatibleResponse(
            boolean sufficient, int availableCount, int requiredCount,
            List<TreatmentHeadInfo> availableHeads, List<TreatmentHeadRecommendation> recommendations,
            String message, AvailabilityDetail detail) {
        
        TreatmentHeadAvailabilityResponse response = new TreatmentHeadAvailabilityResponse(
            sufficient, availableCount, requiredCount, availableHeads, recommendations, message, detail);
        
        // 为向后兼容性添加额外的日志信息
        logger.debug("Created backward compatible response: sufficient={}, available={}, required={}, recommendations={}", 
                    sufficient, availableCount, requiredCount, recommendations.size());
        
        return response;
    }
    
    /**
     * 检查请求是否需要向后兼容性处理
     */
    private boolean needsBackwardCompatibilityHandling(TreatmentHeadAvailabilityRequest request) {
        return request.isLegacyFormat();
    }
    
    /**
     * 记录兼容性处理日志
     */
    private void logCompatibilityHandling(TreatmentHeadAvailabilityRequest request) {
        if (request.isLegacyFormat()) {
            logger.info("Backward compatibility applied: {}", request.getCompatibilityInfo());
        }
    }
    
    /**
     * 为旧版本API提供的兼容方法
     * @deprecated 使用 checkAvailabilityAndRecommend(TreatmentHeadAvailabilityRequest) 替代
     */
    @Deprecated
    public TreatmentHeadAvailabilityResponse checkAvailability(int requiredCount, String treatmentMode, 
                                                              List<String> bodyParts, String patchType) 
            throws SerialCommunicationException {
        logger.warn("Using deprecated checkAvailability method. Please migrate to new API format.");
        
        // 创建旧格式请求对象
        TreatmentHeadAvailabilityRequest legacyRequest = new TreatmentHeadAvailabilityRequest(
            requiredCount, treatmentMode, bodyParts, patchType);
        
        // 调用新的方法
        return checkAvailabilityAndRecommend(legacyRequest);
    }
    
    /**
     * 为旧版本API提供的简化兼容方法
     * @deprecated 使用 checkAvailabilityAndRecommend(TreatmentHeadAvailabilityRequest) 替代
     */
    @Deprecated
    public TreatmentHeadAvailabilityResponse checkAvailability(int requiredCount, String treatmentMode) 
            throws SerialCommunicationException {
        logger.warn("Using deprecated checkAvailability method. Please migrate to new API format.");
        
        // 创建旧格式请求对象
        TreatmentHeadAvailabilityRequest legacyRequest = new TreatmentHeadAvailabilityRequest(
            requiredCount, treatmentMode, null);
        
        // 调用新的方法
        return checkAvailabilityAndRecommend(legacyRequest);
    }
    
    /**
     * 根据治疗参数生成推荐治疗头配置
     * 用于参数设置页面
     */
    public List<TreatmentHeadRecommendation> generateRecommendations(TreatmentParametersCheckRequest request) 
            throws SerialCommunicationException {
        
        logger.info("Generating treatment head recommendations for {} body parts", 
                   request.getBodyParts().size());
        
        List<TreatmentHeadRecommendation> recommendations = new ArrayList<>();
        
        // 获取所有可用的治疗头（包括数据库中的所有治疗头）
        List<TreatmentHeadInfo> allHeads = hardwareService.getAllTreatmentHeadsForManagement();
        
        // 🚨 医疗安全修复：过滤掉正在治疗中的治疗头
        List<TreatmentHeadInfo> availableHeads = filterOutTreatingHeads(allHeads);
        
        // 分别获取浅层和深层的可用治疗头
        List<TreatmentHeadInfo> shallowHeads = availableHeads.stream()
            .filter(head -> "上仓(浅部)".equals(head.getCompartmentType()) 
                        && head.getBatteryLevel() >= 20) // 至少20%电量
            .sorted((h1, h2) -> Integer.compare(h2.getBatteryLevel(), h1.getBatteryLevel())) // 按电量降序
            .collect(Collectors.toList());
            
        List<TreatmentHeadInfo> deepHeads = availableHeads.stream()
            .filter(head -> "下仓(深部)".equals(head.getCompartmentType()) 
                        && head.getBatteryLevel() >= 20) // 至少20%电量
            .sorted((h1, h2) -> Integer.compare(h2.getBatteryLevel(), h1.getBatteryLevel())) // 按电量降序
            .collect(Collectors.toList());
        
        // 颜色分配：按选择顺序分配橙色、绿色、蓝色
        String[] colors = {"orange", "green", "blue"};
        int[] lightColors = {LIGHT_COLOR_ORANGE, LIGHT_COLOR_GREEN, LIGHT_COLOR_BLUE};
        
        int shallowUsed = 0;
        int deepUsed = 0;
        
        // 为每个治疗部位分配治疗头
        for (int partIndex = 0; partIndex < request.getBodyParts().size(); partIndex++) {
            TreatmentBodyPart bodyPart = request.getBodyParts().get(partIndex);
            String color = colors[partIndex % colors.length];
            int lightColor = lightColors[partIndex % lightColors.length];
            
            int count = bodyPart.getParameters().getCount();
            boolean isDeep = "深部".equals(bodyPart.getParameters().getDepth());
            
            // 解析治疗参数
            int durationMinutes = parseDuration(bodyPart.getParameters().getTime());
            int intensity = parseIntensity(bodyPart.getParameters().getIntensity());
            int frequency = parseFrequency(bodyPart.getParameters().getFrequency());
            
            // 分配治疗头
            for (int i = 0; i < count; i++) {
                TreatmentHeadInfo selectedHead = null;
                
                if (isDeep) {
                    if (deepUsed < deepHeads.size()) {
                        selectedHead = deepHeads.get(deepUsed);
                        deepUsed++;
                    }
                } else {
                    if (shallowUsed < shallowHeads.size()) {
                        selectedHead = shallowHeads.get(shallowUsed);
                        shallowUsed++;
                    }
                }
                
                if (selectedHead != null) {
                    TreatmentHeadRecommendation recommendation = new TreatmentHeadRecommendation();
                    recommendation.setHeadNumber(selectedHead.getHeadNumber());
                    recommendation.setSlotNumber(selectedHead.getSlotNumber());
                    recommendation.setTargetBodyPart(bodyPart.getName());
                    recommendation.setLightColor(lightColor);
                    recommendation.setLightColorName(color);
                    recommendation.setDurationMinutes(durationMinutes);
                    recommendation.setIntensity(intensity);
                    recommendation.setFrequency(frequency);
                    recommendation.setRecommendationReason("基于电量和可用性自动推荐");
                    recommendation.setPriority(1);
                    recommendation.setCompartmentType(isDeep ? "下仓(深部)" : "上仓(浅部)");
                    
                    recommendations.add(recommendation);
                    
                    logger.debug("Assigned head {} (slot {}) to {} with color {}", 
                               selectedHead.getHeadNumber(), selectedHead.getSlotNumber(),
                               bodyPart.getName(), color);
                }
            }
        }
        
        logger.info("Generated {} treatment head recommendations", recommendations.size());
        return recommendations;
    }
    
    /**
     * 解析时间字符串，如 "15分钟" -> 15
     */
    private int parseDuration(String timeStr) {
        if (timeStr == null) return 15;
        try {
            return Integer.parseInt(timeStr.replaceAll("[^0-9]", ""));
        } catch (NumberFormatException e) {
            logger.warn("Failed to parse duration: {}, using default 15", timeStr);
            return 15;
        }
    }
    
    /**
     * 解析强度字符串，如 "30mW/cm²" -> 30
     */
    private int parseIntensity(String intensityStr) {
        if (intensityStr == null) return 30;
        try {
            return Integer.parseInt(intensityStr.replaceAll("[^0-9]", ""));
        } catch (NumberFormatException e) {
            logger.warn("Failed to parse intensity: {}, using default 30", intensityStr);
            return 30;
        }
    }
    
    /**
     * 解析频率字符串，如 "1000Hz" -> 1000
     */
    private int parseFrequency(String frequencyStr) {
        if (frequencyStr == null) return 1000;
        try {
            return Integer.parseInt(frequencyStr.replaceAll("[^0-9]", ""));
        } catch (NumberFormatException e) {
            logger.warn("Failed to parse frequency: {}, using default 1000", frequencyStr);
            return 1000;
        }
    }

    /**
     * 过滤掉正在治疗中的治疗头
     */
    private List<TreatmentHeadInfo> filterOutTreatingHeads(List<TreatmentHeadInfo> allHeads) {
        List<TreatmentHeadInfo> availableHeads = new ArrayList<>();
        for (TreatmentHeadInfo head : allHeads) {
            // 假设治疗头有一个状态字段，例如 "CHARGING", "CHARGED", "TREATING"
            // 如果治疗头正在治疗中，则不推荐
            if (!"TREATING".equals(head.getStatus())) {
                availableHeads.add(head);
            } else {
                logger.warn("Skipping head {} (slot {}) as it is currently treating.", 
                           head.getHeadNumber(), head.getSlotNumber());
            }
        }
        return availableHeads;
    }
}