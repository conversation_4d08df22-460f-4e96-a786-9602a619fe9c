package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.EnhancedCandidateItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据缺失测试服务
 * 用于测试和验证数据缺失处理逻辑的正确性
 */
@Service
@Slf4j
public class DataMissingTestService {
    
    @Autowired
    private DataMissingHandlerService dataMissingHandlerService;
    
    /**
     * 创建测试用的候选患者数据，包含各种数据缺失情况
     */
    public List<EnhancedCandidateItem> createTestCandidatesWithMissingData() {
        List<EnhancedCandidateItem> testCandidates = new ArrayList<>();
        
        // 1. 完整数据的患者
        EnhancedCandidateItem complete = new EnhancedCandidateItem();
        complete.setPatientId(1L);
        complete.setCardId("P2025010001");
        complete.setName("张三");
        complete.setAge("45岁");
        complete.setGender("男");
        complete.setVisitTime("2025.01.15");
        complete.setTreatmentParts("肩颈部, 腰背部");
        complete.setTotalSessions(5);
        testCandidates.add(complete);
        
        // 2. 没有档案记录的患者
        EnhancedCandidateItem noRecords = new EnhancedCandidateItem();
        noRecords.setPatientId(2L);
        noRecords.setCardId("P2025010002");
        noRecords.setName("李四");
        noRecords.setAge("38岁");
        noRecords.setGender("女");
        // 模拟没有档案记录的情况
        dataMissingHandlerService.handleNoRecords(noRecords);
        testCandidates.add(noRecords);
        
        // 3. 有档案但没有治疗记录的患者
        EnhancedCandidateItem noTreatments = new EnhancedCandidateItem();
        noTreatments.setPatientId(3L);
        noTreatments.setCardId("P2025010003");
        noTreatments.setName("王五");
        noTreatments.setAge("52岁");
        noTreatments.setGender("男");
        noTreatments.setVisitTime("2025.01.10");
        // 模拟有档案但没有治疗记录的情况
        dataMissingHandlerService.handleNoBodyPartStats(noTreatments);
        testCandidates.add(noTreatments);
        
        // 4. 基本信息缺失的患者
        EnhancedCandidateItem missingBasic = new EnhancedCandidateItem();
        missingBasic.setPatientId(4L);
        missingBasic.setCardId(""); // 空卡号
        missingBasic.setName(""); // 空姓名
        missingBasic.setAge(""); // 空年龄
        missingBasic.setGender(""); // 空性别
        missingBasic.setVisitTime("2025.01.08");
        missingBasic.setTreatmentParts("髋部");
        missingBasic.setTotalSessions(2);
        // 处理基本信息缺失
        dataMissingHandlerService.handleMissingBasicInfo(missingBasic);
        testCandidates.add(missingBasic);
        
        // 5. 数据异常的患者
        EnhancedCandidateItem dataError = new EnhancedCandidateItem();
        dataError.setPatientId(5L);
        dataError.setCardId("P2025010005");
        dataError.setName("赵六");
        dataError.setAge("29岁");
        dataError.setGender("女");
        // 模拟数据异常情况
        dataMissingHandlerService.handleDataException(dataError, 
                new RuntimeException("模拟数据库连接异常"));
        testCandidates.add(dataError);
        
        return testCandidates;
    }
    
    /**
     * 测试数据缺失处理逻辑
     */
    public Map<String, Object> testMissingDataHandling() {
        log.info("开始测试数据缺失处理逻辑");
        
        // 创建测试数据
        List<EnhancedCandidateItem> testCandidates = createTestCandidatesWithMissingData();
        
        // 批量处理数据缺失情况
        List<EnhancedCandidateItem> processedCandidates = 
                dataMissingHandlerService.handleMissingDataBatch(testCandidates);
        
        // 生成测试报告
        Map<String, Object> missingDataReport = 
                dataMissingHandlerService.generateMissingDataReport(processedCandidates);
        
        // 分析每个测试用例的处理结果
        List<Map<String, Object>> testResults = new ArrayList<>();
        for (EnhancedCandidateItem candidate : processedCandidates) {
            Map<String, Object> result = Map.of(
                "patientId", candidate.getPatientId(),
                "name", candidate.getName(),
                "isComplete", dataMissingHandlerService.isDataComplete(candidate),
                "completenessLevel", dataMissingHandlerService.getDataCompletenessLevel(candidate),
                "missingDataHint", dataMissingHandlerService.createMissingDataHint(candidate),
                "hasRecords", candidate.hasRecords(),
                "hasTreatments", candidate.hasTreatments()
            );
            testResults.add(result);
        }
        
        return Map.of(
            "testSummary", Map.of(
                "totalTestCases", testCandidates.size(),
                "processedSuccessfully", processedCandidates.size(),
                "testPassed", processedCandidates.size() == testCandidates.size()
            ),
            "missingDataReport", missingDataReport,
            "testResults", testResults
        );
    }
    
    /**
     * 验证数据缺失处理的正确性
     */
    public boolean validateMissingDataHandling() {
        try {
            List<EnhancedCandidateItem> testCandidates = createTestCandidatesWithMissingData();
            
            // 验证每个测试用例
            for (EnhancedCandidateItem candidate : testCandidates) {
                // 基本验证：确保没有null值
                if (candidate.getName() == null || 
                    candidate.getCardId() == null ||
                    candidate.getAge() == null ||
                    candidate.getGender() == null ||
                    candidate.getVisitTime() == null ||
                    candidate.getTreatmentParts() == null ||
                    candidate.getTotalSessions() == null) {
                    
                    log.error("验证失败：患者{}存在null值", candidate.getPatientId());
                    return false;
                }
                
                // 验证默认值设置是否正确
                if (candidate.getTotalSessions() < 0) {
                    log.error("验证失败：患者{}治疗次数为负数", candidate.getPatientId());
                    return false;
                }
            }
            
            log.info("数据缺失处理验证通过");
            return true;
            
        } catch (Exception e) {
            log.error("验证数据缺失处理时发生异常", e);
            return false;
        }
    }
}