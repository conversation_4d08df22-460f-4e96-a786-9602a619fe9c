package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.TreatmentDetail;
import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.repository.TreatmentDetailRepository;
import com.Bone.BoneSys.repository.ProcessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 紧急修复控制器
 * 用于手动修复治疗状态问题
 */
@RestController
@RequestMapping("/api/emergency-fix")
@CrossOrigin(origins = "*")
public class EmergencyFixController {
    
    private static final Logger logger = LoggerFactory.getLogger(EmergencyFixController.class);
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private ProcessRepository processRepository;
    
    /**
     * 手动修复过期的治疗状态
     * POST /api/emergency-fix/expired-treatments
     */
    @PostMapping("/expired-treatments")
    public ApiResponse<Map<String, Object>> fixExpiredTreatments() {
        try {
            logger.info("开始手动修复过期的治疗状态...");
            
            Map<String, Object> result = new HashMap<>();
            int updatedCount = 0;
            
            // 获取所有正在治疗中的详情
            List<TreatmentDetail> treatingDetails = treatmentDetailRepository
                .findByStatus(TreatmentDetailStatus.TREATING);
            
            LocalDateTime now = LocalDateTime.now();
            
            for (TreatmentDetail detail : treatingDetails) {
                Process process = detail.getProcess();
                if (process == null || process.getStartTime() == null) {
                    continue;
                }
                
                // 计算已经过的时间（分钟）
                long elapsedMinutes = ChronoUnit.MINUTES.between(process.getStartTime(), now);
                int treatmentDurationMinutes = detail.getDuration();
                
                boolean shouldUpdate = false;
                TreatmentDetailStatus newStatus = null;
                
                if (process.getTreatmentMode() == TreatmentMode.ON_SITE) {
                    // 本地治疗：治疗时间结束后变成已完成状态
                    if (elapsedMinutes >= treatmentDurationMinutes) {
                        shouldUpdate = true;
                        newStatus = TreatmentDetailStatus.COMPLETED;
                    }
                } else if (process.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
                    // 远端治疗：治疗时间+15分钟提醒时间后变成待取回状态
                    if (elapsedMinutes >= (treatmentDurationMinutes + 15)) {
                        shouldUpdate = true;
                        newStatus = TreatmentDetailStatus.AWAITING_RETURN;
                    }
                }
                
                if (shouldUpdate && newStatus != null) {
                    detail.setStatus(newStatus);
                    treatmentDetailRepository.save(detail);
                    updatedCount++;
                    
                    logger.info("治疗详情 {} 状态已更新：{} -> {}, 经过时间：{}分钟", 
                               detail.getId(), TreatmentDetailStatus.TREATING, newStatus, elapsedMinutes);
                }
            }
            
            result.put("updated_count", updatedCount);
            result.put("total_checked", treatingDetails.size());
            result.put("fix_time", now.toString());
            
            logger.info("治疗状态修复完成，共更新 {} 个详情", updatedCount);
            return ApiResponse.success("治疗状态修复完成", result);
            
        } catch (Exception e) {
            logger.error("修复过期治疗状态失败", e);
            return ApiResponse.serverError("修复失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查当前治疗状态
     * GET /api/emergency-fix/check-status
     */
    @GetMapping("/check-status")
    public ApiResponse<Map<String, Object>> checkTreatmentStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            LocalDateTime now = LocalDateTime.now();
            
            // 获取所有正在治疗中的详情
            List<TreatmentDetail> treatingDetails = treatmentDetailRepository
                .findByStatus(TreatmentDetailStatus.TREATING);
            
            int expiredCount = 0;
            int nearExpiryCount = 0;
            
            for (TreatmentDetail detail : treatingDetails) {
                Process process = detail.getProcess();
                if (process != null && process.getStartTime() != null) {
                    long elapsedMinutes = ChronoUnit.MINUTES.between(process.getStartTime(), now);
                    int treatmentDurationMinutes = detail.getDuration();
                    
                    if (process.getTreatmentMode() == TreatmentMode.ON_SITE) {
                        if (elapsedMinutes >= treatmentDurationMinutes) {
                            expiredCount++;
                        } else if (elapsedMinutes >= treatmentDurationMinutes - 2) {
                            nearExpiryCount++;
                        }
                    } else if (process.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
                        if (elapsedMinutes >= (treatmentDurationMinutes + 15)) {
                            expiredCount++;
                        } else if (elapsedMinutes >= (treatmentDurationMinutes + 13)) {
                            nearExpiryCount++;
                        }
                    }
                }
            }
            
            status.put("total_treating", treatingDetails.size());
            status.put("expired_count", expiredCount);
            status.put("near_expiry_count", nearExpiryCount);
            status.put("check_time", now.toString());
            
            return ApiResponse.success("状态检查完成", status);
            
        } catch (Exception e) {
            logger.error("检查治疗状态失败", e);
            return ApiResponse.serverError("检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除重复的治疗详情
     * POST /api/emergency-fix/remove-duplicates
     */
    @PostMapping("/remove-duplicates")
    public ApiResponse<Map<String, Object>> removeDuplicateTreatmentDetails() {
        try {
            logger.info("开始删除重复的治疗详情...");
            
            Map<String, Object> result = new HashMap<>();
            int deletedCount = 0;
            
            // 获取所有进行中的进程
            List<Process> inProgressProcesses = processRepository.findByStatus(ProcessStatus.IN_PROGRESS);
            
            for (Process process : inProgressProcesses) {
                List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(process.getId());
                
                // 按身体部位分组，删除重复项（保留ID最小的）
                Map<String, TreatmentDetail> uniqueDetails = new HashMap<>();
                
                for (TreatmentDetail detail : details) {
                    String key = detail.getBodyPart() + "_" + detail.getHeadNumberUsed();
                    
                    if (!uniqueDetails.containsKey(key)) {
                        uniqueDetails.put(key, detail);
                    } else {
                        // 如果已存在，保留ID较小的，删除ID较大的
                        TreatmentDetail existing = uniqueDetails.get(key);
                        if (detail.getId() < existing.getId()) {
                            treatmentDetailRepository.delete(existing);
                            uniqueDetails.put(key, detail);
                            deletedCount++;
                            logger.info("删除重复治疗详情：ID={}, 进程={}, 部位={}", 
                                       existing.getId(), process.getId(), detail.getBodyPart());
                        } else {
                            treatmentDetailRepository.delete(detail);
                            deletedCount++;
                            logger.info("删除重复治疗详情：ID={}, 进程={}, 部位={}", 
                                       detail.getId(), process.getId(), detail.getBodyPart());
                        }
                    }
                }
            }
            
            result.put("deleted_count", deletedCount);
            result.put("processes_checked", inProgressProcesses.size());
            
            logger.info("重复治疗详情删除完成，共删除 {} 个重复项", deletedCount);
            return ApiResponse.success("重复项删除完成", result);
            
        } catch (Exception e) {
            logger.error("删除重复治疗详情失败", e);
            return ApiResponse.serverError("删除失败: " + e.getMessage());
        }
    }
}
