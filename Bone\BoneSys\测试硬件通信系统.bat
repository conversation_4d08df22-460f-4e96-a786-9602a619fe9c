@echo off
chcp 65001 > nul
echo ========================================
echo 硬件通信系统测试脚本
echo ========================================
echo.

echo [1/5] 测试治疗头归还检测API...
echo 请求: POST /api/treatment-process/detect-returns
curl -s -X POST http://localhost:8080/api/treatment-process/detect-returns ^
  -H "Content-Type: application/json" | jq .
echo.

echo [2/5] 测试硬件通信状态...
echo 请求: GET /api/treatment-process/hardware-status
curl -s http://localhost:8080/api/treatment-process/hardware-status | jq .
echo.

echo [3/5] 测试治疗头数据同步...
echo 请求: GET /api/hardware/heads
curl -s "http://localhost:8080/api/hardware/heads?page=1&size=10" | jq .
echo.

echo [4/5] 测试获取进程列表...
echo 请求: GET /api/processes
curl -s "http://localhost:8080/api/processes?page=1&size=5" | jq .
echo.

echo [5/5] 测试治疗进程详情...
echo 请输入要测试的进程ID（如果有的话）:
set /p processId=
if not "%processId%"=="" (
    echo 请求: GET /api/treatment-process/%processId%
    curl -s http://localhost:8080/api/treatment-process/%processId% | jq .
    echo.
    
    echo 请求: GET /api/processes/%processId%/realtime
    curl -s http://localhost:8080/api/processes/%processId%/realtime | jq .
    echo.
) else (
    echo 跳过进程详情测试
    echo.
)

echo [6/8] 测试定时同步状态...
echo 请求: GET /api/hardware/treatment-heads/sync/status
curl -s http://localhost:8080/api/hardware/treatment-heads/sync/status | jq .
echo.

echo [7/8] 手动触发同步...
echo 请求: POST /api/hardware/treatment-heads/sync/trigger
curl -s -X POST http://localhost:8080/api/hardware/treatment-heads/sync/trigger | jq .
echo.

echo [8/8] 检查后端日志...
echo 请检查后端控制台日志，查找以下关键信息：
echo.
echo "✅ 硬件通信日志:"
echo "   - WebSocket连接状态"
echo "   - 串口通信状态"
echo "   - 硬件指令发送和接收"
echo "   - 治疗头数据同步"
echo.
echo "✅ 归还检测日志:"
echo "   - 开始检测治疗头归还状态..."
echo "   - 检测到 X 个治疗头在治疗仓中"
echo "   - 本次检测完成，共检测到 X 个治疗头归还"
echo.
echo "✅ 手动检测日志:"
echo "   - 手动触发治疗头归还检测..."
echo "   - 手动检测完成，共检测到 X 个治疗头归还"
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 🔍 验证要点：
echo.
echo 1. **硬件通信功能**
echo    - 支持WebSocket和串口两种通信模式
echo    - 硬件指令正常发送和接收
echo    - 治疗头数据同步正常
echo    - 指示灯控制功能正常
echo.
echo 2. **归还检测功能**
echo    - 手动检测API：/detect-returns
echo    - API调用返回200状态码和检测结果
echo    - 自动更新AWAITING_RETURN → RETURNED状态
echo.
echo 3. **进程状态更新**
echo    - 所有治疗头归还后，进程状态变为COMPLETED
echo    - 档案完成次数自动增加
echo    - 进程管理页面显示正确状态
echo.
echo 4. **定时同步功能**
echo    - 每10秒自动同步治疗头数据
echo    - 更新治疗头编号对应的电量、槽号、状态
echo    - 同步状态API正常返回统计信息
echo.
echo 5. **前端界面**
echo    - 不再显示"模拟归还"按钮
echo    - 状态通过定时刷新自动更新
echo    - 显示"待归还"状态的治疗项目
echo.
echo 🚨 故障排查：
echo.
echo "如果硬件通信失败："
echo "1. 检查WebSocket连接状态"
echo "2. 检查串口设备连接"
echo "3. 检查硬件指令格式"
echo "4. 检查网络连接和防火墙设置"
echo.
echo "如果检测不工作："
echo "1. 检查硬件服务是否正常工作"
echo "2. 检查数据库中的治疗详情状态"
echo "3. 检查治疗头编号是否正确匹配"
echo "4. 检查定时任务是否正常执行"
echo.
pause
