@echo off
chcp 65001 > nul
echo ========================================
echo 系统状态检查脚本
echo ========================================
echo.

echo [1/5] 检查数据库治疗头表结构和数据...
mysql -u root -p bonesys -e "
DESCRIBE treatment_heads;
"
echo.

echo 检查治疗头数据：
mysql -u root -p bonesys -e "
SELECT 
    head_number as '治疗头编号',
    slot_number as '槽号',
    compartment_type as '仓位类型',
    CASE 
        WHEN compartment_type = 'SHALLOW' THEN '上层浅部'
        WHEN compartment_type = 'DEEP' THEN '下层深部'
        ELSE '未设置'
    END as '仓位描述',
    realtime_status as '状态',
    battery_level as '电量'
FROM treatment_heads 
ORDER BY head_number;
"
echo.

echo [2/5] 测试后端API连接...
echo 请求: GET /api/health
curl -s http://localhost:8080/api/health
echo.
echo.

echo [3/5] 测试治疗头数据同步...
echo 请求: GET /api/hardware/heads
curl -s "http://localhost:8080/api/hardware/heads?page=1&size=5" | jq .
echo.

echo [4/5] 测试治疗参数可用性检查...
echo 请求: POST /api/treatment-parameters/check-availability
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"P001001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"肩颈部\",\"color\":\"#FF6B6B\",\"parameters\":{\"time\":\"15分钟\",\"intensity\":\"30\",\"frequency\":\"1000\",\"depth\":\"深部\",\"count\":2}}]}" | jq .
echo.

echo [5/5] 测试浅部治疗头查询...
echo 请求: 浅部治疗头查询
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"P001002\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"手腕部\",\"color\":\"#00FF00\",\"parameters\":{\"time\":\"10分钟\",\"intensity\":\"25\",\"frequency\":\"100\",\"depth\":\"浅部\",\"count\":3}}]}" | jq .
echo.

echo ========================================
echo 检查完成！
echo ========================================
echo.
echo 🔍 检查要点：
echo.
echo 1. **数据库结构**
echo    - treatment_heads表应该有compartment_type字段
echo    - 治疗头1-10应该是SHALLOW
echo    - 治疗头11-20应该是DEEP
echo.
echo 2. **API连接**
echo    - 后端服务应该在8080端口运行
echo    - /api/health应该返回200状态
echo    - 治疗头数据应该包含compartmentType字段
echo.
echo 3. **浅部深部识别**
echo    - 深部贴片应该匹配DEEP类型治疗头
echo    - 浅部贴片应该匹配SHALLOW类型治疗头
echo    - API应该正确计算可用数量
echo.
echo 🚨 常见问题：
echo.
echo "如果API连接失败："
echo "1. 检查后端是否在8080端口运行"
echo "2. 检查前端是否在5173端口运行"
echo "3. 检查Vite代理配置是否正确"
echo.
echo "如果compartment_type为NULL："
echo "1. 执行 SQL/手动执行槽号修改.sql"
echo "2. 重启后端应用"
echo "3. 重新同步治疗头数据"
echo.
echo "如果浅部深部识别错误："
echo "1. 检查前端发送的depth参数（'浅部'/'深部'）"
echo "2. 检查后端的字符串匹配逻辑"
echo "3. 检查TreatmentHeadInfo的compartmentType设置"
echo.
pause
