package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.config.SerialPortConfiguration;
import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.hardware.SerialPortCommunicationAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 串口通信管理控制器
 * 提供串口配置和状态管理的API接口
 */
@RestController
@RequestMapping("/api/hardware/serial")
@CrossOrigin(origins = "*")
@ConditionalOnProperty(name = "hardware.communication.mode", havingValue = "serial")
public class SerialPortController {
    
    private static final Logger logger = LoggerFactory.getLogger(SerialPortController.class);
    
    @Autowired
    private SerialPortConfiguration serialConfig;
    
    @Autowired(required = false)
    private SerialPortCommunicationAdapter serialAdapter;
    
    /**
     * 获取串口配置信息
     * GET /api/hardware/serial/config
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getSerialConfiguration() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("upperPort", serialConfig.getUpperPort());
            config.put("upperBaudRate", serialConfig.getUpperBaudRate());
            config.put("upperDataBits", serialConfig.getUpperDataBits());
            config.put("upperStopBits", serialConfig.getUpperStopBits());
            config.put("upperParity", serialConfig.getUpperParity());
            
            config.put("lowerPort", serialConfig.getLowerPort());
            config.put("lowerBaudRate", serialConfig.getLowerBaudRate());
            config.put("lowerDataBits", serialConfig.getLowerDataBits());
            config.put("lowerStopBits", serialConfig.getLowerStopBits());
            config.put("lowerParity", serialConfig.getLowerParity());
            
            config.put("connectionTimeout", serialConfig.getConnectionTimeout());
            config.put("readTimeout", serialConfig.getReadTimeout());
            config.put("writeTimeout", serialConfig.getWriteTimeout());
            config.put("autoReconnect", serialConfig.isAutoReconnect());
            config.put("maxRetryAttempts", serialConfig.getMaxRetryAttempts());
            
            config.put("configurationSummary", serialConfig.getConfigurationSummary());
            config.put("isValid", serialConfig.isConfigurationValid());
            
            return ApiResponse.success("串口配置获取成功", config);
            
        } catch (Exception e) {
            logger.error("获取串口配置失败", e);
            return ApiResponse.error(500, "获取串口配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取串口连接状态
     * GET /api/hardware/serial/status
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getSerialStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            if (serialAdapter != null) {
                status.put("adapterInitialized", true);
                status.put("isConnected", serialAdapter.isConnected());
                status.put("connectionStatus", serialAdapter.isConnected() ? "已连接" : "未连接");
            } else {
                status.put("adapterInitialized", false);
                status.put("isConnected", false);
                status.put("connectionStatus", "适配器未初始化");
            }
            
            status.put("configurationValid", serialConfig.isConfigurationValid());
            status.put("upperPort", serialConfig.getUpperPort());
            status.put("lowerPort", serialConfig.getLowerPort());
            
            return ApiResponse.success("串口状态获取成功", status);
            
        } catch (Exception e) {
            logger.error("获取串口状态失败", e);
            return ApiResponse.error(500, "获取串口状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试串口连接
     * POST /api/hardware/serial/test
     */
    @PostMapping("/test")
    public ApiResponse<Map<String, Object>> testSerialConnection() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (serialAdapter == null) {
                result.put("success", false);
                result.put("message", "串口适配器未初始化");
                return ApiResponse.error(400, "串口适配器未初始化", result);
            }
            
            // 测试发送简单指令
            String testCommand = "TRZI\r\n";
            String response = serialAdapter.sendCommand(testCommand);
            
            result.put("success", true);
            result.put("testCommand", testCommand);
            result.put("response", response);
            result.put("message", "串口连接测试成功");
            
            logger.info("串口连接测试成功: command={}, response={}", testCommand, response);
            return ApiResponse.success("串口连接测试成功", result);
            
        } catch (Exception e) {
            logger.error("串口连接测试失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "串口连接测试失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            
            return ApiResponse.error(500, "串口连接测试失败", result);
        }
    }
    
    /**
     * 重新连接串口
     * POST /api/hardware/serial/reconnect
     */
    @PostMapping("/reconnect")
    public ApiResponse<String> reconnectSerial() {
        try {
            if (serialAdapter == null) {
                return ApiResponse.error(400, "串口适配器未初始化");
            }
            
            serialAdapter.connect();
            logger.info("串口重新连接成功");
            
            return ApiResponse.success("串口重新连接成功", "所有串口连接已重新建立");
            
        } catch (Exception e) {
            logger.error("串口重新连接失败", e);
            return ApiResponse.error(500, "串口重新连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 断开串口连接
     * POST /api/hardware/serial/disconnect
     */
    @PostMapping("/disconnect")
    public ApiResponse<String> disconnectSerial() {
        try {
            if (serialAdapter == null) {
                return ApiResponse.error(400, "串口适配器未初始化");
            }
            
            serialAdapter.disconnect();
            logger.info("串口连接已断开");
            
            return ApiResponse.success("串口连接已断开", "所有串口连接已关闭");
            
        } catch (Exception e) {
            logger.error("断开串口连接失败", e);
            return ApiResponse.error(500, "断开串口连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据治疗头编号获取对应的串口
     * GET /api/hardware/serial/port-for-head/{headNumber}
     */
    @GetMapping("/port-for-head/{headNumber}")
    public ApiResponse<Map<String, Object>> getPortForTreatmentHead(@PathVariable Integer headNumber) {
        try {
            if (headNumber < 1 || headNumber > 20) {
                return ApiResponse.badRequest("治疗头编号必须在1-20之间");
            }
            
            String port = serialConfig.getPortForTreatmentHead(headNumber);
            String compartmentType = headNumber <= 10 ? "上仓(浅部)" : "下仓(深部)";
            
            Map<String, Object> result = new HashMap<>();
            result.put("headNumber", headNumber);
            result.put("port", port);
            result.put("compartmentType", compartmentType);
            result.put("description", String.format("治疗头%d使用%s串口%s", headNumber, compartmentType, port));
            
            return ApiResponse.success("串口映射获取成功", result);
            
        } catch (Exception e) {
            logger.error("获取治疗头串口映射失败", e);
            return ApiResponse.error(500, "获取治疗头串口映射失败: " + e.getMessage());
        }
    }
}
