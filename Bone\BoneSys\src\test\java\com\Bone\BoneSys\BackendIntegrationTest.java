package com.Bone.BoneSys;

import com.Bone.BoneSys.controller.*;
import com.Bone.BoneSys.service.*;
import com.Bone.BoneSys.dto.ApiResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 后端功能集成测试
 * 验证后端核心功能的完整性和与前端、硬件的交互能力
 */
@SpringBootTest
@TestPropertySource(properties = {
    "hardware.simulator.enabled=true",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
public class BackendIntegrationTest {

    @Autowired
    private DashboardController dashboardController;
    
    @Autowired
    private SettingsService settingsService;
    
    @Autowired
    private StatisticsService statisticsService;
    
    @Autowired
    private HardwareService hardwareService;

    @Test
    void testSystemStartupAndBasicFunctionality() {
        System.out.println("🧪 测试系统启动和基础功能");
        
        // 验证所有核心服务都已正确注入
        assertNotNull(dashboardController, "DashboardController应该被正确注入");
        assertNotNull(settingsService, "SettingsService应该被正确注入");
        assertNotNull(statisticsService, "StatisticsService应该被正确注入");
        assertNotNull(hardwareService, "HardwareService应该被正确注入");
        
        System.out.println("✅ 所有核心服务注入成功");
    }

    @Test
    void testDashboardControllerIntegration() {
        System.out.println("🧪 测试主界面控制器集成");
        
        try {
            // 测试主界面数据获取
            ApiResponse<?> response = dashboardController.getMainDashboard();
            
            assertNotNull(response, "主界面响应不应为空");
            assertEquals(200, response.getCode(), "响应码应为200");
            assertNotNull(response.getData(), "响应数据不应为空");
            
            System.out.println("✅ 主界面数据接口正常工作");
            System.out.println("   响应码: " + response.getCode());
            System.out.println("   响应消息: " + response.getMessage());
            
        } catch (Exception e) {
            System.err.println("❌ 主界面控制器测试失败: " + e.getMessage());
            fail("主界面控制器应该正常工作");
        }
    }

    @Test
    void testSettingsServiceIntegration() {
        System.out.println("🧪 测试系统设置服务集成");
        
        try {
            // 测试获取系统参数
            SettingsService.SystemParameters params = settingsService.getSystemParameters();
            
            assertNotNull(params, "系统参数不应为空");
            assertEquals("FREEBONE医疗系统", params.getSystemName(), "系统名称应正确");
            assertEquals("1.0.0", params.getSystemVersion(), "系统版本应正确");
            
            // 验证治疗参数
            assertNotNull(params.getTreatmentParameters(), "治疗参数不应为空");
            assertEquals(45, params.getTreatmentParameters().getDefaultIntensity(), "默认强度应为45档位");
            assertArrayEquals(new int[]{30, 45, 60}, params.getTreatmentParameters().getIntensityOptions(), 
                             "强度档位应为30、45、60");
            
            // 验证设备配置
            assertNotNull(params.getDeviceConfiguration(), "设备配置不应为空");
            assertEquals(20, params.getDeviceConfiguration().getTotalTreatmentHeads(), "治疗头总数应为20");
            
            System.out.println("✅ 系统设置服务正常工作");
            System.out.println("   系统名称: " + params.getSystemName());
            System.out.println("   系统版本: " + params.getSystemVersion());
            System.out.println("   默认强度: " + params.getTreatmentParameters().getDefaultIntensity() + "档位");
            
        } catch (Exception e) {
            System.err.println("❌ 系统设置服务测试失败: " + e.getMessage());
            fail("系统设置服务应该正常工作");
        }
    }

    @Test
    void testStatisticsServiceIntegration() {
        System.out.println("🧪 测试统计服务集成");
        
        try {
            // 测试全局统计
            StatisticsService.GlobalTreatmentStatistics globalStats = statisticsService.getGlobalTreatmentStatistics();
            
            assertNotNull(globalStats, "全局统计不应为空");
            assertNotNull(globalStats.getTotalPatients(), "患者总数不应为空");
            assertNotNull(globalStats.getTotalRecords(), "档案总数不应为空");
            assertNotNull(globalStats.getTotalProcesses(), "进程总数不应为空");
            
            System.out.println("✅ 统计服务正常工作");
            System.out.println("   患者总数: " + globalStats.getTotalPatients());
            System.out.println("   档案总数: " + globalStats.getTotalRecords());
            System.out.println("   进程总数: " + globalStats.getTotalProcesses());
            
        } catch (Exception e) {
            System.err.println("❌ 统计服务测试失败: " + e.getMessage());
            fail("统计服务应该正常工作");
        }
    }

    @Test
    void testHardwareServiceIntegration() {
        System.out.println("🧪 测试硬件服务集成");
        
        try {
            // 测试硬件连接状态
            boolean isConnected = hardwareService.isHardwareConnected();
            System.out.println("   硬件连接状态: " + (isConnected ? "已连接" : "未连接"));
            
            // 测试治疗头同步（使用模拟器）
            var treatmentHeads = hardwareService.syncAllTreatmentHeads();
            assertNotNull(treatmentHeads, "治疗头列表不应为空");
            assertEquals(20, treatmentHeads.size(), "应该有20个治疗头");
            
            System.out.println("✅ 硬件服务正常工作");
            System.out.println("   治疗头数量: " + treatmentHeads.size());
            System.out.println("   模拟器状态: 正常");
            
        } catch (Exception e) {
            System.err.println("❌ 硬件服务测试失败: " + e.getMessage());
            fail("硬件服务应该正常工作");
        }
    }

    @Test
    void testSystemPerformance() {
        System.out.println("🧪 测试系统性能");
        
        // 测试主界面数据获取性能
        long startTime = System.currentTimeMillis();
        ApiResponse<?> dashboardResponse = dashboardController.getMainDashboard();
        long dashboardTime = System.currentTimeMillis() - startTime;
        
        assertTrue(dashboardTime < 2000, "主界面数据获取应在2秒内完成，实际: " + dashboardTime + "ms");
        
        // 测试系统设置获取性能
        startTime = System.currentTimeMillis();
        SettingsService.SystemParameters params = settingsService.getSystemParameters();
        long settingsTime = System.currentTimeMillis() - startTime;
        
        assertTrue(settingsTime < 1000, "系统设置获取应在1秒内完成，实际: " + settingsTime + "ms");
        
        // 测试统计数据计算性能
        startTime = System.currentTimeMillis();
        StatisticsService.GlobalTreatmentStatistics stats = statisticsService.getGlobalTreatmentStatistics();
        long statisticsTime = System.currentTimeMillis() - startTime;
        
        assertTrue(statisticsTime < 2000, "统计数据计算应在2秒内完成，实际: " + statisticsTime + "ms");
        
        System.out.println("✅ 系统性能测试通过");
        System.out.println("   主界面响应时间: " + dashboardTime + "ms");
        System.out.println("   设置获取时间: " + settingsTime + "ms");
        System.out.println("   统计计算时间: " + statisticsTime + "ms");
    }

    @Test
    void testDataConsistency() {
        System.out.println("🧪 测试数据一致性");
        
        try {
            // 获取主界面数据
            ApiResponse<?> dashboardResponse = dashboardController.getMainDashboard();
            assertNotNull(dashboardResponse.getData(), "主界面数据不应为空");
            
            // 获取系统设置
            SettingsService.SystemParameters params = settingsService.getSystemParameters();
            assertNotNull(params, "系统参数不应为空");
            
            // 获取统计数据
            StatisticsService.GlobalTreatmentStatistics stats = statisticsService.getGlobalTreatmentStatistics();
            assertNotNull(stats, "统计数据不应为空");
            
            // 验证数据一致性（治疗头数量）
            assertEquals(20, params.getDeviceConfiguration().getTotalTreatmentHeads(), 
                        "设置中的治疗头数量应为20");
            
            System.out.println("✅ 数据一致性验证通过");
            
        } catch (Exception e) {
            System.err.println("❌ 数据一致性测试失败: " + e.getMessage());
            fail("数据应该保持一致性");
        }
    }

    @Test
    void testErrorHandling() {
        System.out.println("🧪 测试错误处理");
        
        try {
            // 测试无效参数的处理
            SettingsService.SystemParametersUpdateRequest invalidRequest = 
                new SettingsService.SystemParametersUpdateRequest();
            SettingsService.TreatmentParameters invalidParams = 
                new SettingsService.TreatmentParameters();
            invalidParams.setDefaultIntensity(999); // 无效强度
            invalidRequest.setTreatmentParameters(invalidParams);
            
            // 应该抛出IllegalArgumentException
            assertThrows(IllegalArgumentException.class, 
                        () -> settingsService.updateSystemParameters(invalidRequest),
                        "无效参数应该抛出异常");
            
            System.out.println("✅ 错误处理验证通过");
            
        } catch (Exception e) {
            System.err.println("❌ 错误处理测试失败: " + e.getMessage());
            fail("错误处理应该正常工作");
        }
    }

    @Test
    void testFrontendBackendCompatibility() {
        System.out.println("🧪 测试前后端兼容性");
        
        try {
            // 验证API响应格式符合前端期望
            ApiResponse<?> response = dashboardController.getMainDashboard();
            
            // 验证标准API响应格式
            assertNotNull(response.getCode(), "响应码不应为空");
            assertNotNull(response.getMessage(), "响应消息不应为空");
            assertNotNull(response.getTimestamp(), "时间戳不应为空");
            
            // 验证成功响应的数据字段
            if (response.getCode() == 200) {
                assertNotNull(response.getData(), "成功响应的数据字段不应为空");
            }
            
            System.out.println("✅ 前后端兼容性验证通过");
            System.out.println("   API响应格式: 标准");
            System.out.println("   数据字段: 完整");
            
        } catch (Exception e) {
            System.err.println("❌ 前后端兼容性测试失败: " + e.getMessage());
            fail("前后端应该保持兼容");
        }
    }

    @Test
    void testHardwareBackendIntegration() {
        System.out.println("🧪 测试硬件后端集成");
        
        try {
            // 测试硬件模拟器功能
            var treatmentHeads = hardwareService.syncAllTreatmentHeads();
            assertNotNull(treatmentHeads, "治疗头同步不应为空");
            assertFalse(treatmentHeads.isEmpty(), "治疗头列表不应为空");
            
            // 验证治疗头数据格式
            var firstHead = treatmentHeads.get(0);
            assertNotNull(firstHead.getHeadNumber(), "治疗头编号不应为空");
            assertNotNull(firstHead.getBatteryLevel(), "电量信息不应为空");
            assertNotNull(firstHead.getStatus(), "状态信息不应为空");
            
            System.out.println("✅ 硬件后端集成验证通过");
            System.out.println("   治疗头数据: 完整");
            System.out.println("   模拟器功能: 正常");
            
        } catch (Exception e) {
            System.err.println("❌ 硬件后端集成测试失败: " + e.getMessage());
            fail("硬件后端集成应该正常工作");
        }
    }
}
