# System Issues Diagnosis and Fix Script
Write-Host "========================================" -ForegroundColor Green
Write-Host "System Issues Diagnosis and Fix Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Step 1: Check database connection
Write-Host "[Step 1] Testing database connection..." -ForegroundColor Yellow
try {
    $dbTest = mysql -u root -p bonesys -e "SELECT 1 as test;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database connection successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Database connection failed" -ForegroundColor Red
        Write-Host "Please check MySQL service and credentials" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Database connection error: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Check compartment_type field
Write-Host ""
Write-Host "[Step 2] Checking compartment_type field..." -ForegroundColor Yellow
$compartmentCheck = mysql -u root -p bonesys -e "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'bonesys' AND TABLE_NAME = 'treatment_heads' AND COLUMN_NAME = 'compartment_type';" 2>$null

if ($compartmentCheck -match "1") {
    Write-Host "✅ compartment_type field exists" -ForegroundColor Green
} else {
    Write-Host "❌ compartment_type field missing, adding..." -ForegroundColor Yellow
    mysql -u root -p bonesys -e "ALTER TABLE treatment_heads ADD COLUMN compartment_type VARCHAR(10) NULL DEFAULT NULL COMMENT 'Compartment type: SHALLOW or DEEP' AFTER slot_number;"
    Write-Host "✅ compartment_type field added" -ForegroundColor Green
}

# Step 3: Fix compartment_type data
Write-Host ""
Write-Host "[Step 3] Fixing compartment_type data..." -ForegroundColor Yellow
mysql -u root -p bonesys -e "UPDATE treatment_heads SET compartment_type = 'SHALLOW' WHERE head_number BETWEEN 1 AND 10 AND (compartment_type IS NULL OR compartment_type = ''); UPDATE treatment_heads SET compartment_type = 'DEEP' WHERE head_number BETWEEN 11 AND 20 AND (compartment_type IS NULL OR compartment_type = '');"
Write-Host "✅ compartment_type data updated" -ForegroundColor Green

# Step 4: Test backend APIs
Write-Host ""
Write-Host "[Step 4] Testing backend APIs..." -ForegroundColor Yellow

# Test health API
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Health API working" -ForegroundColor Green
} catch {
    Write-Host "❌ Health API failed: $_" -ForegroundColor Red
}

# Test treatment heads API
try {
    $headsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/hardware/heads?page=1&size=3" -Method Get -TimeoutSec 5
    Write-Host "✅ Treatment heads API working" -ForegroundColor Green
} catch {
    Write-Host "❌ Treatment heads API failed: $_" -ForegroundColor Red
}

# Step 5: Test treatment parameters API
Write-Host ""
Write-Host "[Step 5] Testing treatment parameters API..." -ForegroundColor Yellow

# Test shallow patch
$shallowBody = @{
    patientId = "TEST001"
    treatmentMode = "local"
    bodyParts = @(
        @{
            name = "wrist"
            color = "#00FF00"
            parameters = @{
                time = "10min"
                intensity = "25"
                frequency = "100"
                depth = "浅部"
                count = 2
            }
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $shallowResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/treatment-parameters/check-availability" -Method Post -Body $shallowBody -ContentType "application/json" -TimeoutSec 5
    Write-Host "✅ Shallow patch API working" -ForegroundColor Green
} catch {
    Write-Host "❌ Shallow patch API failed: $_" -ForegroundColor Red
}

# Test deep patch
$deepBody = @{
    patientId = "TEST002"
    treatmentMode = "local"
    bodyParts = @(
        @{
            name = "shoulder"
            color = "#FF0000"
            parameters = @{
                time = "15min"
                intensity = "30"
                frequency = "1000"
                depth = "深部"
                count = 3
            }
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $deepResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/treatment-parameters/check-availability" -Method Post -Body $deepBody -ContentType "application/json" -TimeoutSec 5
    Write-Host "✅ Deep patch API working" -ForegroundColor Green
} catch {
    Write-Host "❌ Deep patch API failed: $_" -ForegroundColor Red
}

# Step 6: Test sync status
Write-Host ""
Write-Host "[Step 6] Testing sync status..." -ForegroundColor Yellow
try {
    $syncResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/hardware/treatment-heads/sync/status" -Method Get -TimeoutSec 5
    Write-Host "✅ Sync status API working" -ForegroundColor Green
} catch {
    Write-Host "❌ Sync status API failed: $_" -ForegroundColor Red
}

# Step 7: Verify database data
Write-Host ""
Write-Host "[Step 7] Verifying database data..." -ForegroundColor Yellow
$verifyResult = mysql -u root -p bonesys -e "SELECT 'Shallow heads' as type, COUNT(*) as total, COUNT(CASE WHEN compartment_type = 'SHALLOW' THEN 1 END) as correct_type FROM treatment_heads WHERE head_number BETWEEN 1 AND 10 UNION ALL SELECT 'Deep heads' as type, COUNT(*) as total, COUNT(CASE WHEN compartment_type = 'DEEP' THEN 1 END) as correct_type FROM treatment_heads WHERE head_number BETWEEN 11 AND 20;"
Write-Host $verifyResult

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Diagnosis and Fix Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Visit http://localhost:5173/api-test for visual testing" -ForegroundColor White
Write-Host "2. Check if both frontend (5173) and backend (8080) are running" -ForegroundColor White
Write-Host "3. If issues persist, restart the backend application" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
