package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.service.TreatmentHeadRecommendationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 硬件控制API控制器
 * 提供硬件设备控制的REST接口
 */
@RestController
@RequestMapping("/api/hardware")
@CrossOrigin(origins = "*")
public class HardwareController {
    
    private static final Logger logger = LoggerFactory.getLogger(HardwareController.class);
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private TreatmentHeadRecommendationService recommendationService;
    
    /**
     * 获取治疗头管理页面数据
     * GET /api/hardware/heads
     * 页面：新版-治疗头管理.png
     */
    @GetMapping("/heads")
    public ApiResponse<Map<String, Object>> getTreatmentHeads(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            logger.info("API request: get treatment heads page={}, size={}", page, size);
            
            // 获取所有治疗头数据（包括数据库中的所有20个治疗头）
            List<TreatmentHeadInfo> allHeads = hardwareService.getAllTreatmentHeadsForManagement();
            
            // 计算分页
            int totalRecords = allHeads.size();
            int totalPages = (int) Math.ceil((double) totalRecords / size);
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, totalRecords);
            
            List<TreatmentHeadInfo> pagedHeads = allHeads.subList(startIndex, endIndex);
            
            // 转换为API文档格式
            List<Map<String, Object>> heads = pagedHeads.stream().map(head -> {
                Map<String, Object> headData = new java.util.HashMap<>();
                headData.put("headNumber", head.getHeadNumber());
                headData.put("textStatus", mapStatusToText(head.getStatus()));
                headData.put("iconStatus", head.getStatus());
                headData.put("usageCount", head.getUsageCount());
                headData.put("totalUsageTime", "0小时0分钟"); // 暂时使用默认值
                headData.put("batteryLevel", head.getBatteryLevel());
                headData.put("lightColor", mapStatusToLightColor(head.getStatus()));
                return headData;
            }).collect(java.util.stream.Collectors.toList());
            
            // 构建分页信息
            Map<String, Object> pagination = new java.util.HashMap<>();
            pagination.put("currentPage", page);
            pagination.put("totalPages", totalPages);
            pagination.put("totalRecords", totalRecords);
            pagination.put("pageSize", size);
            
            // 构建响应数据
            Map<String, Object> responseData = new java.util.HashMap<>();
            responseData.put("heads", heads);
            responseData.put("pagination", pagination);
            
            return ApiResponse.success("治疗头列表获取成功", responseData);
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to get treatment heads", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during getting treatment heads", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 将状态映射为中文文本
     */
    private String mapStatusToText(String status) {
        switch (status != null ? status.toUpperCase() : "UNKNOWN") {
            case "CHARGING": return "充电中";
            case "CHARGED": return "充电结束";
            case "IN_USE": return "使用中";
            case "TREATING": return "治疗中"; // 新增TREATING状态
            case "LOW_BATTERY": return "电量不足";
            default: return "未知状态";
        }
    }
    
    /**
     * 将状态映射为指示灯颜色
     */
    private String mapStatusToLightColor(String status) {
        switch (status != null ? status.toUpperCase() : "UNKNOWN") {
            case "CHARGING": return "YELLOW";
            case "CHARGED": return "GREEN";
            case "IN_USE": return "BLUE";
            case "TREATING": return "RED"; // 新增TREATING状态，使用红色表示正在治疗
            case "LOW_BATTERY": return "RED";
            default: return "OFF";
        }
    }
    
    /**
     * 同步所有治疗头数据
     * GET /api/hardware/treatment-heads/sync
     */
    @PostMapping("/treatment-heads/sync")
    public ApiResponse<List<TreatmentHeadInfo>> syncTreatmentHeads() {
        try {
            logger.info("API request: sync treatment heads");
            List<TreatmentHeadInfo> headInfos = hardwareService.syncAllTreatmentHeads();
            return ApiResponse.success("Successfully synced treatment heads from hardware", headInfos);
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to sync treatment heads", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during treatment heads sync", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 设置治疗头指示灯
     * POST /api/hardware/treatment-heads/lights
     */
    @PostMapping("/treatment-heads/lights")
    public ApiResponse<List<TreatmentHeadLightResponse>> setTreatmentHeadLights(
            @RequestBody List<TreatmentHeadLightRequest> lightRequests) {
        try {
            logger.info("API request: set treatment head lights for {} heads", lightRequests.size());
            List<TreatmentHeadLightResponse> responses = hardwareService.setTreatmentHeadLights(lightRequests);
            return ApiResponse.success("Successfully set treatment head lights", responses);
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to set treatment head lights", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during light setting", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 关闭治疗头指示灯
     * DELETE /api/hardware/treatment-heads/lights
     */
    @DeleteMapping("/treatment-heads/lights")
    public ApiResponse<List<Integer>> turnOffTreatmentHeadLights(@RequestBody List<Integer> headNumbers) {
        try {
            logger.info("API request: turn off lights for heads: {}", headNumbers);
            List<Integer> responseHeadNumbers = hardwareService.turnOffTreatmentHeadLights(headNumbers);
            return ApiResponse.success("Successfully turned off treatment head lights", responseHeadNumbers);
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to turn off treatment head lights", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during light turn off", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 发送治疗参数到治疗头（不开始治疗）
     * POST /api/hardware/treatment-params
     */
    @PostMapping("/treatment-params")
    public ApiResponse<Boolean> sendTreatmentParams(@RequestBody TreatmentParamsRequest request) {
        try {
            logger.info("API request: send treatment params: {}", request);
            boolean success = hardwareService.sendTreatmentParams(request);
            
            if (success) {
                return ApiResponse.success("Successfully sent treatment parameters", success);
            } else {
                return ApiResponse.error(500, "Failed to send treatment parameters");
            }
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to send treatment params", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during treatment params sending", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 开始治疗
     * POST /api/hardware/treatment/{headNumber}/start
     */
    @PostMapping("/treatment/{headNumber}/start")
    public ApiResponse<Boolean> startTreatment(
            @PathVariable int headNumber,
            @RequestBody TreatmentStartRequest request) {
        try {
            logger.info("API request: start treatment on head {} with duration={}min, intensity={}mW/cm², frequency={}Hz", 
                       headNumber, request.getDuration(), request.getIntensity(), request.getFrequency());
            
            boolean success = hardwareService.startTreatment(headNumber, request.getDuration(), request.getIntensity(), request.getFrequency());
            
            if (success) {
                return ApiResponse.success("Successfully started treatment", success);
            } else {
                return ApiResponse.error(500, "Failed to start treatment");
            }
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to start treatment", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during treatment start", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 停止治疗
     * POST /api/hardware/treatment/{headNumber}/stop
     */
    @PostMapping("/treatment/{headNumber}/stop")
    public ApiResponse<Boolean> stopTreatment(@PathVariable int headNumber) {
        try {
            logger.info("API request: stop treatment on head {}", headNumber);
            boolean success = hardwareService.stopTreatment(headNumber);
            
            if (success) {
                return ApiResponse.success("Successfully stopped treatment", success);
            } else {
                return ApiResponse.error(500, "Failed to stop treatment");
            }
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to stop treatment", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during treatment stop", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 检查硬件连接状态
     * GET /api/hardware/status
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getHardwareStatus() {
        try {
            boolean connected = hardwareService.isHardwareConnected();
            
            Map<String, Object> status = Map.of(
                "connected", connected,
                "status", connected ? "Connected" : "Disconnected",
                "timestamp", System.currentTimeMillis()
            );
            
            return ApiResponse.success("Hardware status retrieved", status);
            
        } catch (Exception e) {
            logger.error("Error checking hardware status", e);
            return ApiResponse.error(500, "Failed to check hardware status: " + e.getMessage());
        }
    }
    
    /**
     * 重新连接硬件
     * POST /api/hardware/reconnect
     */
    @PostMapping("/reconnect")
    public ApiResponse<Boolean> reconnectHardware() {
        try {
            logger.info("API request: reconnect hardware");
            hardwareService.reconnectHardware();
            return ApiResponse.success("Successfully reconnected to hardware", true);
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to reconnect hardware", e);
            return ApiResponse.error(500, "Hardware reconnection failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during hardware reconnection", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 批量设置单色指示灯（便捷接口）
     * POST /api/hardware/treatment-heads/lights/batch
     */
    @PostMapping("/treatment-heads/lights/batch")
    public ApiResponse<List<TreatmentHeadLightResponse>> setBatchLights(
            @RequestParam List<Integer> headNumbers,
            @RequestParam int colorCode) {
        try {
            logger.info("API request: set batch lights for heads {} with color {}", headNumbers, colorCode);
            
            List<TreatmentHeadLightRequest> lightRequests = headNumbers.stream()
                .map(headNumber -> new TreatmentHeadLightRequest(headNumber, colorCode))
                .collect(java.util.stream.Collectors.toList());
            
            List<TreatmentHeadLightResponse> responses = hardwareService.setTreatmentHeadLights(lightRequests);
            return ApiResponse.success("Successfully set batch lights", responses);
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to set batch lights", e);
            return ApiResponse.error(500, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during batch light setting", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 检查治疗头可用性并获取推荐（新格式API）
     * POST /api/hardware/treatment-heads/check-availability
     */
    @PostMapping("/treatment-heads/check-availability")
    public ResponseEntity<ApiResponse<TreatmentHeadAvailabilityResponse>> checkTreatmentHeadAvailability(
            @RequestBody TreatmentHeadAvailabilityRequest request) {
        try {
            // 记录兼容性信息
            if (request.isLegacyFormat()) {
                logger.info("API request: check treatment head availability using legacy format - {}", 
                           request.getCompatibilityInfo());
            } else {
                logger.info("API request: check treatment head availability for {} total patches", 
                           request.calculateTotalRequiredCount());
            }
            
            TreatmentHeadAvailabilityResponse response = recommendationService.checkAvailabilityAndRecommend(request);

            if (response.isSufficient()) {
                return ResponseEntity.ok(ApiResponse.success("Treatment heads are sufficient", response));
            } else {
                // 返回HTTP 409状态码表示治疗头资源冲突/不足
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error(409, response.getMessage(), response));
            }

        } catch (SerialCommunicationException e) {
            logger.error("Failed to check treatment head availability", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(1001, "Hardware communication failed: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during availability check", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "Internal server error: " + e.getMessage()));
        }
    }
    
    /**
     * 检查治疗头可用性（向后兼容的API端点）
     * POST /api/hardware/treatment-heads/check-availability-legacy
     * @deprecated 使用 /api/hardware/treatment-heads/check-availability 替代
     */
    @Deprecated
    @PostMapping("/treatment-heads/check-availability-legacy")
    public ApiResponse<TreatmentHeadAvailabilityResponse> checkTreatmentHeadAvailabilityLegacy(
            @RequestParam int requiredCount,
            @RequestParam String treatmentMode,
            @RequestParam(required = false) List<String> bodyParts,
            @RequestParam(defaultValue = "SHALLOW") String patchType) {
        try {
            logger.warn("Using deprecated legacy API endpoint. Please migrate to new format.");
            logger.info("Legacy API request: requiredCount={}, treatmentMode={}, bodyParts={}, patchType={}", 
                       requiredCount, treatmentMode, bodyParts, patchType);
            
            // 使用废弃的服务方法
            TreatmentHeadAvailabilityResponse response = recommendationService.checkAvailability(
                requiredCount, treatmentMode, bodyParts, patchType);
            
            if (response.isSufficient()) {
                return ApiResponse.success("Treatment heads are sufficient (legacy API)", response);
            } else {
                return ApiResponse.error(1002, "治疗头数量不足", response);
            }
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to check treatment head availability (legacy)", e);
            return ApiResponse.error(1001, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during availability check (legacy)", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 简化的治疗头可用性检查（向后兼容）
     * GET /api/hardware/treatment-heads/check-simple
     * @deprecated 使用 POST /api/hardware/treatment-heads/check-availability 替代
     */
    @Deprecated
    @GetMapping("/treatment-heads/check-simple")
    public ApiResponse<TreatmentHeadAvailabilityResponse> checkTreatmentHeadAvailabilitySimple(
            @RequestParam int requiredCount,
            @RequestParam String treatmentMode) {
        try {
            logger.warn("Using deprecated simple API endpoint. Please migrate to new format.");
            logger.info("Simple API request: requiredCount={}, treatmentMode={}", requiredCount, treatmentMode);
            
            // 使用废弃的服务方法
            TreatmentHeadAvailabilityResponse response = recommendationService.checkAvailability(
                requiredCount, treatmentMode);
            
            if (response.isSufficient()) {
                return ApiResponse.success("Treatment heads are sufficient (simple API)", response);
            } else {
                return ApiResponse.error(1002, "治疗头数量不足", response);
            }
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to check treatment head availability (simple)", e);
            return ApiResponse.error(1001, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during availability check (simple)", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 向推荐的治疗头发送治疗参数
     * POST /api/hardware/treatment-heads/send-parameters
     */
    @PostMapping("/treatment-heads/send-parameters")
    public ApiResponse<Boolean> sendParametersToRecommended(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> recommendationMaps = (List<Map<String, Object>>) request.get("recommendations");
            String treatmentMode = (String) request.get("treatmentMode");
            @SuppressWarnings("unchecked")
            Map<String, Object> treatmentParamsMap = (Map<String, Object>) request.get("treatmentParams");
            
            if (recommendationMaps == null || treatmentMode == null || treatmentParamsMap == null) {
                return ApiResponse.error(400, "Missing required parameters: recommendations, treatmentMode, treatmentParams");
            }
            
            // 转换推荐列表
            List<TreatmentHeadRecommendation> recommendations = recommendationMaps.stream()
                .map(this::mapToRecommendation)
                .collect(java.util.stream.Collectors.toList());
            
            // 转换治疗参数
            TreatmentParamsRequest treatmentParams = mapToTreatmentParams(treatmentParamsMap);
            
            logger.info("API request: send treatment parameters to {} recommended heads, mode: {}", 
                       recommendations.size(), treatmentMode);
            
            boolean success = recommendationService.sendTreatmentParametersToRecommended(
                recommendations, treatmentParams, treatmentMode);
            
            if (success) {
                String message = "ON_SITE".equals(treatmentMode) ? 
                    "Treatment parameters sent and treatment started" : 
                    "Treatment parameters sent to recommended heads";
                return ApiResponse.success(message, success);
            } else {
                return ApiResponse.error(500, "Failed to send treatment parameters");
            }
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to send treatment parameters", e);
            return ApiResponse.error(1001, "Hardware communication failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during parameter sending", e);
            return ApiResponse.error(500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 关闭所有治疗头指示灯
     * POST /api/hardware/treatment-heads/lights/turn-off-all
     */
    @PostMapping("/treatment-heads/lights/turn-off-all")
    public ApiResponse<String> turnOffAllLights() {
        try {
            logger.info("API request: turn off all treatment head lights");
            // recommendationService.turnOffAllLights();
            // TODO: Implement turn off all lights
            return ApiResponse.success("All treatment head lights turned off", "Success");
            
        } catch (Exception e) {
            logger.error("Failed to turn off all lights", e);
            return ApiResponse.error(500, "Failed to turn off lights: " + e.getMessage());
        }
    }
    
    /**
     * 辅助方法：将Map转换为TreatmentHeadRecommendation
     */
    private TreatmentHeadRecommendation mapToRecommendation(Map<String, Object> map) {
        TreatmentHeadRecommendation rec = new TreatmentHeadRecommendation();
        rec.setHeadNumber((Integer) map.get("headNumber"));
        rec.setSlotNumber((Integer) map.get("slotNumber"));
        rec.setBatteryLevel((Integer) map.get("batteryLevel"));
        rec.setUsageCount((Integer) map.get("usageCount"));
        rec.setStatus((String) map.get("status"));
        rec.setLightColor((Integer) map.get("lightColor"));
        rec.setLightColorName((String) map.get("lightColorName"));
        rec.setPriority((Integer) map.get("priority"));
        rec.setRecommendationReason((String) map.get("recommendationReason"));
        rec.setCompartmentType((String) map.get("compartmentType"));
        return rec;
    }
    
    /**
     * 辅助方法：将Map转换为TreatmentParamsRequest
     */
    private TreatmentParamsRequest mapToTreatmentParams(Map<String, Object> map) {
        int durationMinutes = (Integer) map.get("durationMinutes");
        int intensity = (Integer) map.get("intensity");
        int frequency = (Integer) map.get("frequency");
        @SuppressWarnings("unchecked")
        List<Integer> headNumbers = (List<Integer>) map.get("headNumbers");
        
        return new TreatmentParamsRequest(durationMinutes, intensity, frequency, headNumbers);
    }
}