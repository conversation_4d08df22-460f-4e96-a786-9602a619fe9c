package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.LocalTreatmentService;
import com.Bone.BoneSys.service.LocalTreatmentService.LocalTreatmentRequest;
import com.Bone.BoneSys.service.LocalTreatmentService.LocalTreatmentResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 本地治疗控制器
 * 提供本地治疗模式的API接口
 */
@RestController
@RequestMapping("/api/treatment/local")
@CrossOrigin(origins = "*")
public class LocalTreatmentController {
    
    private static final Logger logger = LoggerFactory.getLogger(LocalTreatmentController.class);
    
    @Autowired
    private LocalTreatmentService localTreatmentService;
    
    /**
     * 检查治疗头充足性
     * POST /api/treatment/local/check-availability
     */
    @PostMapping("/check-availability")
    public ApiResponse<Map<String, Object>> checkTreatmentHeadAvailability(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> requiredHeadNumbers = (List<Integer>) request.get("headNumbers");
            
            if (requiredHeadNumbers == null || requiredHeadNumbers.isEmpty()) {
                return ApiResponse.badRequest("治疗头编号列表不能为空");
            }
            
            boolean available = localTreatmentService.checkTreatmentHeadAvailability(requiredHeadNumbers);
            
            Map<String, Object> result = new HashMap<>();
            result.put("available", available);
            result.put("requiredHeadNumbers", requiredHeadNumbers);
            result.put("message", available ? "所有治疗头都可用" : "部分治疗头不可用");
            
            return ApiResponse.success("治疗头充足性检查完成", result);
            
        } catch (Exception e) {
            logger.error("检查治疗头充足性失败", e);
            return ApiResponse.error(500, "检查治疗头充足性失败: " + e.getMessage());
        }
    }
    
    /**
     * 点亮推荐治疗头指示灯
     * POST /api/treatment/local/light-up-heads
     */
    @PostMapping("/light-up-heads")
    public ApiResponse<Map<String, Object>> lightUpRecommendedHeads(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> headNumbers = (List<Integer>) request.get("headNumbers");
            
            if (headNumbers == null || headNumbers.isEmpty()) {
                return ApiResponse.badRequest("治疗头编号列表不能为空");
            }
            
            boolean success = localTreatmentService.lightUpRecommendedHeads(headNumbers);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("headNumbers", headNumbers);
            result.put("message", success ? "治疗头指示灯点亮成功" : "治疗头指示灯点亮失败");
            
            return ApiResponse.success("治疗头指示灯操作完成", result);
            
        } catch (Exception e) {
            logger.error("点亮治疗头指示灯失败", e);
            return ApiResponse.error(500, "点亮治疗头指示灯失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行本地治疗
     * POST /api/treatment/local/execute
     */
    @PostMapping("/execute")
    public ApiResponse<LocalTreatmentResponse> executeLocalTreatment(@RequestBody LocalTreatmentRequest request) {
        try {
            // 验证请求参数
            if (request.getRecommendedHeadNumbers() == null || request.getRecommendedHeadNumbers().isEmpty()) {
                return ApiResponse.badRequest("治疗头编号列表不能为空");
            }
            
            if (request.getDuration() <= 0 || request.getDuration() > 60) {
                return ApiResponse.badRequest("治疗时长必须在1-60分钟之间");
            }
            
            if (request.getIntensity() < 1 || request.getIntensity() > 100) {
                return ApiResponse.badRequest("治疗强度必须在1-100之间");
            }
            
            if (request.getFrequency() != 0 && request.getFrequency() != 1) {
                return ApiResponse.badRequest("频率标记必须是0(100Hz)或1(1000Hz)");
            }
            
            logger.info("开始执行本地治疗: 治疗头={}, 时长={}分钟, 强度={}, 频率={}", 
                       request.getRecommendedHeadNumbers(), request.getDuration(), 
                       request.getIntensity(), request.getFrequency() == 0 ? "100Hz" : "1000Hz");
            
            LocalTreatmentResponse response = localTreatmentService.executeLocalTreatment(request);
            
            if (response.isSuccess()) {
                return ApiResponse.success("本地治疗执行成功", response);
            } else {
                return ApiResponse.error(400, response.getMessage(), response);
            }
            
        } catch (Exception e) {
            logger.error("执行本地治疗失败", e);
            return ApiResponse.error(500, "执行本地治疗失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取本地治疗配置信息
     * GET /api/treatment/local/config
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getLocalTreatmentConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("maxDuration", 60); // 最大治疗时长（分钟）
            config.put("minDuration", 1);  // 最小治疗时长（分钟）
            config.put("maxIntensity", 100); // 最大治疗强度
            config.put("minIntensity", 1);   // 最小治疗强度
            config.put("frequencies", Map.of(
                "0", "100Hz",
                "1", "1000Hz"
            ));
            config.put("commandTimeout", 10); // 指令超时时间（秒）
            config.put("maxTreatmentHeads", 20); // 最大治疗头数量
            config.put("description", "本地治疗模式配置参数");
            
            return ApiResponse.success("本地治疗配置获取成功", config);
            
        } catch (Exception e) {
            logger.error("获取本地治疗配置失败", e);
            return ApiResponse.error(500, "获取本地治疗配置失败: " + e.getMessage());
        }
    }
}
