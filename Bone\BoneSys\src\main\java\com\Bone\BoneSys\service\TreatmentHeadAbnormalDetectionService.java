package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import com.Bone.BoneSys.websocket.NotificationWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 治疗头异常检测服务
 * 负责检测治疗头连接异常、统计失败次数、更新状态和推送通知
 */
@Service
public class TreatmentHeadAbnormalDetectionService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadAbnormalDetectionService.class);
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    @Autowired
    private NotificationWebSocketHandler notificationHandler;
    
    // 失败次数统计（内存中维护，key为治疗头编号）
    private final Map<Integer, Integer> failureCountMap = new ConcurrentHashMap<>();
    
    // 异常状态跟踪（避免重复通知）
    private final Map<Integer, Boolean> abnormalStatusMap = new ConcurrentHashMap<>();
    
    // 复位通知跟踪（避免重复通知）
    private final Map<Integer, Boolean> resetNotificationSentMap = new ConcurrentHashMap<>();
    
    // 失败次数阈值
    private static final int FAILURE_THRESHOLD = 5;
    
    /**
     * 处理治疗头查询结果，检测异常情况
     */
    public void processQueryResults(List<TreatmentHeadInfo> headInfoList) {
        logger.debug("开始处理治疗头查询结果，共 {} 个治疗头", headInfoList.size());
        
        // 重置当前查询中的治疗头状态
        Map<Integer, Boolean> currentQueryHeads = new HashMap<>();
        
        for (TreatmentHeadInfo headInfo : headInfoList) {
            int headNumber = headInfo.getHeadNumber();
            currentQueryHeads.put(headNumber, true);
            
            if ("ABNORMAL".equals(headInfo.getStatus())) {
                // 处理异常治疗头
                handleAbnormalHead(headInfo);
            } else {
                // 处理正常治疗头
                handleNormalHead(headInfo);
            }
        }
        
        // 检查数据库中的异常治疗头是否已恢复
        checkRecoveredHeads(currentQueryHeads);
    }
    
    /**
     * 处理异常治疗头
     */
    private void handleAbnormalHead(TreatmentHeadInfo headInfo) {
        int headNumber = headInfo.getHeadNumber();
        int failureCount = headInfo.getFailureCount();
        String compartmentType = headInfo.getCompartmentType();
        int slotNumber = headInfo.getSlotNumber();
        
        logger.warn("检测到异常治疗头: 编号={}, 槽位={}, 仓位={}, 失败次数={}", 
                   headNumber, slotNumber, compartmentType, failureCount);
        
        // 更新失败次数（只在内存中记录）
        failureCountMap.put(headNumber, failureCount);

        // 更新数据库状态
        updateTreatmentHeadStatus(headNumber, TreatmentHeadStatus.ABNORMAL);
        
        // 检查是否需要发送通知
        boolean wasAbnormal = abnormalStatusMap.getOrDefault(headNumber, false);
        if (!wasAbnormal) {
            // 首次检测到异常，发送重新插入通知
            sendReinsertNotification(headNumber, compartmentType, slotNumber);
            abnormalStatusMap.put(headNumber, true);
        }
        
        // 检查是否需要发送复位通知
        if (failureCount > FAILURE_THRESHOLD) {
            boolean resetNotificationSent = resetNotificationSentMap.getOrDefault(headNumber, false);
            if (!resetNotificationSent) {
                sendResetNotification(headNumber, compartmentType, slotNumber, failureCount);
                resetNotificationSentMap.put(headNumber, true);
            }
        }
    }
    
    /**
     * 处理正常治疗头
     */
    private void handleNormalHead(TreatmentHeadInfo headInfo) {
        int headNumber = headInfo.getHeadNumber();
        
        // 如果之前是异常状态，现在恢复正常
        boolean wasAbnormal = abnormalStatusMap.getOrDefault(headNumber, false);
        if (wasAbnormal) {
            logger.info("治疗头 {} 已从异常状态恢复正常", headNumber);
            
            // 清除异常状态记录
            abnormalStatusMap.remove(headNumber);
            failureCountMap.remove(headNumber);
            resetNotificationSentMap.remove(headNumber);
            
            // 更新数据库状态
            TreatmentHeadStatus newStatus = determineNormalStatus(headInfo);
            updateTreatmentHeadStatus(headNumber, newStatus);
        }
    }
    
    /**
     * 检查已恢复的治疗头
     */
    private void checkRecoveredHeads(Map<Integer, Boolean> currentQueryHeads) {
        // 检查之前异常但本次查询中未出现的治疗头
        for (Integer headNumber : abnormalStatusMap.keySet()) {
            if (!currentQueryHeads.containsKey(headNumber)) {
                // 治疗头不在当前查询结果中，可能已被取出
                logger.info("异常治疗头 {} 不在当前查询结果中，可能已被取出", headNumber);
                // 保持异常状态，等待重新插入
            }
        }
    }
    
    /**
     * 确定正常治疗头的状态
     */
    private TreatmentHeadStatus determineNormalStatus(TreatmentHeadInfo headInfo) {
        int batteryLevel = headInfo.getBatteryLevel();
        
        if (batteryLevel >= 80) {
            return TreatmentHeadStatus.CHARGED;
        } else if (batteryLevel > 20) {
            return TreatmentHeadStatus.CHARGING;
        } else {
            return TreatmentHeadStatus.CHARGING; // 低电量也算充电中
        }
    }
    
    /**
     * 更新治疗头状态到数据库
     */
    private void updateTreatmentHeadStatus(int headNumber, TreatmentHeadStatus status) {
        try {
            Optional<TreatmentHead> headOptional = treatmentHeadRepository.findByHeadNumber(headNumber);
            if (headOptional.isPresent()) {
                TreatmentHead head = headOptional.get();
                TreatmentHeadStatus oldStatus = head.getRealtimeStatus();

                if (oldStatus != status) {
                    head.setRealtimeStatus(status);
                    treatmentHeadRepository.save(head);

                    logger.info("治疗头 {} 状态已更新: {} -> {}", headNumber, oldStatus, status);
                }
            } else {
                logger.warn("治疗头 {} 在数据库中不存在，无法更新状态", headNumber);
            }
        } catch (Exception e) {
            logger.error("更新治疗头 {} 状态失败", headNumber, e);
        }
    }


    
    /**
     * 发送重新插入通知
     */
    private void sendReinsertNotification(int headNumber, String compartmentType, int slotNumber) {
        try {
            notificationHandler.sendReinsertHeadNotification(headNumber, compartmentType, slotNumber);
            logger.info("已发送重新插入通知: 治疗头={}, 仓位={}, 槽位={}", headNumber, compartmentType, slotNumber);
        } catch (Exception e) {
            logger.error("发送重新插入通知失败", e);
        }
    }
    
    /**
     * 发送复位通知
     */
    private void sendResetNotification(int headNumber, String compartmentType, int slotNumber, int failureCount) {
        try {
            notificationHandler.sendResetHeadNotification(headNumber, compartmentType, slotNumber, failureCount);
            logger.info("已发送复位通知: 治疗头={}, 仓位={}, 槽位={}, 失败次数={}", 
                       headNumber, compartmentType, slotNumber, failureCount);
        } catch (Exception e) {
            logger.error("发送复位通知失败", e);
        }
    }
    
    /**
     * 获取当前异常状态统计
     */
    public Map<String, Object> getAbnormalStatusStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("abnormalHeadCount", abnormalStatusMap.size());
        stats.put("abnormalHeads", abnormalStatusMap.keySet());
        stats.put("failureCounts", new HashMap<>(failureCountMap));
        stats.put("resetNotificationsSent", resetNotificationSentMap.size());
        
        return stats;
    }
    
    /**
     * 手动重置异常状态（用于测试或维护）
     */
    public void resetAbnormalStatus(int headNumber) {
        abnormalStatusMap.remove(headNumber);
        failureCountMap.remove(headNumber);
        resetNotificationSentMap.remove(headNumber);
        
        logger.info("治疗头 {} 的异常状态已手动重置", headNumber);
    }
    
    /**
     * 清除所有异常状态记录
     */
    public void clearAllAbnormalStatus() {
        abnormalStatusMap.clear();
        failureCountMap.clear();
        resetNotificationSentMap.clear();
        
        logger.info("所有异常状态记录已清除");
    }
}
