<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';
import { getMainDashboard } from '@/api';

const router = useRouter();

// 治疗头状态数据
const availableHeads = ref(0);
const totalHeads = ref(0);

// 系统信息数据
const systemInfo = ref({
  systemName: 'FREEBONE医疗系统',
  version: '1.0.0',
  uptime: '0小时0分钟'
});

const dataOverview = ref({
  totalPatients: 0,
  totalRecords: 0,
  totalProcesses: 0,
  todayProcesses: 0
});

// 获取主界面数据
const fetchMainData = async () => {
  try {
    const response = await getMainDashboard();
    
    console.log('主界面数据响应:', response);
    
    if (response.data) {
      // 更新治疗头状态
      availableHeads.value = response.data.availableHeads || 18;
      totalHeads.value = response.data.totalHeads || 20;
      
      // 更新系统信息
      if (response.data.systemInfo) {
        systemInfo.value = {
          systemName: response.data.systemInfo.systemName || 'FREEBONE医疗系统',
          version: response.data.systemInfo.version || '1.0.0',
          uptime: response.data.systemInfo.uptime || '0小时0分钟'
        };
      }
      
      // 更新数据概览
      if (response.data.dataOverview) {
        dataOverview.value = {
          totalPatients: response.data.dataOverview.totalPatients || 0,
          totalRecords: response.data.dataOverview.totalRecords || 0,
          totalProcesses: response.data.dataOverview.totalProcesses || 0,
          todayProcesses: response.data.dataOverview.todayProcesses || 0
        };
      }
      
      console.log('主界面数据更新成功:', {
        availableHeads: availableHeads.value,
        totalHeads: totalHeads.value,
        systemInfo: systemInfo.value,
        dataOverview: dataOverview.value
      });
    }
  } catch (error) {
    console.error('获取主界面数据失败:', error);
    // 使用默认值
    availableHeads.value = 18;
    totalHeads.value = 20;
  }
};

// 跳转到新建患者档案页面
const goToNewPatient = () => {
  router.push('/new-patient');
};

// 跳转到患者档案管理页面
const goToPatientManagement = () => {
  router.push('/patients');
};

// 跳转到进程管理页面
const goToProcessManagement = () => {
  router.push('/process-management');
};

// 跳转到治疗头管理页面
const goToTreatmentHeadManagement = () => {
  router.push('/treatment-head-management');
};

// 退出登录
const logout = () => {
  localStorage.removeItem('isAuthenticated');
  MessagePlugin.success('已退出登录');
  router.push('/login');
};

// 在script setup中添加测试函数

// 测试通知功能
const testTreatmentCompletedNotification = () => {
  // 模拟WebSocket消息
  const mockMessage = {
    type: 'TREATMENT_COMPLETED',
    data: {
      patientName: '张三'
    },
    timestamp: new Date().toISOString()
  };
  
  // 直接派发自定义事件
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

const testPickupReminderNotification = () => {
  // 模拟WebSocket消息
  const mockMessage = {
    type: 'PICKUP_REMINDER',
    data: {
      headNumbers: [1, 2, 3]
    },
    timestamp: new Date().toISOString()
  };
  
  // 直接派发自定义事件
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

const testResetHeadNotification = () => {
  // 模拟WebSocket消息
  const mockMessage = {
    type: 'RESET_HEAD',
    data: {
      headNumber: 99,
      compartmentType: '上仓(浅部)',
      slotNumber: 5,
      failureCount: 6,
      message: '请将上仓5号卡槽的治疗头复位'
    },
    timestamp: new Date().toISOString()
  };
  
  // 直接派发自定义事件
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

const testReinsertHeadNotification = () => {
  // 模拟WebSocket消息 - 失败次数≤5次时显示重新插入
  const failureCount = 3;
  const mockMessage = {
    type: 'REINSERT_HEAD',
    data: {
      headNumber: 99,
      compartmentType: '下仓(深部)',
      slotNumber: 8,
      failureCount: failureCount,
      message: `请重新插入下仓8号卡槽的治疗头（失败${failureCount}次）`
    },
    timestamp: new Date().toISOString()
  };
  
  // 直接派发自定义事件
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

// 测试重新插入 - 失败次数达到边界值
const testReinsertHeadBoundary = () => {
  const failureCount = 5;
  const mockMessage = {
    type: 'REINSERT_HEAD',
    data: {
      headNumber: 99,
      compartmentType: '上仓(浅部)',
      slotNumber: 3,
      failureCount: failureCount,
      message: `请重新插入上仓3号卡槽的治疗头（失败${failureCount}次）`
    },
    timestamp: new Date().toISOString()
  };
  
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

// 测试复位 - 失败次数超过5次
const testResetHeadHighFailure = () => {
  const failureCount = 8;
  const mockMessage = {
    type: 'RESET_HEAD',
    data: {
      headNumber: 99,
      compartmentType: '下仓(深部)',
      slotNumber: 7,
      failureCount: failureCount,
      message: `请将下仓7号卡槽的治疗头复位（失败${failureCount}次）`
    },
    timestamp: new Date().toISOString()
  };
  
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

// 测试本地治疗启动进度
const testLocalStartupProgress = () => {
  let progress = 0;
  const total = 5;
  let completed = 0;
  let failed = 0;
  
  const interval = setInterval(() => {
    progress += 20;
    
    if (progress <= 60) {
      completed = Math.floor(progress / 20);
      const mockMessage = {
        type: 'STARTUP_PROGRESS',
        data: {
          total: total,
          completed: completed,
          failed: failed,
          current: completed + 1,
          progress: progress,
          currentOperation: `正在启动治疗头 ${completed + 1}...`,
          failedHeads: []
        }
      };
      window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
    } else if (progress === 80) {
      // 模拟一个失败
      failed = 1;
      completed = 3;
      const mockMessage = {
        type: 'STARTUP_PROGRESS',
        data: {
          total: total,
          completed: completed,
          failed: failed,
          current: 5,
          progress: progress,
          currentOperation: '治疗头 4 启动失败，继续启动治疗头 5...',
          failedHeads: [{
            compartmentType: '上仓',
            slotNumber: 4,
            reason: '通信超时'
          }]
        }
      };
      window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
    } else if (progress >= 100) {
      completed = 4;
      failed = 1;
      const mockMessage = {
        type: 'STARTUP_PROGRESS',
        data: {
          total: total,
          completed: completed,
          failed: failed,
          current: 0,
          progress: 100,
          currentOperation: '治疗头启动完成！即将跳转...',
          failedHeads: [{
            compartmentType: '上仓',
            slotNumber: 4,
            reason: '通信超时'
          }]
        }
      };
      window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
      clearInterval(interval);
    }
  }, 1000);
};

// 测试远端治疗下载进度
const testRemoteDownloadProgress = () => {
  let progress = 0;
  const total = 6;
  let completed = 0;
  let failed = 0;
  
  const interval = setInterval(() => {
    progress += 16.7;
    
    if (progress <= 50) {
      completed = Math.floor(progress / 16.7);
      const mockMessage = {
        type: 'DOWNLOAD_PROGRESS',
        data: {
          total: total,
          completed: completed,
          failed: failed,
          current: completed + 1,
          progress: progress,
          currentOperation: `正在向治疗头 ${completed + 1} 下载参数...`,
          failedHeads: []
        }
      };
      window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
    } else if (progress >= 67 && progress < 84) {
      // 模拟两个失败
      failed = 2;
      completed = 2;
      const mockMessage = {
        type: 'DOWNLOAD_PROGRESS',
        data: {
          total: total,
          completed: completed,
          failed: failed,
          current: 5,
          progress: progress,
          currentOperation: '部分治疗头下载失败，继续下载其他治疗头...',
          failedHeads: [
            {
              compartmentType: '下仓',
              slotNumber: 7,
              reason: '参数校验失败'
            },
            {
              compartmentType: '上仓',
              slotNumber: 2,
              reason: '设备无响应'
            }
          ]
        }
      };
      window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
    } else if (progress >= 100) {
      completed = 4;
      failed = 2;
      const mockMessage = {
        type: 'DOWNLOAD_PROGRESS',
        data: {
          total: total,
          completed: completed,
          failed: failed,
          current: 0,
          progress: 100,
          currentOperation: '参数下载完成！即将跳转...',
          failedHeads: [
            {
              compartmentType: '下仓',
              slotNumber: 7,
              reason: '参数校验失败'
            },
            {
              compartmentType: '上仓',
              slotNumber: 2,
              reason: '设备无响应'
            }
          ]
        }
      };
      window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
      clearInterval(interval);
    }
  }, 800);
};

// 隐藏开发者模式逻辑
const clickCount = ref(0);
const showDevButtons = ref(false);
let clickTimer: number | null = null;

// 进度条测试相关
const showTestProgress = ref(false);
const testProgressData = ref({
  title: '',
  total: 0,
  completed: 0,
  failed: 0,
  progress: 0,
  currentOperation: '',
  failedHeads: [] as Array<{
    compartmentType: string,
    slotNumber: number,
    reason: string
  }>
});

// 监听进度条测试事件
const handleTestProgressNotification = (event: CustomEvent) => {
  const message = event.detail;
  if (message.type === 'STARTUP_PROGRESS' || message.type === 'DOWNLOAD_PROGRESS') {
    showTestProgress.value = true;
    testProgressData.value = {
      title: message.type === 'STARTUP_PROGRESS' ? '治疗头启动进度' : '参数下载进度',
      total: message.data.total || 0,
      completed: message.data.completed || 0,
      failed: message.data.failed || 0,
      progress: message.data.progress || 0,
      currentOperation: message.data.currentOperation || '',
      failedHeads: message.data.failedHeads || []
    };
    
    // 如果进度完成，3秒后自动隐藏
    if (message.data.progress >= 100) {
      setTimeout(() => {
        showTestProgress.value = false;
      }, 3000);
    }
  }
};

// 关闭测试进度条
const closeTestProgress = () => {
  showTestProgress.value = false;
  testProgressData.value = {
    title: '',
    total: 0,
    completed: 0,
    failed: 0,
    progress: 0,
    currentOperation: '',
    failedHeads: []
  };
};

const handleLogoClick = () => {
  clickCount.value++;
  
  // 清除之前的定时器
  if (clickTimer) {
    clearTimeout(clickTimer);
  }
  
  // 如果连续点击5次，显示开发者按钮
  if (clickCount.value >= 5) {
    showDevButtons.value = true;
    clickCount.value = 0;
    return;
  }
  
  // 设置超时重置计数器（2秒内没有点击就重置）
  clickTimer = setTimeout(() => {
    clickCount.value = 0;
  }, 2000);
};

// 页面加载时获取治疗头状态
onMounted(() => {
  fetchMainData();
  // 监听进度条测试事件
  window.addEventListener('mock-notification', handleTestProgressNotification as EventListener);
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (clickTimer) {
    clearTimeout(clickTimer);
  }
  // 清理事件监听器
  window.removeEventListener('mock-notification', handleTestProgressNotification as EventListener);
});
</script>

<template>
  <div class="page flex-col">
    <div class="group_1 flex-col">
      <div class="box_1 flex-col">
        <!-- 左上角返回按钮 -->
        
        <div class="back-button flex-row justify-between" @click="logout">
          </div>
        
        <!-- 隐藏的开发者测试按钮 (连续点击5次logo显示) -->
        <div v-if="showDevButtons" class="test-buttons" style="position: absolute; top: 20px; right: 200px; z-index: 999; display: flex; flex-wrap: wrap; max-width: 700px; background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 16px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3);">
          <!-- 通知测试 -->
          <div style="width: 100%; margin-bottom: 8px; font-size: 14px; font-weight: bold; color: #333; text-align: center;">
            📢 通知测试
          </div>
          <button @click="testTreatmentCompletedNotification" style="margin: 2px; padding: 5px 10px; background: #4CAF50; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            测试治疗完成
          </button>
          <button @click="testPickupReminderNotification" style="margin: 2px; padding: 5px 10px; background: #2196F3; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            测试待取回
          </button>
          
          <!-- 异常弹窗测试 -->
          <div style="width: 100%; margin: 8px 0 4px 0; font-size: 14px; font-weight: bold; color: #333; text-align: center;">
            ⚠️ 异常弹窗测试
          </div>
          <button @click="testReinsertHeadNotification" style="margin: 2px; padding: 5px 10px; background: #9c27b0; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            重新插入(3次)
          </button>
          <button @click="testReinsertHeadBoundary" style="margin: 2px; padding: 5px 10px; background: #e91e63; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            重新插入(5次)
          </button>
          <button @click="testResetHeadNotification" style="margin: 2px; padding: 5px 10px; background: #ff9800; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            复位治疗头(6次)
          </button>
          <button @click="testResetHeadHighFailure" style="margin: 2px; padding: 5px 10px; background: #ff5722; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            复位治疗头(8次)
          </button>
          
          <!-- 进度条测试 -->
          <div style="width: 100%; margin: 8px 0 4px 0; font-size: 14px; font-weight: bold; color: #333; text-align: center;">
            📊 进度条测试
          </div>
          <button @click="testLocalStartupProgress" style="margin: 2px; padding: 5px 10px; background: #00bcd4; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            本地启动进度
          </button>
          <button @click="testRemoteDownloadProgress" style="margin: 2px; padding: 5px 10px; background: #795548; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px;">
            远端下载进度
          </button>
          
          <!-- 控制按钮 -->
          <button @click="showDevButtons = false" style="margin: 8px 2px 2px 2px; padding: 5px 10px; background: #f44336; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 12px; width: 100%;">
            隐藏测试面板
          </button>
        </div>

        <!-- 右上角治疗头状态 -->
        <div class="treatment-status" @click="goToTreatmentHeadManagement">
          <span>{{ availableHeads }}/{{ totalHeads }}</span>
        </div>
        
        <img
          class="image_1"
          referrerpolicy="no-referrer"
          src="../assets/images/logo.png"
          @click="handleLogoClick"
        />
        <div class="image-wrapper_1 flex-row justify-between">
          <div class="function-button-container">
            <img
              class="image_2"
              referrerpolicy="no-referrer"
              src="../assets/images/new_patient.png"
              @click="goToNewPatient"
            />
          </div>
          <div class="function-button-container">
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="../assets/images/patient_management.png"
              @click="goToPatientManagement"
            />
          </div>
        </div>
        <div class="function-button-container center">
          <img
            class="image_4"
            referrerpolicy="no-referrer"
            src="../assets/images/process_management.png"
            @click="goToProcessManagement"
          />
        </div>
      </div>
    </div>
    
    <!-- 进度条测试区域 -->
    <div v-if="showTestProgress" class="test-progress-container">
      <div class="test-progress-content">
        <div class="progress-header">
          <h3>{{ testProgressData.title }}</h3>
          <button @click="closeTestProgress" class="close-btn">×</button>
        </div>
        
        <div class="progress-stats">
          <span class="stat-item total">总计: {{ testProgressData.total }}</span>
          <span class="stat-item completed">完成: {{ testProgressData.completed }}</span>
          <span class="stat-item failed">失败: {{ testProgressData.failed }}</span>
        </div>
        
        <div class="progress-bar-container">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: testProgressData.progress + '%' }"></div>
          </div>
          <span class="progress-percentage">{{ Math.round(testProgressData.progress) }}%</span>
        </div>
        
        <div v-if="testProgressData.currentOperation" class="current-operation">
          {{ testProgressData.currentOperation }}
        </div>
        
        <div v-if="testProgressData.failedHeads && testProgressData.failedHeads.length > 0" class="failed-heads">
          <div class="failed-header">
            <span class="failed-icon">⚠️</span>
            <span class="failed-title">失败的治疗头</span>
          </div>
          <div class="failed-list">
            <div v-for="(failedHead, index) in testProgressData.failedHeads" :key="index" class="failed-item">
              <span class="failed-location">{{ failedHead.compartmentType }}{{ failedHead.slotNumber }}号卡槽</span>
              <span class="failed-reason">{{ failedHead.reason }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.group_1 {
  height: 1080px;
  background: url('../assets/images/background.png')
    100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.box_1 {
  width: 1920px;
  height: 1080px;
  background: url('../assets/images/main_background.png')
    0px 0px no-repeat;
  background-size: 1920px 1083px;
  position: relative;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 100px;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  width: 241px;
  height: 70px;
  background: url(../assets/images/返回登录.png)
    0px 0px no-repeat;
  background-size: 197.0699999999997px 61px;
}

.group_11 {
  width: 33px;
  height: 30px;
  margin: 16px 0 0 20px;
}

.text_31 {
  width: 152px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 95px;
  margin: -15px 20px 0 70px;
}
.back-button:hover {
  transform: scale(1.05);
}

.treatment-status {
  position: absolute;
  top: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-size: 30px;
  font-weight: bold;
  z-index: 10;
  cursor: pointer;
}

.image_1 {
  width: 691px;
  height: 111px;
  margin: 155px 0 0 615px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image_1:hover {
  transform: scale(1.02);
}

.image-wrapper_1 {
  width: 1304px;
  height: 462px;
  margin: 148px 0 0 271px;
  position: relative;
}

.function-button-container {
  position: relative;
  overflow: visible;
  transition: all 0.3s ease;
}

.function-button-container:hover {
  transform: translateY(-8px);
}

.function-button-container:active {
  transform: translateY(2px);
}

.function-button-container.center {
  margin: 5px 0 62px 786px;
  width: 346px;
}

.image_2 {
  width: 663px;
  height: 462px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_2:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.0);
}

.image_3 {
  width: 611px;
  height: 420px;
  margin-top: 18px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_3:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.05);
}

.image_4 {
  width: 346px;
  height: 137px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_4:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.05);
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

/* 进度条测试区域样式 */
.test-progress-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background: rgba(0, 0, 0, 0.6);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-progress-content {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  padding: 24px;
  min-width: 500px;
  max-width: 600px;
  border: 2px solid #5cd4c8;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-header h3 {
  color: #2d3436;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.close-btn:hover {
  background: #e55656;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}

.stat-item {
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.stat-item.total {
  background: #e3f2fd;
  color: #1976d2;
}

.stat-item.completed {
  background: #e8f5e8;
  color: #388e3c;
}

.stat-item.failed {
  background: #ffebee;
  color: #d32f2f;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #5cd4c8 0%, #4ecdc4 50%, #45b7b8 100%);
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #2d3436;
  min-width: 45px;
  text-align: right;
}

.current-operation {
  text-align: center;
  font-size: 16px;
  color: #636e72;
  margin-bottom: 16px;
  padding: 10px;
  background: rgba(92, 212, 200, 0.1);
  border-radius: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.failed-heads {
  margin-top: 16px;
  border-top: 1px solid #dee2e6;
  padding-top: 16px;
}

.failed-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.failed-icon {
  font-size: 18px;
  margin-right: 8px;
}

.failed-title {
  font-size: 16px;
  font-weight: 600;
  color: #d63031;
}

.failed-list {
  max-height: 120px;
  overflow-y: auto;
}

.failed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  background: #fff5f5;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.failed-location {
  font-weight: 600;
  color: #d63031;
  font-size: 14px;
}

.failed-reason {
  font-size: 12px;
  color: #636e72;
  text-align: right;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
