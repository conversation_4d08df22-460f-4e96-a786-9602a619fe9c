<template>
  <svg :width="size" :height="size" :viewBox="`0 0 ${size} ${size}`" class="pie-chart">
    <!-- 剩余部分底层圆 -->
    <circle :cx="center" :cy="center" :r="radius" :fill="remainColor" />

    <!-- 使用部分扇形 -->
    <path :d="usedPath" fill="#FFFFFF" />

    <!-- 外围圆弧边框 -->
    <circle 
      :cx="center" 
      :cy="center" 
      :r="radius" 
      fill="none" 
      :stroke="remainColor" 
      :stroke-width="strokeWidth"
    />

    <!-- 中心文字 -->
    <text
      :x="center"
      :y="center"
      text-anchor="middle"
      dominant-baseline="middle"
      :font-size="fontSize"
      :fill="textColor"
    >
      {{ Math.round(usedPercent) }}%
    </text>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  usageCount: number;
  maxUsage?: number;
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  maxUsage: 500,
  size: 42
});

const center = computed(() => props.size / 2);
const radius = computed(() => (props.size - 4) / 2); // 减去描边宽度
const strokeWidth = computed(() => 2);
const fontSize = computed(() => props.size / 4);

const usedPercent = computed(() => Math.min(100, (props.usageCount / props.maxUsage) * 100));

const remainColor = computed(() => {
  if (usedPercent.value <= 60) return '#19C361';
  if (usedPercent.value <= 85) return '#FFA616';
  return '#FF0909';
});
const textColor = remainColor;

// 计算已使用扇形路径
const usedPath = computed(() => {
  if (usedPercent.value <= 0) return '';
  if (usedPercent.value >= 100) {
    // 整圆
    return `M ${center.value} ${center.value} m -${radius.value},0 a ${radius.value},${radius.value} 0 1,0 ${radius.value * 2},0 a ${radius.value},${radius.value} 0 1,0 -${radius.value * 2},0`;
  }
  const angle = (usedPercent.value / 100) * 360;
  const largeArc = angle > 180 ? 1 : 0;
  const radians = (angle - 90) * Math.PI / 180; // 起点在12点钟方向
  const x = center.value + radius.value * Math.cos(radians);
  const y = center.value + radius.value * Math.sin(radians);
  return `M ${center.value} ${center.value} L ${center.value} ${center.value - radius.value} A ${radius.value} ${radius.value} 0 ${largeArc} 1 ${x} ${y} Z`;
});
</script>

<style scoped>
.pie-chart {
  display: block;
}
</style> 