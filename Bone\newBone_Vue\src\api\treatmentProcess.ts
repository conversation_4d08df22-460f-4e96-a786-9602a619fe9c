import http from '@/utils/axios';

// 治疗进程相关的类型定义
export interface TreatmentDetailRequest {
  bodyPart: string;
  headNumber: number;
  duration: number;
  intensity: number;
  frequency: number;
  patchType: 'SHALLOW' | 'DEEP';
  patchQuantity: number;
}

export interface StartTreatmentRequest {
  recordId: number;
  treatmentMode: 'ON_SITE' | 'TAKE_AWAY';
  treatmentDetails: TreatmentDetailRequest[];
}

export interface StartTreatmentResponse {
  processId: number;
  status: string;
  startedHeads: number[];
  totalHeads: number;
  message: string;
}

export interface TreatmentDetailVO {
  detailId: number;
  bodyPart: string;
  headNumberUsed: number;
  duration: number;
  intensity: number;
  frequency: number;
  patchType: string;
  patchQuantity: number;
  status: string;
  statusDescription: string;
}

export interface TreatmentProcessVO {
  processId: number;
  patientName: string;
  patientCardId: string;
  treatmentMode: string;
  status: string;
  startTime: string;
  details: TreatmentDetailVO[];
}

// 治疗进程实时数据接口
export interface BodyPartTreatment {
  bodyPart: string;
  remainingTime: string;
  intensity: string;
  elapsedTime: string; // 已用时间字符串格式
  elapsedTimeSeconds: number; // 已用时间秒数
  totalDurationSeconds: number; // 总时长秒数
  remainingTimeSeconds: number; // 添加剩余时间秒数
}

export interface ProcessRealtimeResponse {
  patientName: string;
  treatmentMode: string;
  startTime: string; // 进程开始时间
  bodyParts: BodyPartTreatment[];
}

// 进程管理相关接口
export interface ProcessItem {
  processId: number;
  cardId: string;
  patientName: string;
  bodyPart: string;
  status: string;
}

export interface ProcessListResponse {
  processes: ProcessItem[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
  };
  statusOptions: string[];
}

// 进程管理相关API
export const getProcesses = (params?: {
  page?: number;
  size?: number;
  cardId?: string;
  patientName?: string;
  status?: string;
}) => {
  return http.get<ProcessListResponse>('/processes', { params });
};

// 获取治疗进程实时数据
export const getProcessRealtime = (processId: number) => {
  return http.get<ProcessRealtimeResponse>(`/processes/${processId}/realtime`);
};

// API调用函数

/**
 * 启动治疗进程
 */
export const startTreatmentProcess = async (request: StartTreatmentRequest) => {
  // 个性化提高超时：初始化/下发参数可能较久，这里放宽到60秒
  return await http.post<StartTreatmentResponse>(
    '/treatment-process/start',
    request,
    { timeout: 1200000 }
  );
};

/**
 * 获取治疗进程详情
 */
export const getTreatmentProcess = async (processId: number) => {
  return await http.get<TreatmentProcessVO>(`/treatment-process/${processId}`);
};

/**
 * 终止整个治疗进程
 */
export const terminateProcess = async (processId: number) => {
  return await http.post(`/treatment-process/${processId}/terminate`);
};

/**
 * 完成整个治疗进程
 */
export const completeProcess = async (processId: number) => {
  return await http.post(`/treatment-process/${processId}/complete`);
};

/**
 * 终止指定部位的治疗
 */
export const terminateDetail = async (detailId: number) => {
  return await http.post(`/treatment-process/detail/${detailId}/terminate`);
};

/**
 * 完成指定部位的治疗
 */
export const completeDetail = async (detailId: number) => {
  return await http.post(`/treatment-process/detail/${detailId}/complete`);
}; 
