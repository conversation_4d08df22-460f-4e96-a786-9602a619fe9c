package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.service.TreatmentHeadQuerySchedulerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 治疗头查询控制器
 * 提供手动触发查询和查询状态的API接口
 */
@RestController
@RequestMapping("/api/hardware/treatment-heads/query")
@CrossOrigin(origins = "*")
public class TreatmentHeadQueryController {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadQueryController.class);
    
    @Autowired
    private TreatmentHeadQuerySchedulerService querySchedulerService;
    
    /**
     * 触发开机启动查询
     * POST /api/hardware/treatment-heads/query/startup
     */
    @PostMapping("/startup")
    public ApiResponse<String> triggerStartupQuery() {
        try {
            querySchedulerService.triggerStartupQuery();
            logger.info("开机启动查询已触发");
            return ApiResponse.success("开机启动查询已触发", "查询将在下一个调度周期执行");
        } catch (Exception e) {
            logger.error("触发开机启动查询失败", e);
            return ApiResponse.error(500, "触发开机启动查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 触发进入档案查询
     * POST /api/hardware/treatment-heads/query/enter-record
     */
    @PostMapping("/enter-record")
    public ApiResponse<String> triggerEnterRecordQuery() {
        try {
            querySchedulerService.triggerEnterRecordQuery();
            logger.info("进入档案查询已触发");
            return ApiResponse.success("进入档案查询已触发", "查询将在下一个调度周期执行");
        } catch (Exception e) {
            logger.error("触发进入档案查询失败", e);
            return ApiResponse.error(500, "触发进入档案查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 触发确认创建患者查询
     * POST /api/hardware/treatment-heads/query/create-patient
     */
    @PostMapping("/create-patient")
    public ApiResponse<String> triggerCreatePatientQuery() {
        try {
            querySchedulerService.triggerCreatePatientQuery();
            logger.info("确认创建患者查询已触发");
            return ApiResponse.success("确认创建患者查询已触发", "查询将在下一个调度周期执行");
        } catch (Exception e) {
            logger.error("触发确认创建患者查询失败", e);
            return ApiResponse.error(500, "触发确认创建患者查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 立即执行查询
     * POST /api/hardware/treatment-heads/query/immediate
     */
    @PostMapping("/immediate")
    public ApiResponse<List<TreatmentHeadInfo>> executeImmediateQuery(@RequestParam(defaultValue = "手动触发") String reason) {
        try {
            List<TreatmentHeadInfo> headInfoList = querySchedulerService.executeImmediateQuery(reason);
            logger.info("立即查询执行成功，查询到 {} 个治疗头", headInfoList.size());
            return ApiResponse.success("立即查询执行成功", headInfoList);
        } catch (Exception e) {
            logger.error("立即查询执行失败", e);
            return ApiResponse.error(500, "立即查询执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取查询状态信息
     * GET /api/hardware/treatment-heads/query/status
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getQueryStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("isFullCompartment", querySchedulerService.isFullCompartment());
            status.put("currentHeadCount", querySchedulerService.getCurrentHeadCount());
            status.put("lastQueryTime", querySchedulerService.getLastQueryTime());
            status.put("lastFullCheckTime", querySchedulerService.getLastFullCheckTime());
            status.put("statusInfo", querySchedulerService.getQueryStatusInfo());
            
            return ApiResponse.success("查询状态获取成功", status);
        } catch (Exception e) {
            logger.error("获取查询状态失败", e);
            return ApiResponse.error(500, "获取查询状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置查询状态
     * POST /api/hardware/treatment-heads/query/reset
     */
    @PostMapping("/reset")
    public ApiResponse<String> resetQueryStatus() {
        try {
            querySchedulerService.resetQueryStatus();
            logger.info("查询状态已重置");
            return ApiResponse.success("查询状态已重置", "所有查询状态已恢复到初始值");
        } catch (Exception e) {
            logger.error("重置查询状态失败", e);
            return ApiResponse.error(500, "重置查询状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取查询配置信息
     * GET /api/hardware/treatment-heads/query/config
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getQueryConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("fullCompartmentInterval", "2分钟");
            config.put("partialCompartmentInterval", "30秒");
            config.put("totalTreatmentHeads", 20);
            config.put("schedulerInterval", "30秒");
            config.put("description", "智能查询策略：满仓时每2分钟查询，未满仓时每30秒查询");
            
            return ApiResponse.success("查询配置获取成功", config);
        } catch (Exception e) {
            logger.error("获取查询配置失败", e);
            return ApiResponse.error(500, "获取查询配置失败: " + e.getMessage());
        }
    }
}
