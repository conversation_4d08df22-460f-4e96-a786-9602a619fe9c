# FREEBONE医疗设备管理系统

**版本**: 1.0  
**状态**: 生产就绪  
**更新时间**: 2025-07-28

## 项目概述

FREEBONE医疗设备管理系统是一个基于Spring Boot的医疗设备管理系统，用于管理和控制FREEBONE医疗治疗设备。系统支持20个治疗头的实时监控、患者档案管理、治疗进程跟踪等功能。

## ✨ 核心功能

- 🏥 **患者档案管理** - 完整的患者信息和诊断记录管理
- 🔄 **治疗进程跟踪** - 实时监控治疗进程状态和进度
- 🎛️ **硬件设备控制** - 20个治疗头的完整控制和状态监控
- 📊 **实时数据同步** - 每10秒自动同步治疗头状态
- 🔐 **用户认证授权** - JWT认证和权限管理
- 🤖 **硬件模拟器** - 完整的硬件行为模拟，支持开发和测试

## 🚀 技术栈

- **后端框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0+
- **硬件通信**: 串口通信 (jSerialComm)
- **构建工具**: Gradle 8.x
- **Java版本**: JDK 17+

## 项目结构

```
src/main/java/com/Bone/BoneSys/
├── config/          # 配置类
│   ├── AppProperties.java      # 应用配置属性
│   ├── CorsConfig.java         # 跨域配置
│   ├── DatabaseConfig.java    # 数据库配置
│   └── SecurityConfig.java    # 安全配置
├── controller/      # 控制器层
│   └── HealthController.java  # 健康检查控制器
├── dto/            # 数据传输对象
│   ├── ApiResponse.java       # 统一API响应格式
│   └── PaginationInfo.java    # 分页信息
├── entity/         # JPA实体类
├── exception/      # 异常处理
│   ├── BaseException.java     # 基础异常类
│   └── SerialCommunicationException.java  # 串口通信异常
├── hardware/       # 硬件通信
├── repository/     # 数据访问层
├── service/        # 业务逻辑层
├── util/           # 工具类
│   └── Constants.java         # 系统常量
└── BoneSysApplication.java    # 主应用类
```

## 配置说明

### 数据库配置
```properties
spring.datasource.url=***********************************
spring.datasource.username=root
spring.datasource.password=123456
```

### 串口通信配置
```properties
serial.port.name=COM1
serial.port.baud-rate=115200
serial.port.data-bits=8
serial.port.stop-bits=1
serial.port.parity=0
```

### JWT配置
```properties
jwt.secret=BoneSysSecretKeyForJWTTokenGeneration2025
jwt.expiration=86400000
```

## 快速开始

### 1. 环境准备
- 安装Java JDK 17
- 安装MySQL 8.0
- 创建数据库 `bonesys`

### 2. 数据库初始化
执行 `SQL/build.sql` 脚本创建数据库表结构

### 3. 启动应用
```bash
./gradlew bootRun
```

### 4. 健康检查
访问 http://localhost:8080/api/health 检查系统状态

## API文档

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-01-26T10:00:00"
}
```

### 响应码说明
- 200: 成功
- 400: 请求参数错误
- 401: 未授权访问
- 404: 资源不存在
- 500: 服务器内部错误
- 1001: 硬件通信异常
- 1002: 治疗头不可用
- 1003: 患者信息重复

## 开发规范

### 包命名规范
- `controller`: REST API控制器
- `service`: 业务逻辑服务
- `repository`: 数据访问接口
- `entity`: JPA实体类
- `dto`: 数据传输对象
- `config`: 配置类
- `exception`: 异常处理
- `util`: 工具类
- `hardware`: 硬件通信

### 代码规范
- 使用Lombok减少样板代码
- 统一使用ApiResponse包装响应
- 异常处理使用全局异常处理器
- 数据库操作使用事务管理

## 部署说明

### 生产环境配置
1. 修改数据库连接配置
2. 配置串口设备权限
3. 设置JVM参数
4. 配置日志级别

### 监控和维护
- 系统健康检查：/api/health
- 应用日志监控
- 数据库性能监控
- 硬件设备状态监控

## 📚 文档

- **[项目部署和配置指南](项目部署和配置指南.md)** - 详细的部署和配置说明
- **[系统架构和技术说明](系统架构和技术说明.md)** - 系统架构和技术细节
- **[API接口文档](docx/API接口文档.md)** - 完整的API接口说明
- **[硬件通信协议](docx/硬件指令.md)** - 硬件通信协议详解
- **[API设计完整性检查报告](docx/API设计完整性检查报告.md)** - 系统完整性评估

## 📄 许可证

MIT License

## 📞 技术支持

- **问题反馈**: 通过GitHub Issues提交
- **技术文档**: 查看项目文档目录
- **开发指南**: 参考系统架构说明

---

**系统状态**: ✅ 生产就绪，可立即部署使用  
**最后更新**: 2025-07-28  
**维护状态**: 积极维护中