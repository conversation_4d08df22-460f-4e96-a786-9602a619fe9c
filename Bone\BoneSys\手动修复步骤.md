# 🔧 手动修复步骤指南

## 问题1: 网络连接问题

### 检查服务运行状态
```bash
# 检查端口占用
netstat -an | findstr :8080  # 后端端口
netstat -an | findstr :5173  # 前端端口
```

### 启动服务
```bash
# 启动后端 (在BoneSys目录)
./gradlew bootRun

# 启动前端 (在Bone_Vue目录)
npm run dev
```

## 问题2: 数据库compartment_type字段

### 检查字段是否存在
```sql
USE bonesys;
DESCRIBE treatment_heads;
```

### 添加字段（如果不存在）
```sql
ALTER TABLE treatment_heads 
ADD COLUMN compartment_type VARCHAR(10) NULL DEFAULT NULL 
COMMENT '仓位类型: SHALLOW(浅部上层), DEEP(深部下层)' 
AFTER slot_number;
```

### 更新数据
```sql
-- 设置浅部治疗头
UPDATE treatment_heads 
SET compartment_type = 'SHALLOW' 
WHERE head_number BETWEEN 1 AND 10;

-- 设置深部治疗头
UPDATE treatment_heads 
SET compartment_type = 'DEEP' 
WHERE head_number BETWEEN 11 AND 20;
```

### 验证数据
```sql
SELECT 
    head_number,
    compartment_type,
    CASE 
        WHEN compartment_type = 'SHALLOW' THEN '上层浅部'
        WHEN compartment_type = 'DEEP' THEN '下层深部'
        ELSE '未设置'
    END as description
FROM treatment_heads 
ORDER BY head_number;
```

## 问题3: API测试

### 测试健康检查
```bash
curl http://localhost:8080/api/health
```

### 测试治疗头数据
```bash
curl "http://localhost:8080/api/hardware/heads?page=1&size=5"
```

### 测试浅部贴片
```bash
curl -X POST http://localhost:8080/api/treatment-parameters/check-availability \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": "TEST001",
    "treatmentMode": "local",
    "bodyParts": [{
      "name": "手腕",
      "color": "#00FF00",
      "parameters": {
        "time": "10分钟",
        "intensity": "25",
        "frequency": "100",
        "depth": "浅部",
        "count": 2
      }
    }]
  }'
```

### 测试深部贴片
```bash
curl -X POST http://localhost:8080/api/treatment-parameters/check-availability \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": "TEST002",
    "treatmentMode": "local",
    "bodyParts": [{
      "name": "肩颈",
      "color": "#FF0000",
      "parameters": {
        "time": "15分钟",
        "intensity": "30",
        "frequency": "1000",
        "depth": "深部",
        "count": 3
      }
    }]
  }'
```

## 问题4: 定时同步检查

### 查看同步状态
```bash
curl http://localhost:8080/api/hardware/treatment-heads/sync/status
```

### 手动触发同步
```bash
curl -X POST http://localhost:8080/api/hardware/treatment-heads/sync/trigger
```

### 检查同步配置
查看 `application.properties` 文件中的配置：
```properties
treatment-head.sync.enabled=true
treatment-head.sync.interval=10000
```

## 问题5: 前端代理配置

### 检查vite.config.ts
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8080',
        changeOrigin: true,
        secure: false,
      }
    }
  }
})
```

### 检查axios配置
```typescript
// src/utils/axios.ts
const http = axios.create({
  baseURL: '/api', // 使用相对路径
  timeout: 10000,
});
```

## 验证步骤

### 1. 访问前端测试页面
```
http://localhost:5173/api-test
```

### 2. 检查数据库数据
```sql
SELECT 
    COUNT(CASE WHEN compartment_type = 'SHALLOW' THEN 1 END) as shallow_count,
    COUNT(CASE WHEN compartment_type = 'DEEP' THEN 1 END) as deep_count,
    COUNT(CASE WHEN compartment_type IS NULL THEN 1 END) as null_count
FROM treatment_heads;
```

### 3. 检查API响应
所有API应该返回HTTP状态码200，并包含正确的数据结构。

## 常见错误解决

### 错误1: Connection refused
- 检查后端是否在8080端口运行
- 检查防火墙设置

### 错误2: 404 Not Found
- 检查API路径是否正确
- 检查控制器映射

### 错误3: compartment_type为NULL
- 执行上述SQL更新语句
- 重启后端应用

### 错误4: 前端代理失败
- 重启前端开发服务器
- 检查vite.config.ts配置
- 清除浏览器缓存

## 成功标准

- ✅ 数据库中所有治疗头都有正确的compartment_type
- ✅ 所有API返回200状态码
- ✅ 浅部深部贴片能正确识别对应的治疗头
- ✅ 定时同步每10秒正常运行
- ✅ 前端能正常访问所有后端API

完成以上步骤后，系统应该能正常工作！
