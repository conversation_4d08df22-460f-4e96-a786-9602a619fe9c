# 硬件通信配置示例
# 复制到 application.yml 或 application-{profile}.yml 中使用

hardware:
  communication:
    # 通信模式：websocket 或 serial
    mode: websocket
    
    # WebSocket配置（远程硬件模式）
    websocket:
      url: ws://localhost:8081/hardware
      reconnect: true
      timeout: 5000
      max-reconnect-attempts: 10
      reconnect-interval: 3000
    
    # 串口配置（本地硬件模式）
    serial:
      port: COM3
      baud-rate: 115200
      data-bits: 8
      stop-bits: 1
      parity: NONE
      timeout: 3000
      auto-detect-port: true

---
# 开发环境配置（WebSocket模式）
spring:
  profiles: dev

hardware:
  communication:
    mode: websocket
    websocket:
      url: ws://localhost:8081/hardware

---
# 生产环境配置（串口模式）
spring:
  profiles: prod

hardware:
  communication:
    mode: serial
    serial:
      port: COM3
      baud-rate: 115200

---
# 测试环境配置（模拟模式）
spring:
  profiles: test

hardware:
  communication:
    mode: websocket
    websocket:
      url: ws://localhost:8081/hardware
      timeout: 1000
