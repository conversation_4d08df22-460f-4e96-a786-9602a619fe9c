package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Integer> {
    
    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 根据用户名更新最后登录时间
     */
    @Query("UPDATE User u SET u.lastUpdatedAt = CURRENT_TIMESTAMP WHERE u.username = :username")
    void updateLastLoginTime(@Param("username") String username);
    
    /**
     * 查找唯一的用户（系统只有一个用户）
     */
    @Query("SELECT u FROM User u ORDER BY u.id LIMIT 1")
    Optional<User> findTheOnlyUser();
}