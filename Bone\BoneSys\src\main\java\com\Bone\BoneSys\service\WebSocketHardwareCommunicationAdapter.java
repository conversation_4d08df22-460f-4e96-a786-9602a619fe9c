package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * WebSocket硬件通信适配器
 * 将WebSocketHardwareService适配到统一接口
 */
@Service
@ConditionalOnProperty(name = "hardware.communication.mode", havingValue = "websocket")
public class WebSocketHardwareCommunicationAdapter implements HardwareCommunicationInterface {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketHardwareCommunicationAdapter.class);
    
    @Autowired
    private WebSocketHardwareService webSocketHardwareService;
    
    @Override
    public String sendCommand(String command) throws SerialCommunicationException {
        logger.debug("Sending WebSocket command: {}", command.trim());
        return webSocketHardwareService.sendCommand(command);
    }
    
    @Override
    public String sendCommand(String command, String layer) throws SerialCommunicationException {
        logger.debug("Sending WebSocket command to {} layer: {}", layer, command.trim());
        return webSocketHardwareService.sendCommand(command, layer);
    }
    
    @Override
    public boolean isConnected() {
        return webSocketHardwareService.isConnected();
    }
    
    @Override
    public void connect() throws SerialCommunicationException {
        logger.info("Attempting to connect via WebSocket communication...");
        webSocketHardwareService.connect();
        logger.info("WebSocket communication connected successfully");
    }
    
    @Override
    public void disconnect() {
        logger.info("Disconnecting WebSocket communication...");
        webSocketHardwareService.disconnect();
        logger.info("WebSocket communication disconnected");
    }
    
    @Override
    public String getCommunicationType() {
        return "WEBSOCKET";
    }
} 