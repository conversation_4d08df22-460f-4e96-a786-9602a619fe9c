-- FREEBONE医疗系统 - 数据库验证脚本
-- 用于验证数据库重建后的数据完整性

USE bonesys;

-- ========================================
-- 1. 基础数据验证
-- ========================================

SELECT '=== 基础数据统计 ===' as info;

SELECT 
    '治疗头总数' as item,
    COUNT(*) as count,
    '应为20' as expected
FROM treatment_heads
UNION ALL
SELECT 
    '治疗参数预设总数' as item,
    COUNT(*) as count,
    '应为10' as expected
FROM treatment_parameter_presets
UNION ALL
SELECT 
    '用户总数' as item,
    COUNT(*) as count,
    '应为1' as expected
FROM users;

-- ========================================
-- 2. 治疗头数据验证
-- ========================================

SELECT '=== 治疗头初始状态验证 ===' as info;

-- 检查治疗头编号完整性
SELECT 
    '治疗头编号范围' as check_item,
    MIN(head_number) as min_number,
    MAX(head_number) as max_number,
    '应为1-20' as expected
FROM treatment_heads;

-- 检查初始状态分布
SELECT 
    realtime_status as status,
    COUNT(*) as count,
    CASE 
        WHEN realtime_status = 'TREATING' THEN '✓ 正确（初始状态）'
        ELSE '⚠ 需要检查'
    END as validation
FROM treatment_heads
GROUP BY realtime_status;

-- 检查槽位号初始状态
SELECT 
    '空槽位号数量' as check_item,
    COUNT(*) as count,
    '应为20（初始状态）' as expected
FROM treatment_heads
WHERE slot_number IS NULL;

-- 检查电量初始状态
SELECT 
    '空电量数量' as check_item,
    COUNT(*) as count,
    '应为20（初始状态）' as expected
FROM treatment_heads
WHERE battery_level IS NULL;

-- ========================================
-- 3. 治疗参数预设验证
-- ========================================

SELECT '=== 治疗参数预设验证 ===' as info;

-- 检查预设名称和部位
SELECT 
    preset_name,
    body_part,
    recommended_count,
    min_patch_count,
    max_patch_count,
    CASE 
        WHEN min_patch_count = 1 AND max_patch_count = 6 THEN '✓ 正确'
        ELSE '⚠ 需要检查'
    END as patch_count_validation
FROM treatment_parameter_presets
ORDER BY preset_name, body_part;

-- 检查默认预设
SELECT 
    '默认预设数量' as check_item,
    COUNT(*) as count,
    '应为6' as expected
FROM treatment_parameter_presets
WHERE is_default = TRUE;

-- ========================================
-- 4. 约束验证
-- ========================================

SELECT '=== 数据库约束验证 ===' as info;

-- 检查治疗头编号唯一性
SELECT 
    '重复的治疗头编号' as check_item,
    COUNT(*) as count,
    '应为0' as expected
FROM (
    SELECT head_number
    FROM treatment_heads
    GROUP BY head_number
    HAVING COUNT(*) > 1
) as duplicates;

-- 检查预设名称和部位组合唯一性
SELECT 
    '重复的预设组合' as check_item,
    COUNT(*) as count,
    '应为0' as expected
FROM (
    SELECT preset_name, body_part
    FROM treatment_parameter_presets
    GROUP BY preset_name, body_part
    HAVING COUNT(*) > 1
) as duplicates;

-- ========================================
-- 5. 详细数据展示
-- ========================================

SELECT '=== 治疗头详细数据 ===' as info;

SELECT 
    head_number as 编号,
    slot_number as 槽位,
    realtime_status as 状态,
    battery_level as 电量,
    total_usage_count as 使用次数,
    max_usage_count as 最大次数
FROM treatment_heads
ORDER BY head_number;

SELECT '=== 验证完成 ===' as info;

SELECT 
    '数据库验证完成' as message,
    NOW() as timestamp,
    '如果所有检查项都正确，数据库已准备就绪' as note;
