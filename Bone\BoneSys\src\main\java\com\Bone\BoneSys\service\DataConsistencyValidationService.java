package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.EnhancedCandidateItem;
import com.Bone.BoneSys.dto.EnhancedCandidateListResponse;
import com.Bone.BoneSys.entity.BodyPartStat;
import com.Bone.BoneSys.entity.Patient;
import com.Bone.BoneSys.entity.Record;
import com.Bone.BoneSys.repository.BodyPartStatRepository;
import com.Bone.BoneSys.repository.PatientRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据一致性验证服务
 * 用于验证前端显示的数据与数据库中的实际数据是否一致
 */
@Service
@Slf4j
public class DataConsistencyValidationService {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;
    
    @Autowired
    private PatientDataAggregationService patientDataAggregationService;
    
    /**
     * 验证所有候选患者数据的一致性
     */
    public Map<String, Object> validateAllCandidatesConsistency() {
        log.info("开始验证所有候选患者数据一致性");
        
        try {
            // 获取前端API返回的数据
            Pageable pageable = PageRequest.of(0, 100); // 验证前100条数据
            EnhancedCandidateListResponse apiResponse = patientDataAggregationService
                    .getPatientCandidatesWithStats(null, pageable);
            
            List<Map<String, Object>> validationResults = new ArrayList<>();
            int totalValidated = 0;
            int consistentCount = 0;
            int inconsistentCount = 0;
            
            for (EnhancedCandidateItem candidate : apiResponse.getCandidates()) {
                Map<String, Object> result = validateSinglePatientConsistency(candidate);
                validationResults.add(result);
                totalValidated++;
                
                if ((Boolean) result.get("isConsistent")) {
                    consistentCount++;
                } else {
                    inconsistentCount++;
                }
            }
            
            // 生成验证报告
            Map<String, Object> report = new HashMap<>();
            report.put("totalValidated", totalValidated);
            report.put("consistentCount", consistentCount);
            report.put("inconsistentCount", inconsistentCount);
            report.put("consistencyRate", totalValidated > 0 ? 
                    String.format("%.2f%%", (consistentCount * 100.0 / totalValidated)) : "0%");
            report.put("validationResults", validationResults);
            report.put("summary", generateValidationSummary(validationResults));
            
            log.info("数据一致性验证完成: 总计{}条, 一致{}条, 不一致{}条, 一致性率{}",
                    totalValidated, consistentCount, inconsistentCount, report.get("consistencyRate"));
            
            return report;
            
        } catch (Exception e) {
            log.error("验证数据一致性时发生异常", e);
            return Map.of("error", "验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证单个患者数据的一致性
     */
    public Map<String, Object> validateSinglePatientConsistency(EnhancedCandidateItem candidate) {
        Map<String, Object> result = new HashMap<>();
        result.put("patientId", candidate.getPatientId());
        result.put("name", candidate.getName());
        
        List<String> inconsistencies = new ArrayList<>();
        
        try {
            // 从数据库获取原始数据
            Patient patient = patientRepository.findById(candidate.getPatientId()).orElse(null);
            if (patient == null) {
                inconsistencies.add("患者在数据库中不存在");
                result.put("isConsistent", false);
                result.put("inconsistencies", inconsistencies);
                return result;
            }
            
            // 验证患者基本信息
            validateBasicInfo(candidate, patient, inconsistencies);
            
            // 验证就诊时间
            validateVisitTime(candidate, patient, inconsistencies);
            
            // 验证治疗部位和次数
            validateTreatmentData(candidate, patient, inconsistencies);
            
            result.put("isConsistent", inconsistencies.isEmpty());
            result.put("inconsistencies", inconsistencies);
            result.put("databaseData", createDatabaseDataSummary(patient));
            result.put("apiData", createApiDataSummary(candidate));
            
        } catch (Exception e) {
            log.error("验证患者{}数据一致性时发生异常", candidate.getPatientId(), e);
            inconsistencies.add("验证过程中发生异常: " + e.getMessage());
            result.put("isConsistent", false);
            result.put("inconsistencies", inconsistencies);
        }
        
        return result;
    }
    
    /**
     * 验证患者基本信息
     */
    private void validateBasicInfo(EnhancedCandidateItem candidate, Patient patient, List<String> inconsistencies) {
        // 验证姓名
        if (!candidate.getName().equals(patient.getName())) {
            inconsistencies.add(String.format("姓名不一致: API返回'%s', 数据库为'%s'", 
                    candidate.getName(), patient.getName()));
        }
        
        // 验证就诊卡号
        if (!candidate.getCardId().equals(patient.getPatientCardId())) {
            inconsistencies.add(String.format("就诊卡号不一致: API返回'%s', 数据库为'%s'", 
                    candidate.getCardId(), patient.getPatientCardId()));
        }
        
        // 验证年龄
        String expectedAge = patient.getAge();
        if (expectedAge != null && !expectedAge.endsWith("岁")) {
            expectedAge += "岁";
        }
        if (!candidate.getAge().equals(expectedAge)) {
            inconsistencies.add(String.format("年龄不一致: API返回'%s', 数据库为'%s'", 
                    candidate.getAge(), expectedAge));
        }
        
        // 验证性别
        if (!candidate.getGender().equals(patient.getGender())) {
            inconsistencies.add(String.format("性别不一致: API返回'%s', 数据库为'%s'", 
                    candidate.getGender(), patient.getGender()));
        }
    }
    
    /**
     * 验证就诊时间
     */
    private void validateVisitTime(EnhancedCandidateItem candidate, Patient patient, List<String> inconsistencies) {
        List<Record> records = recordRepository.findByPatientId(patient.getId());
        
        if (records.isEmpty()) {
            if (!"无档案".equals(candidate.getVisitTime())) {
                inconsistencies.add(String.format("就诊时间不一致: 患者无档案记录，但API返回'%s'", 
                        candidate.getVisitTime()));
            }
        } else {
            // 获取最新的就诊时间
            String expectedVisitTime = records.stream()
                    .map(Record::getCreatedAt)
                    .max(java.time.LocalDate::compareTo)
                    .map(date -> date.format(DATE_FORMATTER))
                    .orElse("无档案");
            
            if (!candidate.getVisitTime().equals(expectedVisitTime)) {
                inconsistencies.add(String.format("就诊时间不一致: API返回'%s', 数据库最新为'%s'", 
                        candidate.getVisitTime(), expectedVisitTime));
            }
        }
    }
    
    /**
     * 验证治疗数据
     */
    private void validateTreatmentData(EnhancedCandidateItem candidate, Patient patient, List<String> inconsistencies) {
        List<BodyPartStat> bodyPartStats = bodyPartStatRepository.findByPatientId(patient.getId());
        
        if (bodyPartStats.isEmpty()) {
            if (!"待确定".equals(candidate.getTreatmentParts())) {
                inconsistencies.add(String.format("治疗部位不一致: 患者无治疗记录，但API返回'%s'", 
                        candidate.getTreatmentParts()));
            }
            if (candidate.getTotalSessions() != 0) {
                inconsistencies.add(String.format("治疗次数不一致: 患者无治疗记录，但API返回%d次", 
                        candidate.getTotalSessions()));
            }
        } else {
            // 验证治疗部位
            String expectedParts = bodyPartStats.stream()
                    .map(BodyPartStat::getBodyPart)
                    .filter(part -> part != null && !part.trim().isEmpty())
                    .distinct()
                    .collect(Collectors.joining(", "));
            
            if (expectedParts.isEmpty()) {
                expectedParts = "待确定";
            }
            
            if (!candidate.getTreatmentParts().equals(expectedParts)) {
                inconsistencies.add(String.format("治疗部位不一致: API返回'%s', 数据库为'%s'", 
                        candidate.getTreatmentParts(), expectedParts));
            }
            
            // 验证治疗次数
            int expectedSessions = bodyPartStats.stream()
                    .mapToInt(stat -> stat.getTotalUsageCount() != null ? stat.getTotalUsageCount() : 0)
                    .sum();
            
            if (!candidate.getTotalSessions().equals(expectedSessions)) {
                inconsistencies.add(String.format("治疗次数不一致: API返回%d次, 数据库为%d次", 
                        candidate.getTotalSessions(), expectedSessions));
            }
        }
    }
    
    /**
     * 创建数据库数据摘要
     */
    private Map<String, Object> createDatabaseDataSummary(Patient patient) {
        List<Record> records = recordRepository.findByPatientId(patient.getId());
        List<BodyPartStat> bodyPartStats = bodyPartStatRepository.findByPatientId(patient.getId());
        
        return Map.of(
                "name", patient.getName(),
                "cardId", patient.getPatientCardId(),
                "age", patient.getAge(),
                "gender", patient.getGender(),
                "recordCount", records.size(),
                "bodyPartStatCount", bodyPartStats.size(),
                "latestVisitTime", records.stream()
                        .map(Record::getCreatedAt)
                        .max(java.time.LocalDate::compareTo)
                        .map(date -> date.format(DATE_FORMATTER))
                        .orElse("无档案"),
                "totalSessions", bodyPartStats.stream()
                        .mapToInt(stat -> stat.getTotalUsageCount() != null ? stat.getTotalUsageCount() : 0)
                        .sum()
        );
    }
    
    /**
     * 创建API数据摘要
     */
    private Map<String, Object> createApiDataSummary(EnhancedCandidateItem candidate) {
        return Map.of(
                "name", candidate.getName(),
                "cardId", candidate.getCardId(),
                "age", candidate.getAge(),
                "gender", candidate.getGender(),
                "visitTime", candidate.getVisitTime(),
                "treatmentParts", candidate.getTreatmentParts(),
                "totalSessions", candidate.getTotalSessions()
        );
    }
    
    /**
     * 生成验证摘要
     */
    private Map<String, Object> generateValidationSummary(List<Map<String, Object>> validationResults) {
        Map<String, Integer> inconsistencyTypes = new HashMap<>();
        
        for (Map<String, Object> result : validationResults) {
            @SuppressWarnings("unchecked")
            List<String> inconsistencies = (List<String>) result.get("inconsistencies");
            
            for (String inconsistency : inconsistencies) {
                String type = extractInconsistencyType(inconsistency);
                inconsistencyTypes.put(type, inconsistencyTypes.getOrDefault(type, 0) + 1);
            }
        }
        
        return Map.of(
                "commonInconsistencyTypes", inconsistencyTypes,
                "mostCommonIssue", inconsistencyTypes.entrySet().stream()
                        .max(Map.Entry.comparingByValue())
                        .map(Map.Entry::getKey)
                        .orElse("无"),
                "totalIssueTypes", inconsistencyTypes.size()
        );
    }
    
    /**
     * 提取不一致类型
     */
    private String extractInconsistencyType(String inconsistency) {
        if (inconsistency.contains("姓名不一致")) return "姓名不一致";
        if (inconsistency.contains("就诊卡号不一致")) return "就诊卡号不一致";
        if (inconsistency.contains("年龄不一致")) return "年龄不一致";
        if (inconsistency.contains("性别不一致")) return "性别不一致";
        if (inconsistency.contains("就诊时间不一致")) return "就诊时间不一致";
        if (inconsistency.contains("治疗部位不一致")) return "治疗部位不一致";
        if (inconsistency.contains("治疗次数不一致")) return "治疗次数不一致";
        return "其他";
    }
    
    /**
     * 修复发现的数据不一致问题
     */
    public Map<String, Object> fixDataInconsistencies(List<Long> patientIds) {
        log.info("开始修复数据不一致问题，患者ID列表: {}", patientIds);
        
        int fixedCount = 0;
        int failedCount = 0;
        List<String> fixResults = new ArrayList<>();
        
        for (Long patientId : patientIds) {
            try {
                // 这里可以添加具体的修复逻辑
                // 例如：重新计算统计数据、更新缓存等
                log.info("修复患者{}的数据不一致问题", patientId);
                fixedCount++;
                fixResults.add("患者" + patientId + ": 修复成功");
            } catch (Exception e) {
                log.error("修复患者{}数据失败", patientId, e);
                failedCount++;
                fixResults.add("患者" + patientId + ": 修复失败 - " + e.getMessage());
            }
        }
        
        return Map.of(
                "totalProcessed", patientIds.size(),
                "fixedCount", fixedCount,
                "failedCount", failedCount,
                "fixResults", fixResults
        );
    }
}