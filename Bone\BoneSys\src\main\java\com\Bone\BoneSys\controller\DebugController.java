package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.context.annotation.Profile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器 - 用于调试治疗头数据获取问题
 * 临时接口，用于排查数据不一致问题
 */
@RestController
@RequestMapping("/api/debug")
@Profile("dev")
public class DebugController {
    
    private static final Logger logger = LoggerFactory.getLogger(DebugController.class);
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    /**
     * 调试治疗头数据获取
     * GET /api/debug/treatment-heads
     */
    @GetMapping("/hardware-treatment-heads")
    public ApiResponse<Map<String, Object>> debugTreatmentHeads() {
        try {
            logger.info("Debug: Getting treatment heads data...");
            
            Map<String, Object> debugData = new HashMap<>();
            
            // 1. 直接从数据库获取原始数据
            List<TreatmentHead> dbHeads = treatmentHeadRepository.findAll();
            debugData.put("database_raw_data", dbHeads);
            logger.info("Debug: Found {} treatment heads in database", dbHeads.size());
            
            // 2. 通过HardwareService获取转换后的数据
            List<TreatmentHeadInfo> serviceHeads = hardwareService.getAllTreatmentHeadsForManagement();
            debugData.put("service_converted_data", serviceHeads);
            logger.info("Debug: HardwareService returned {} treatment heads", serviceHeads.size());
            
            // 3. 对比数据差异
            Map<String, Object> comparison = new HashMap<>();
            for (int i = 0; i < Math.min(dbHeads.size(), serviceHeads.size()); i++) {
                TreatmentHead dbHead = dbHeads.get(i);
                TreatmentHeadInfo serviceHead = serviceHeads.get(i);
                
                Map<String, Object> headComparison = new HashMap<>();
                headComparison.put("head_number", dbHead.getHeadNumber() + " vs " + serviceHead.getHeadNumber());
                headComparison.put("battery_level", 
                    (dbHead.getBatteryLevel() != null ? dbHead.getBatteryLevel() : "NULL") + 
                    " vs " + serviceHead.getBatteryLevel());
                headComparison.put("status", 
                    (dbHead.getRealtimeStatus() != null ? dbHead.getRealtimeStatus().name() : "NULL") + 
                    " vs " + serviceHead.getStatus());
                headComparison.put("slot_number", 
                    (dbHead.getSlotNumber() != null ? dbHead.getSlotNumber() : "NULL") + 
                    " vs " + serviceHead.getSlotNumber());
                headComparison.put("compartment_type", 
                    (dbHead.getCompartmentType() != null ? dbHead.getCompartmentType() : "NULL") + 
                    " vs " + serviceHead.getCompartmentType());
                
                comparison.put("head_" + dbHead.getHeadNumber(), headComparison);
            }
            debugData.put("data_comparison", comparison);
            
            // 4. 检查特定问题
            Map<String, Object> issues = new HashMap<>();
            long nullBatteryCount = dbHeads.stream().mapToLong(h -> h.getBatteryLevel() == null ? 1 : 0).sum();
            long nullStatusCount = dbHeads.stream().mapToLong(h -> h.getRealtimeStatus() == null ? 1 : 0).sum();
            long zeroBatteryInService = serviceHeads.stream().mapToLong(h -> h.getBatteryLevel() == 0 ? 1 : 0).sum();
            long nullStatusInService = serviceHeads.stream().mapToLong(h -> h.getStatus() == null ? 1 : 0).sum();
            
            issues.put("null_battery_in_db", nullBatteryCount);
            issues.put("null_status_in_db", nullStatusCount);
            issues.put("zero_battery_in_service", zeroBatteryInService);
            issues.put("null_status_in_service", nullStatusInService);
            debugData.put("issues_summary", issues);
            
            return ApiResponse.success("Debug data retrieved successfully", debugData);
            
        } catch (Exception e) {
            logger.error("Debug: Failed to get treatment heads data", e);
            return ApiResponse.serverError("Debug failed: " + e.getMessage());
        }
    }
    
    /**
     * 强制刷新治疗头数据
     * POST /api/debug/refresh-treatment-heads
     */
    @GetMapping("/refresh-treatment-heads")
    public ApiResponse<String> refreshTreatmentHeads() {
        try {
            logger.info("Debug: Forcing refresh of treatment heads data...");
            
            // 强制清除可能的缓存
            treatmentHeadRepository.flush();
            
            // 重新获取数据
            List<TreatmentHead> refreshedHeads = treatmentHeadRepository.findAll();
            
            logger.info("Debug: Refreshed {} treatment heads", refreshedHeads.size());
            
            return ApiResponse.success("Treatment heads data refreshed successfully", 
                "Refreshed " + refreshedHeads.size() + " treatment heads");
            
        } catch (Exception e) {
            logger.error("Debug: Failed to refresh treatment heads data", e);
            return ApiResponse.serverError("Refresh failed: " + e.getMessage());
        }
    }
}
