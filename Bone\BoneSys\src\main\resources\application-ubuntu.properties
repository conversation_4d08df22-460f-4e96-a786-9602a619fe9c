# Ubuntu生产环境配置

# 硬件通信模式配置
hardware.communication.mode=serial

# 硬件模拟器配置（生产环境禁用）
hardware.simulator.enabled=false

# 双层串口配置 (Ubuntu生产环境)
# 生产模式：启用真实串口连接
hardware.serial.enabled=true
hardware.serial.dev-mode=false
serial.port.upper=/dev/ttysWK2
serial.port.lower=/dev/ttysWK3
serial.port.auto-detect=true
serial.port.baud-rate=115200
serial.port.data-bits=8
serial.port.stop-bits=1
serial.port.parity=0
serial.port.timeout=5000

# 治疗头自动同步配置
treatment-head.sync.enabled=true
treatment-head.sync.interval=10000
treatment-head.sync.startup-delay=5000
treatment-head.sync.timeout=5000
treatment-head.sync.retry-attempts=3
treatment-head.sync.retry-delay=2000

# 日志配置
logging.level.com.Bone.BoneSys=INFO
logging.level.com.Bone.BoneSys.service.DualLayerSerialCommunicationService=DEBUG
logging.level.com.Bone.BoneSys.service.SerialHardwareCommunicationAdapter=DEBUG
logging.level.com.Bone.BoneSys.service.HardwareService=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# 性能优化配置
hardware.performance.metrics-report.interval=300000
hardware.performance.cleanup.interval=30000 