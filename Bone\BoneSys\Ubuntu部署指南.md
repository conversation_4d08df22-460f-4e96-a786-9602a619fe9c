# 🐧 Ubuntu版本编译和部署指南

## 📋 概述

本指南详细说明如何编译和部署适用于Ubuntu环境的串口版本系统。

## 🔧 编译过程

### 1. **编译命令（Windows环境）**

```bash
# 进入后端项目目录
cd BoneSys

# 清理并编译项目
.\gradlew.bat clean build -x test

# 或者包含测试的完整构建
.\gradlew.bat clean build
```

### 2. **编译命令（Linux/Ubuntu环境）**

```bash
# 进入后端项目目录
cd BoneSys

# 确保gradlew有执行权限
chmod +x gradlew

# 清理并编译项目
./gradlew clean build -x test

# 或者包含测试的完整构建
./gradlew clean build
```

### 3. **编译输出**

编译成功后，JAR文件位置：
```
BoneSys/build/libs/BoneSys-0.0.1-SNAPSHOT.jar
```

## 🚀 部署到Ubuntu

### 1. **系统准备**

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Java 17
sudo apt install openjdk-17-jdk -y

# 验证Java版本
java -version
```

### 2. **创建部署目录**

```bash
# 创建应用目录
sudo mkdir -p /opt/bonesys
sudo chown $USER:$USER /opt/bonesys

# 创建日志目录
sudo mkdir -p /var/log/bonesys
sudo chown $USER:$USER /var/log/bonesys

# 创建配置目录
mkdir -p /opt/bonesys/config
```

### 3. **复制文件**

```bash
# 复制JAR文件
cp BoneSys/build/libs/BoneSys-0.0.1-SNAPSHOT.jar /opt/bonesys/

# 复制Ubuntu配置文件
cp BoneSys/src/main/resources/application-ubuntu.properties /opt/bonesys/config/
```

### 4. **配置串口权限**

```bash
# 将用户添加到dialout组（串口访问权限）
sudo usermod -a -G dialout $USER

# 检查串口设备
ls -la /dev/ttysWK* /dev/ttyUSB* /dev/ttyACM*

# 设置串口权限（如果需要）
sudo chmod 666 /dev/ttysWK2 /dev/ttysWK3
```

## ▶️ 启动方式

### 1. **直接启动（开发/测试）**

```bash
# 基本启动
java -jar /opt/bonesys/BoneSys-0.0.1-SNAPSHOT.jar --spring.profiles.active=ubuntu

# 指定配置文件启动
java -jar /opt/bonesys/BoneSys-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=ubuntu \
  --spring.config.location=/opt/bonesys/config/

# 指定端口启动
java -jar /opt/bonesys/BoneSys-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=ubuntu \
  --server.port=8080
```

### 2. **后台启动**

```bash
# 后台启动并输出日志
nohup java -jar /opt/bonesys/BoneSys-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=ubuntu \
  > /var/log/bonesys/app.log 2>&1 &

# 查看进程
ps aux | grep BoneSys

# 查看日志
tail -f /var/log/bonesys/app.log
```

### 3. **系统服务（推荐生产环境）**

创建systemd服务文件：

```bash
sudo nano /etc/systemd/system/bonesys.service
```

服务文件内容：
```ini
[Unit]
Description=Bone Medical System
After=network.target

[Service]
Type=simple
User=bonesys
WorkingDirectory=/opt/bonesys
ExecStart=/usr/bin/java -jar /opt/bonesys/BoneSys-0.0.1-SNAPSHOT.jar --spring.profiles.active=ubuntu
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=bonesys

[Install]
WantedBy=multi-user.target
```

启用和启动服务：
```bash
# 创建专用用户
sudo useradd -r -s /bin/false bonesys
sudo chown -R bonesys:bonesys /opt/bonesys
sudo chown -R bonesys:bonesys /var/log/bonesys

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable bonesys

# 启动服务
sudo systemctl start bonesys

# 查看状态
sudo systemctl status bonesys

# 查看日志
sudo journalctl -u bonesys -f
```

## ⚙️ 关键配置差异

### Ubuntu配置 (`application-ubuntu.properties`)
```properties
# 串口通信模式
hardware.communication.mode=serial

# 禁用硬件模拟器
hardware.simulator.enabled=false

# Ubuntu串口配置
serial.port.upper=/dev/ttysWK2
serial.port.lower=/dev/ttysWK3
serial.port.auto-detect=true
```

### 开发配置 (`application-dev.properties`)
```properties
# WebSocket通信模式
hardware.communication.mode=websocket

# 启用硬件模拟器
hardware.simulator.enabled=true

# WebSocket配置
hardware.websocket.url=ws://122.51.229.122:6123
```

## 🔍 故障排查

### 1. **串口问题**

```bash
# 检查串口设备
dmesg | grep tty

# 检查串口权限
ls -la /dev/ttysWK*

# 测试串口通信
sudo apt install minicom
sudo minicom -D /dev/ttysWK2 -b 115200
```

### 2. **Java问题**

```bash
# 检查Java版本
java -version

# 检查系统资源
free -h
df -h

# 检查端口占用
sudo netstat -tlnp | grep 8080
```

### 3. **日志检查**

```bash
# 应用日志
tail -f /var/log/bonesys/app.log

# 系统日志
sudo journalctl -u bonesys -f

# 串口相关日志
dmesg | grep -i serial
```

## 📊 性能优化

### 1. **JVM参数优化**

```bash
java -Xms512m -Xmx1g -XX:+UseG1GC \
  -jar /opt/bonesys/BoneSys-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=ubuntu
```

### 2. **系统优化**

```bash
# 优化串口缓冲区
echo 'KERNEL=="ttysWK*", MODE="0666", GROUP="dialout"' | sudo tee /etc/udev/rules.d/99-serial.rules
sudo udevadm control --reload-rules

# 设置系统限制
echo "bonesys soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "bonesys hard nofile 65536" | sudo tee -a /etc/security/limits.conf
```

## ✅ 验证部署

### 1. **健康检查**

```bash
# API健康检查
curl http://localhost:8080/actuator/health

# 串口连接检查
curl http://localhost:8080/api/hardware/status
```

### 2. **功能测试**

```bash
# 查询治疗头
curl -X GET http://localhost:8080/api/hardware/treatment-heads

# 检查通信模式
curl -X GET http://localhost:8080/api/hardware/info
```

## 🔒 安全配置

### 1. **防火墙配置**

```bash
# 开放8080端口
sudo ufw allow 8080/tcp

# 检查防火墙状态
sudo ufw status
```

### 2. **SSL配置（可选）**

如需HTTPS，在配置文件中添加：
```properties
server.ssl.key-store=/opt/bonesys/keystore.p12
server.ssl.key-store-password=your-password
server.ssl.key-store-type=PKCS12
server.port=8443
```

---

## 🎯 总结

- ✅ **编译**: 使用 `./gradlew clean build` 
- ✅ **部署**: 复制JAR到 `/opt/bonesys/`
- ✅ **启动**: `--spring.profiles.active=ubuntu`
- ✅ **配置**: 串口模式，禁用模拟器
- ✅ **监控**: systemd服务 + 日志监控

现在你可以成功在Ubuntu环境中部署串口版本的医疗系统了！🚀 