package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.EnhancedCandidateItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据缺失处理服务
 * 专门处理患者数据中的各种缺失情况，确保前端显示的一致性和用户体验
 */
@Service
@Slf4j
public class DataMissingHandlerService {
    
    // 默认值常量
    public static final String DEFAULT_NO_RECORD = "无档案";
    public static final String DEFAULT_PENDING = "待确定";
    public static final String DEFAULT_UNKNOWN_PATIENT = "未知患者";
    public static final String DEFAULT_UNKNOWN_AGE = "未知";
    public static final String DEFAULT_UNKNOWN_GENDER = "未知";
    public static final String DEFAULT_UNKNOWN_CARD = "未知";
    public static final String DEFAULT_DATA_ERROR = "数据异常";
    public static final Integer DEFAULT_ZERO_SESSIONS = 0;
    
    /**
     * 处理患者没有档案记录的情况
     */
    public void handleNoRecords(EnhancedCandidateItem candidate) {
        if (candidate == null) return;
        
        log.debug("处理患者{}无档案记录情况", candidate.getPatientId());
        
        candidate.setVisitTime(DEFAULT_NO_RECORD);
        candidate.setTreatmentParts(DEFAULT_PENDING);
        candidate.setTotalSessions(DEFAULT_ZERO_SESSIONS);
    }
    
    /**
     * 处理档案没有部位统计的情况
     */
    public void handleNoBodyPartStats(EnhancedCandidateItem candidate) {
        if (candidate == null) return;
        
        log.debug("处理患者{}无部位统计情况", candidate.getPatientId());
        
        candidate.setTreatmentParts(DEFAULT_PENDING);
        candidate.setTotalSessions(DEFAULT_ZERO_SESSIONS);
    }
    
    /**
     * 处理患者基本信息缺失的情况
     */
    public void handleMissingBasicInfo(EnhancedCandidateItem candidate) {
        if (candidate == null) return;
        
        log.debug("处理患者{}基本信息缺失情况", candidate.getPatientId());
        
        // 处理姓名缺失
        if (candidate.getName() == null || candidate.getName().trim().isEmpty()) {
            candidate.setName(DEFAULT_UNKNOWN_PATIENT);
            log.warn("患者{}姓名缺失，设置为默认值", candidate.getPatientId());
        }
        
        // 处理年龄缺失
        if (candidate.getAge() == null || candidate.getAge().trim().isEmpty()) {
            candidate.setAge(DEFAULT_UNKNOWN_AGE);
        }
        
        // 处理性别缺失
        if (candidate.getGender() == null || candidate.getGender().trim().isEmpty()) {
            candidate.setGender(DEFAULT_UNKNOWN_GENDER);
        }
        
        // 处理就诊卡号缺失
        if (candidate.getCardId() == null || candidate.getCardId().trim().isEmpty()) {
            candidate.setCardId(DEFAULT_UNKNOWN_CARD);
            log.warn("患者{}就诊卡号缺失，设置为默认值", candidate.getPatientId());
        }
    }
    
    /**
     * 处理数据异常情况
     */
    public void handleDataException(EnhancedCandidateItem candidate, Exception exception) {
        if (candidate == null) return;
        
        log.error("处理患者{}数据异常: {}", candidate.getPatientId(), exception.getMessage());
        
        candidate.setVisitTime(DEFAULT_DATA_ERROR);
        candidate.setTreatmentParts(DEFAULT_DATA_ERROR);
        candidate.setTotalSessions(DEFAULT_ZERO_SESSIONS);
    }
    
    /**
     * 批量处理候选患者列表中的数据缺失情况
     */
    public List<EnhancedCandidateItem> handleMissingDataBatch(List<EnhancedCandidateItem> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return candidates;
        }
        
        log.info("批量处理{}个候选患者的数据缺失情况", candidates.size());
        
        return candidates.stream()
                .map(this::handleMissingDataSingle)
                .collect(Collectors.toList());
    }
    
    /**
     * 处理单个候选患者的数据缺失情况
     */
    public EnhancedCandidateItem handleMissingDataSingle(EnhancedCandidateItem candidate) {
        if (candidate == null) {
            return null;
        }
        
        try {
            // 处理基本信息缺失
            handleMissingBasicInfo(candidate);
            
            // 检查并处理档案相关数据缺失
            if (!candidate.hasRecords()) {
                handleNoRecords(candidate);
            } else if (!candidate.hasTreatments()) {
                handleNoBodyPartStats(candidate);
            }
            
            // 确保数据完整性
            ensureDataIntegrity(candidate);
            
            return candidate;
            
        } catch (Exception e) {
            log.error("处理候选患者{}数据缺失时发生异常", candidate.getPatientId(), e);
            handleDataException(candidate, e);
            return candidate;
        }
    }
    
    /**
     * 确保数据完整性
     */
    private void ensureDataIntegrity(EnhancedCandidateItem candidate) {
        // 确保就诊时间不为空
        if (candidate.getVisitTime() == null) {
            candidate.setVisitTime(DEFAULT_NO_RECORD);
        }
        
        // 确保治疗部位不为空
        if (candidate.getTreatmentParts() == null) {
            candidate.setTreatmentParts(DEFAULT_PENDING);
        }
        
        // 确保治疗次数不为空且非负
        if (candidate.getTotalSessions() == null || candidate.getTotalSessions() < 0) {
            candidate.setTotalSessions(DEFAULT_ZERO_SESSIONS);
        }
    }
    
    /**
     * 生成数据缺失统计报告
     */
    public Map<String, Object> generateMissingDataReport(List<EnhancedCandidateItem> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return Map.of("message", "无数据");
        }
        
        int total = candidates.size();
        int noRecords = 0;
        int noTreatments = 0;
        int missingNames = 0;
        int missingCardIds = 0;
        int dataErrors = 0;
        
        for (EnhancedCandidateItem candidate : candidates) {
            if (DEFAULT_NO_RECORD.equals(candidate.getVisitTime())) {
                noRecords++;
            }
            
            if (DEFAULT_PENDING.equals(candidate.getTreatmentParts())) {
                noTreatments++;
            }
            
            if (DEFAULT_UNKNOWN_PATIENT.equals(candidate.getName())) {
                missingNames++;
            }
            
            if (DEFAULT_UNKNOWN_CARD.equals(candidate.getCardId())) {
                missingCardIds++;
            }
            
            if (DEFAULT_DATA_ERROR.equals(candidate.getVisitTime()) || 
                DEFAULT_DATA_ERROR.equals(candidate.getTreatmentParts())) {
                dataErrors++;
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("noRecords", noRecords);
        result.put("noRecordsPercentage", String.format("%.1f%%", (noRecords * 100.0 / total)));
        result.put("noTreatments", noTreatments);
        result.put("noTreatmentsPercentage", String.format("%.1f%%", (noTreatments * 100.0 / total)));
        result.put("missingNames", missingNames);
        result.put("missingNamesPercentage", String.format("%.1f%%", (missingNames * 100.0 / total)));
        result.put("missingCardIds", missingCardIds);
        result.put("missingCardIdsPercentage", String.format("%.1f%%", (missingCardIds * 100.0 / total)));
        result.put("dataErrors", dataErrors);
        result.put("dataErrorsPercentage", String.format("%.1f%%", (dataErrors * 100.0 / total)));
        return result;
    }
    
    /**
     * 检查候选患者数据的完整性
     */
    public boolean isDataComplete(EnhancedCandidateItem candidate) {
        if (candidate == null) {
            return false;
        }
        
        return !DEFAULT_UNKNOWN_PATIENT.equals(candidate.getName()) &&
               !DEFAULT_UNKNOWN_CARD.equals(candidate.getCardId()) &&
               !DEFAULT_DATA_ERROR.equals(candidate.getVisitTime()) &&
               !DEFAULT_DATA_ERROR.equals(candidate.getTreatmentParts()) &&
               candidate.isValid();
    }
    
    /**
     * 获取数据完整性等级
     */
    public String getDataCompletenessLevel(EnhancedCandidateItem candidate) {
        if (candidate == null) {
            return "无数据";
        }
        
        if (isDataComplete(candidate)) {
            return "完整";
        }
        
        if (candidate.hasRecords() && candidate.hasTreatments()) {
            return "良好";
        }
        
        if (candidate.hasRecords()) {
            return "一般";
        }
        
        return "缺失";
    }
    
    /**
     * 创建数据缺失提示信息
     */
    public String createMissingDataHint(EnhancedCandidateItem candidate) {
        if (candidate == null) {
            return "数据不存在";
        }
        
        StringBuilder hint = new StringBuilder();
        
        if (DEFAULT_NO_RECORD.equals(candidate.getVisitTime())) {
            hint.append("该患者尚未建立档案记录；");
        }
        
        if (DEFAULT_PENDING.equals(candidate.getTreatmentParts())) {
            hint.append("治疗部位待确定；");
        }
        
        if (candidate.getTotalSessions() == 0) {
            hint.append("尚未开始治疗；");
        }
        
        if (DEFAULT_UNKNOWN_PATIENT.equals(candidate.getName())) {
            hint.append("患者姓名信息缺失；");
        }
        
        if (DEFAULT_UNKNOWN_CARD.equals(candidate.getCardId())) {
            hint.append("就诊卡号信息缺失；");
        }
        
        if (hint.length() == 0) {
            return "数据完整";
        }
        
        return hint.toString().replaceAll("；$", "");
    }
}