# 治疗头自动同步系统 - 设计文档

## 概述

治疗头自动同步系统是一个后台服务，负责在应用启动时初始化治疗头数据，并定期与硬件控制板同步治疗头状态。该系统确保数据库中的治疗头信息与实际硬件状态保持一致，为前端界面和业务逻辑提供准确的数据支持。

## 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    治疗头自动同步系统                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  启动初始化器    │  │  定时同步服务    │  │  同步状态监控    │ │
│  │                │  │                │  │                │ │
│  │ - 应用启动触发   │  │ - 定时任务调度   │  │ - 状态查询API   │ │
│  │ - 初始数据加载   │  │ - 错误重试机制   │  │ - 性能监控      │ │
│  │ - 失败容错处理   │  │ - 优雅停止      │  │ - 手动触发API   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  数据同步处理器  │  │  硬件通信适配器  │  │  数据库操作器    │ │
│  │                │  │                │  │                │ │
│  │ - TRZI指令处理  │  │ - 串口通信      │  │ - 记录创建/更新  │ │
│  │ - 响应数据解析   │  │ - 硬件模拟器    │  │ - 状态映射      │ │
│  │ - 状态映射转换   │  │ - 通信异常处理   │  │ - 事务管理      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 数据流设计

```
应用启动 → 初始化器 → 硬件查询 → 数据解析 → 数据库更新
    ↓
定时调度器 → 同步服务 → 硬件查询 → 数据解析 → 数据库更新
    ↓
监控API ← 状态记录 ← 执行结果 ← 数据库操作 ← 状态映射
```

## 详细设计

### 1. 治疗头同步服务 (TreatmentHeadSyncService)

```java
@Service
public class TreatmentHeadSyncService {
    
    // 配置参数
    @Value("${treatment-head.sync.interval:10000}")
    private long syncInterval;
    
    @Value("${treatment-head.sync.enabled:true}")
    private boolean syncEnabled;
    
    // 核心方法
    @PostConstruct
    public void initializeOnStartup();
    
    @Scheduled(fixedDelayString = "${treatment-head.sync.interval:10000}")
    public void scheduledSync();
    
    public SyncResult performSync();
    
    public SyncStatus getSyncStatus();
    
    @PreDestroy
    public void shutdown();
}
```

### 2. 同步结果数据结构

```java
public class SyncResult {
    private boolean success;
    private LocalDateTime syncTime;
    private int totalHeads;
    private int updatedRecords;
    private int createdRecords;
    private long executionTimeMs;
    private String errorMessage;
    private List<TreatmentHeadInfo> syncedHeads;
}

public class SyncStatus {
    private LocalDateTime lastSyncTime;
    private boolean lastSyncSuccess;
    private long totalSyncCount;
    private long successfulSyncCount;
    private long failedSyncCount;
    private double averageExecutionTime;
    private String currentStatus; // RUNNING, IDLE, ERROR
}
```

### 3. 配置参数设计

```properties
# 治疗头同步配置
treatment-head.sync.enabled=true
treatment-head.sync.interval=10000
treatment-head.sync.startup-delay=5000
treatment-head.sync.timeout=5000
treatment-head.sync.retry-attempts=3
treatment-head.sync.retry-delay=2000

# 性能监控配置
treatment-head.sync.performance-threshold=3000
treatment-head.sync.enable-metrics=true
treatment-head.sync.log-level=DEBUG
```

### 4. 数据库映射逻辑

```java
public class TreatmentHeadStatusMapper {
    
    public static TreatmentHead.RealtimeStatus mapHardwareStatus(
            int batteryLevel, boolean isCharging, boolean isTreating) {
        
        if (isTreating) {
            return TreatmentHead.RealtimeStatus.TREATING;
        }
        
        if (batteryLevel == 100) {
            return TreatmentHead.RealtimeStatus.CHARGED;
        }
        
        if (batteryLevel > 60 && isCharging) {
            return TreatmentHead.RealtimeStatus.CHARGING;
        }
        
        return TreatmentHead.RealtimeStatus.CHARGED; // 默认状态
    }
}
```

### 5. 错误处理策略

```java
public class SyncErrorHandler {
    
    // 错误分类处理
    public void handleCommunicationError(SerialCommunicationException e);
    public void handleParsingError(DataParsingException e);
    public void handleDatabaseError(DataAccessException e);
    
    // 重试机制
    public boolean shouldRetry(Exception e, int attemptCount);
    public long getRetryDelay(int attemptCount);
    
    // 降级策略
    public void enableGracefulDegradation();
    public void disableGracefulDegradation();
}
```

## API接口设计

### 1. 同步状态查询接口

```http
GET /api/hardware/treatment-heads/sync/status
Response: {
    "code": 200,
    "message": "Sync status retrieved",
    "data": {
        "lastSyncTime": "2025-07-28T10:30:00",
        "lastSyncSuccess": true,
        "totalSyncCount": 150,
        "successfulSyncCount": 148,
        "failedSyncCount": 2,
        "averageExecutionTime": 1250.5,
        "currentStatus": "IDLE"
    }
}
```

### 2. 手动触发同步接口

```http
POST /api/hardware/treatment-heads/sync/trigger
Response: {
    "code": 200,
    "message": "Manual sync completed",
    "data": {
        "success": true,
        "syncTime": "2025-07-28T10:35:00",
        "totalHeads": 20,
        "updatedRecords": 18,
        "createdRecords": 2,
        "executionTimeMs": 1150
    }
}
```

### 3. 同步配置管理接口

```http
GET /api/hardware/treatment-heads/sync/config
PUT /api/hardware/treatment-heads/sync/config
{
    "enabled": true,
    "interval": 10000,
    "timeout": 5000,
    "retryAttempts": 3
}
```

## 性能考虑

### 1. 执行效率优化

- **批量数据库操作**: 使用批量更新减少数据库交互次数
- **增量更新**: 只更新发生变化的治疗头记录
- **连接池管理**: 复用数据库连接，避免频繁创建销毁
- **异步处理**: 同步任务异步执行，不阻塞主线程

### 2. 内存使用优化

- **数据缓存**: 缓存上次同步结果，避免重复处理
- **对象复用**: 复用数据传输对象，减少GC压力
- **流式处理**: 大量数据使用流式处理，避免内存溢出

### 3. 网络通信优化

- **连接复用**: 复用串口连接，避免频繁开关
- **超时控制**: 合理设置通信超时，避免长时间阻塞
- **错误快速失败**: 快速识别通信错误，避免无效重试

## 监控和日志

### 1. 关键指标监控

- 同步成功率
- 平均执行时间
- 数据库操作耗时
- 硬件通信延迟
- 错误发生频率

### 2. 日志记录策略

```java
// 正常同步日志 (DEBUG级别)
logger.debug("Treatment head sync completed: {} heads processed in {}ms", 
            headCount, executionTime);

// 性能警告日志 (WARN级别)
logger.warn("Treatment head sync took {}ms, exceeding threshold of {}ms", 
           executionTime, performanceThreshold);

// 错误日志 (ERROR级别)
logger.error("Treatment head sync failed: {}", errorMessage, exception);
```

## 测试策略

### 1. 单元测试

- 数据解析逻辑测试
- 状态映射逻辑测试
- 错误处理机制测试
- 配置参数验证测试

### 2. 集成测试

- 硬件通信集成测试
- 数据库操作集成测试
- 定时任务执行测试
- 错误恢复机制测试

### 3. 性能测试

- 大量数据同步性能测试
- 并发访问压力测试
- 长时间运行稳定性测试
- 内存泄漏检测测试

## 部署和配置

### 1. 生产环境配置

```properties
# 生产环境优化配置
treatment-head.sync.interval=10000
treatment-head.sync.timeout=3000
treatment-head.sync.retry-attempts=5
treatment-head.sync.performance-threshold=2000
treatment-head.sync.log-level=INFO
```

### 2. 开发环境配置

```properties
# 开发环境调试配置
treatment-head.sync.interval=5000
treatment-head.sync.timeout=10000
treatment-head.sync.retry-attempts=2
treatment-head.sync.performance-threshold=5000
treatment-head.sync.log-level=DEBUG
```

### 3. 健康检查

系统应提供健康检查接口，用于监控同步服务的运行状态：

```http
GET /actuator/health/treatment-head-sync
Response: {
    "status": "UP",
    "details": {
        "lastSyncTime": "2025-07-28T10:30:00",
        "syncEnabled": true,
        "consecutiveFailures": 0,
        "averageResponseTime": "1.2s"
    }
}
```