package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;

/**
 * 硬件通信统一接口
 * 支持串口和WebSocket两种通信方式
 */
public interface HardwareCommunicationInterface {
    
    /**
     * 发送命令并返回响应
     */
    String sendCommand(String command) throws SerialCommunicationException;
    
    /**
     * 发送命令到指定层级（适用于双层硬件架构）
     */
    String sendCommand(String command, String layer) throws SerialCommunicationException;
    
    /**
     * 检查连接状态
     */
    boolean isConnected();
    
    /**
     * 连接到硬件
     */
    void connect() throws SerialCommunicationException;
    
    /**
     * 断开硬件连接
     */
    void disconnect();
    
    /**
     * 获取通信类型标识
     */
    String getCommunicationType();
} 