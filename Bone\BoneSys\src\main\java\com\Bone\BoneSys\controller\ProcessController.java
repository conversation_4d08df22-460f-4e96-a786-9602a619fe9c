package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.repository.*;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 进程管理控制器
 * 对应UI页面：新版-进程管理.png, 新版-治疗进程-本地治疗.png, 新版-治疗进程-取走治疗.png
 */
@RestController
@RequestMapping("/api/processes")
@CrossOrigin(origins = "*")
public class ProcessController {

    private static final Logger logger = LoggerFactory.getLogger(ProcessController.class);

    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;

    /**
     * 获取进程管理页面数据（按部位合并显示）
     * GET /api/processes
     */
    @GetMapping
    public ApiResponse<ProcessListResponse> getProcesses(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String cardId,
            @RequestParam(required = false) String patientName,
            @RequestParam(required = false) String status) {

        try {
            logger.info("Fetching processes list - page: {}, size: {}, cardId: {}, patientName: {}, status: {}",
                       page, size, cardId, patientName, status);

            // 显示进行中的进程对应的治疗详情
            List<TreatmentDetailStatus> visibleStatuses = Arrays.asList(
                TreatmentDetailStatus.TREATING,
                TreatmentDetailStatus.COMPLETED,
                TreatmentDetailStatus.AWAITING_RETURN
            );

            // 只显示进行中的进程（process表状态为IN_PROGRESS）
            List<ProcessStatus> allowedProcessStatuses = Arrays.asList(
                ProcessStatus.IN_PROGRESS
            );

            // 查询所有符合条件的治疗详情（使用大分页获取所有数据）
            Pageable largePage = PageRequest.of(0, 10000, Sort.by("process.id").descending());
            Page<TreatmentDetail> detailPage;

            if (cardId != null && !cardId.trim().isEmpty()) {
                detailPage = treatmentDetailRepository.findByPatientCardIdContainingAndStatusInAndProcessStatusIn(
                    cardId.trim(), visibleStatuses, allowedProcessStatuses, largePage);
            } else if (patientName != null && !patientName.trim().isEmpty()) {
                detailPage = treatmentDetailRepository.findByPatientNameContainingAndStatusInAndProcessStatusIn(
                    patientName.trim(), visibleStatuses, allowedProcessStatuses, largePage);
            } else if (status != null && !status.trim().isEmpty()) {
                TreatmentDetailStatus detailStatus = TreatmentDetailStatus.valueOf(status.trim().toUpperCase());
                if (visibleStatuses.contains(detailStatus)) {
                    detailPage = treatmentDetailRepository.findByStatusAndProcessStatusIn(
                        detailStatus, allowedProcessStatuses, largePage);
                } else {
                    detailPage = Page.empty(largePage);
                }
            } else {
                detailPage = treatmentDetailRepository.findByStatusInAndProcessStatusIn(
                    visibleStatuses, allowedProcessStatuses, largePage);
            }

            List<TreatmentDetail> allDetails = detailPage.getContent();

            // 按进程ID和部位分组，合并同一部位的多个治疗头数据
            Map<String, ProcessItem> processItemMap = new LinkedHashMap<>();

            for (TreatmentDetail detail : allDetails) {
                String key = detail.getProcess().getId() + "_" + detail.getBodyPart();

                if (!processItemMap.containsKey(key)) {
                    // 创建新的进程项
                    ProcessItem item = new ProcessItem();
                    item.setProcessId(detail.getProcess().getId());
                    item.setCardId(detail.getProcess().getRecord().getPatient().getPatientCardId());
                    item.setPatientName(detail.getProcess().getRecord().getPatient().getName());
                    item.setBodyPart(detail.getBodyPart());

                    // 统一使用英文状态传输
                    item.setStatus(determineBodyPartStatus(detail.getProcess().getId(), detail.getBodyPart()));

                    processItemMap.put(key, item);
                }
            }

            // 转换为列表并分页
            List<ProcessItem> allProcesses = new ArrayList<>(processItemMap.values());

            // 手动分页
            int start = (page - 1) * size;
            int end = Math.min(start + size, allProcesses.size());
            List<ProcessItem> processes = allProcesses.subList(start, end);

            ProcessListResponse response = new ProcessListResponse();
            response.setProcesses(processes);

            // 计算合并后数据的分页信息
            int totalRecords = allProcesses.size();
            int totalPages = (int) Math.ceil((double) totalRecords / size);

            response.setPagination(new PaginationInfo(
                page,
                totalPages,
                totalRecords,
                size
            ));
            response.setStatusOptions(Arrays.asList("TREATING", "COMPLETED", "AWAITING_RETURN"));

            return ApiResponse.success("进程列表获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching processes list", e);
            return ApiResponse.error(500, "获取进程列表失败: " + e.getMessage());
        }
    }

    /**
     * 调试方法：获取所有治疗详情（用于调试进程管理页面显示问题）
     * GET /api/processes/debug/all-details
     */
    @GetMapping("/debug/all-details")
    public ApiResponse<Object> getAllTreatmentDetailsForDebug() {
        try {
            logger.info("Debug: Fetching all treatment details");

            // 获取所有治疗详情
            List<TreatmentDetail> allDetails = treatmentDetailRepository.findAll();
            logger.info("Debug: Found {} total treatment details", allDetails.size());

            // 获取所有进程
            List<com.Bone.BoneSys.entity.Process> allProcesses = processRepository.findAll();
            logger.info("Debug: Found {} total processes", allProcesses.size());

            // 构建调试信息
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("totalDetails", allDetails.size());
            debugInfo.put("totalProcesses", allProcesses.size());

            // 按状态分组的治疗详情
            Map<String, Long> detailsByStatus = allDetails.stream()
                .collect(Collectors.groupingBy(
                    detail -> detail.getStatus().toString(),
                    Collectors.counting()
                ));
            debugInfo.put("detailsByStatus", detailsByStatus);

            // 按状态分组的进程
            Map<String, Long> processesByStatus = allProcesses.stream()
                .collect(Collectors.groupingBy(
                    process -> process.getStatus().toString(),
                    Collectors.counting()
                ));
            debugInfo.put("processesByStatus", processesByStatus);

            // 最近的几个治疗详情
            List<Map<String, Object>> recentDetails = allDetails.stream()
                .sorted((a, b) -> b.getId().compareTo(a.getId()))
                .limit(5)
                .map(detail -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("id", detail.getId());
                    info.put("bodyPart", detail.getBodyPart());
                    info.put("status", detail.getStatus().toString());
                    info.put("processId", detail.getProcess().getId());
                    info.put("processStatus", detail.getProcess().getStatus().toString());
                    info.put("patientName", detail.getProcess().getRecord().getPatient().getName());
                    info.put("cardId", detail.getProcess().getRecord().getPatient().getPatientCardId());
                    return info;
                })
                .collect(Collectors.toList());
            debugInfo.put("recentDetails", recentDetails);

            return ApiResponse.success("调试信息获取成功", debugInfo);

        } catch (Exception e) {
            logger.error("Error fetching debug info", e);
            return ApiResponse.error(500, "获取调试信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗详情列表（用于进程管理页面）
     * GET /api/processes/details
     */
    @GetMapping("/details")
    public ApiResponse<TreatmentDetailListResponse> getTreatmentDetails(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String cardId,
            @RequestParam(required = false) String patientName,
            @RequestParam(required = false) String status) {

        try {
            logger.info("Fetching treatment details list - page: {}, size: {}, cardId: {}, patientName: {}, status: {}",
                       page, size, cardId, patientName, status);

            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("id").descending());
            Page<TreatmentDetail> detailPage;

            // 只显示TREATING、COMPLETED、AWAITING_RETURN状态的治疗详情
            List<TreatmentDetailStatus> visibleStatuses = Arrays.asList(
                TreatmentDetailStatus.TREATING,
                TreatmentDetailStatus.COMPLETED,
                TreatmentDetailStatus.AWAITING_RETURN
            );

            // 根据筛选条件查询
            if (cardId != null && !cardId.trim().isEmpty()) {
                detailPage = treatmentDetailRepository.findByPatientCardIdContainingAndStatusIn(
                    cardId.trim(), visibleStatuses, pageable);
            } else if (patientName != null && !patientName.trim().isEmpty()) {
                detailPage = treatmentDetailRepository.findByPatientNameContainingAndStatusIn(
                    patientName.trim(), visibleStatuses, pageable);
            } else if (status != null && !status.trim().isEmpty()) {
                TreatmentDetailStatus detailStatus = TreatmentDetailStatus.valueOf(status.trim().toUpperCase());
                if (visibleStatuses.contains(detailStatus)) {
                    detailPage = treatmentDetailRepository.findByStatus(detailStatus, pageable);
                } else {
                    // 如果查询的状态不在可见状态列表中，返回空结果
                    detailPage = Page.empty(pageable);
                }
            } else {
                detailPage = treatmentDetailRepository.findByStatusIn(visibleStatuses, pageable);
            }

            // 转换为响应格式
            List<TreatmentDetailItem> details = detailPage.getContent().stream()
                .map(this::convertToTreatmentDetailItem)
                .collect(Collectors.toList());

            TreatmentDetailListResponse response = new TreatmentDetailListResponse();
            response.setDetails(details);
            response.setPagination(new PaginationInfo(
                page,
                detailPage.getTotalPages(),
                (int) detailPage.getTotalElements(),
                size
            ));
            response.setStatusOptions(Arrays.asList("TREATING", "COMPLETED", "AWAITING_RETURN"));

            return ApiResponse.success("治疗详情列表获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching treatment details list", e);
            return ApiResponse.error(500, "获取治疗详情列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗进程实时数据
     * GET /api/processes/{processId}/realtime
     */
    @GetMapping("/{processId}/realtime")
    public ApiResponse<ProcessRealtimeResponse> getProcessRealtime(@PathVariable Long processId) {
        try {
            logger.info("Fetching process realtime data: {}", processId);

            Optional<com.Bone.BoneSys.entity.Process> processOpt = processRepository.findById(processId);
            if (!processOpt.isPresent()) {
                return ApiResponse.error(404, "进程不存在");
            }

            com.Bone.BoneSys.entity.Process process = processOpt.get();
            
            ProcessRealtimeResponse response = new ProcessRealtimeResponse();
            response.setPatientName(process.getRecord().getPatient().getName());
            response.setTreatmentMode(process.getTreatmentMode().name());
            response.setStartTime(process.getStartTime()); // 设置进程开始时间

            // 构建部位治疗信息
            List<BodyPartTreatment> bodyParts = process.getTreatmentDetails().stream()
                .map(detail -> {
                    BodyPartTreatment bodyPart = new BodyPartTreatment();
                    bodyPart.setBodyPart(detail.getBodyPart());
                    bodyPart.setIntensity(detail.getIntensity() + "mW/cm²");
                    
                    // 计算剩余时间和已用时间
                    if (process.getStatus() == ProcessStatus.IN_PROGRESS) {
                        LocalDateTime now = LocalDateTime.now();
                        LocalDateTime startTime = process.getStartTime();
                        long elapsedSeconds = Duration.between(startTime, now).getSeconds();
                        long totalDurationSeconds = detail.getDuration() * 60L; // 转换为秒

                        // 对于远端治疗，允许remainingSeconds为负数，以便前端判断是否需要更新状态
                        long remainingSeconds;
                        if (process.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
                            // 远端治疗：允许负数，用于前端判断是否超过治疗时间+提醒时间
                            remainingSeconds = totalDurationSeconds - elapsedSeconds;
                        } else {
                            // 本地治疗：保持原逻辑，不允许负数
                            remainingSeconds = Math.max(0, totalDurationSeconds - elapsedSeconds);
                        }

                        // 精确到秒的剩余时间计算
                        bodyPart.setRemainingTime(formatRemainingTimeWithSeconds(Math.max(0, remainingSeconds)));
                        bodyPart.setElapsedTime(formatElapsedTime(elapsedSeconds));
                        bodyPart.setElapsedTimeSeconds(elapsedSeconds);
                        bodyPart.setTotalDurationSeconds(totalDurationSeconds);
                        bodyPart.setRemainingTimeSeconds(remainingSeconds); // 允许负数用于前端状态判断
                    } else {
                        long totalDurationSeconds = detail.getDuration() * 60L;
                        bodyPart.setRemainingTime("已完成");
                        bodyPart.setElapsedTime(formatElapsedTime(totalDurationSeconds));
                        bodyPart.setElapsedTimeSeconds(totalDurationSeconds);
                        bodyPart.setTotalDurationSeconds(totalDurationSeconds);
                        bodyPart.setRemainingTimeSeconds(0L);
                    }
                    
                    return bodyPart;
                })
                .collect(Collectors.toList());

            response.setBodyParts(bodyParts);

            return ApiResponse.success("实时数据获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching process realtime data: {}", processId, e);
            return ApiResponse.error(500, "获取实时数据失败: " + e.getMessage());
        }
    }

    /**
     * 确定某个进程的某个部位的整体状态
     * 根据该部位所有治疗头的状态来确定部位的整体状态
     */
    private String determineBodyPartStatus(Long processId, String bodyPart) {
        // 查询该进程该部位的所有治疗详情
        List<TreatmentDetail> bodyPartDetails = treatmentDetailRepository.findByProcessId(processId)
            .stream()
            .filter(detail -> bodyPart.equals(detail.getBodyPart()))
            .collect(Collectors.toList());

        if (bodyPartDetails.isEmpty()) {
            return "UNKNOWN";
        }

        // 状态优先级：TREATING > AWAITING_RETURN > COMPLETED > TERMINATED
        boolean hasTreating = bodyPartDetails.stream().anyMatch(d -> d.getStatus() == TreatmentDetailStatus.TREATING);
        boolean hasAwaitingReturn = bodyPartDetails.stream().anyMatch(d -> d.getStatus() == TreatmentDetailStatus.AWAITING_RETURN);
        boolean hasCompleted = bodyPartDetails.stream().anyMatch(d -> d.getStatus() == TreatmentDetailStatus.COMPLETED);

        if (hasTreating) {
            return "TREATING";
        } else if (hasAwaitingReturn) {
            return "AWAITING_RETURN";
        } else if (hasCompleted) {
            return "COMPLETED";
        } else {
            return "TERMINATED";
        }
    }

    /**
     * 转换Process为ProcessItem（保留原方法用于其他地方）
     */
    private ProcessItem convertToProcessItem(com.Bone.BoneSys.entity.Process process) {
        ProcessItem item = new ProcessItem();

        item.setProcessId(process.getId());
        item.setCardId(process.getRecord().getPatient().getPatientCardId());
        item.setPatientName(process.getRecord().getPatient().getName());

        // 获取治疗部位（合并所有治疗详情的部位）
        List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(process.getId());
        if (!details.isEmpty()) {
            // 获取所有不重复的部位并合并显示
            String bodyParts = details.stream()
                .map(TreatmentDetail::getBodyPart)
                .distinct()
                .collect(Collectors.joining(", "));
            item.setBodyPart(bodyParts);
        } else {
            item.setBodyPart("未知");
        }

        item.setStatus(convertStatusToDisplayText(process.getStatus()));

        return item;
    }

    /**
     * 转换TreatmentDetail为ProcessItem（用于进程管理页面按部位显示）
     */
    private ProcessItem convertTreatmentDetailToProcessItem(TreatmentDetail detail) {
        ProcessItem item = new ProcessItem();

        item.setProcessId(detail.getProcess().getId());
        item.setCardId(detail.getProcess().getRecord().getPatient().getPatientCardId());
        item.setPatientName(detail.getProcess().getRecord().getPatient().getName());
        item.setBodyPart(detail.getBodyPart()); // 单个部位
        item.setStatus(detail.getStatus().name()); // 返回英文状态，让前端进行映射

        return item;
    }

    /**
     * 转换治疗详情为显示项
     */
    private TreatmentDetailItem convertToTreatmentDetailItem(TreatmentDetail detail) {
        TreatmentDetailItem item = new TreatmentDetailItem();
        item.setDetailId(detail.getId());
        item.setCardId(detail.getProcess().getRecord().getPatient().getPatientCardId());
        item.setPatientName(detail.getProcess().getRecord().getPatient().getName());
        item.setBodyPart(detail.getBodyPart());
        item.setStatus(convertDetailStatusToDisplayText(detail.getStatus())); // 中文显示状态
        item.setStatusCode(detail.getStatus().name()); // 英文枚举状态
        item.setProcessId(detail.getProcess().getId());
        item.setTreatmentMode(detail.getProcess().getTreatmentMode().name());
        item.setProcessStatus(detail.getProcess().getStatus().name()); // 进程状态（英文枚举）
        item.setStartTime(detail.getProcess().getStartTime());
        item.setHeadNumberUsed(detail.getHeadNumberUsed()); // 设置使用的治疗头编号
        return item;
    }

    /**
     * 转换治疗详情状态为显示文本
     */
    private String convertDetailStatusToDisplayText(TreatmentDetailStatus status) {
        switch (status) {
            case TREATING:
                return "治疗中";
            case COMPLETED:
                return "已完成";
            case AWAITING_RETURN:
                return "待取回";
            default:
                return "未知状态";
        }
    }

    /**
     * 转换状态为显示文本
     */
    private String convertStatusToDisplayText(ProcessStatus status) {
        switch (status) {
            case IN_PROGRESS:
                return "治疗中";
            case COMPLETED:
                return "已完成";
            case CANCELLED:
                return "已取消";
            default:
                return "未知";
        }
    }

    /**
     * 格式化剩余时间（精确到秒）
     */
    private String formatRemainingTimeWithSeconds(long totalSeconds) {
        if (totalSeconds <= 0) {
            return "0分0秒";
        }
        
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;
        
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }

    /**
     * 格式化已用时间（精确到秒）
     */
    private String formatElapsedTime(long totalSeconds) {
        if (totalSeconds < 60) {
            return totalSeconds + "秒";
        }
        
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;
        
        if (minutes < 60) {
            return minutes + "分" + seconds + "秒";
        } else {
            long hours = minutes / 60;
            long remainingMinutes = minutes % 60;
            return hours + "时" + remainingMinutes + "分" + seconds + "秒";
        }
    }

    // DTO类定义
    @Data
    public static class ProcessListResponse {
        private List<ProcessItem> processes;
        private PaginationInfo pagination;
        private List<String> statusOptions;
    }

    @Data
    public static class ProcessItem {
        private Long processId;
        private String cardId;
        private String patientName;
        private String bodyPart;
        private String status;
    }

    /**
     * 治疗详情列表响应
     */
    @Data
    public static class TreatmentDetailListResponse {
        private List<TreatmentDetailItem> details;
        private PaginationInfo pagination;
        private List<String> statusOptions;
    }

    /**
     * 治疗详情项
     */
    @Data
    public static class TreatmentDetailItem {
        private Long detailId;
        private String cardId;
        private String patientName;
        private String bodyPart;
        private String status; // 治疗详情状态（中文显示）
        private String statusCode; // 治疗详情状态（英文枚举）
        private Long processId;
        private String treatmentMode;
        private String processStatus; // 进程状态（英文枚举）
        private LocalDateTime startTime;
        private Integer headNumberUsed; // 使用的治疗头编号
    }

    @Data
    public static class ProcessRealtimeResponse {
        private String patientName;
        private String treatmentMode;
        private LocalDateTime startTime; // 添加进程开始时间
        private List<BodyPartTreatment> bodyParts;
    }

    @Data
    public static class BodyPartTreatment {
        private String bodyPart;
        private String remainingTime;
        private String intensity;
        private String elapsedTime; // 已用时间字符串格式
        private Long elapsedTimeSeconds; // 已用时间秒数
        private Long totalDurationSeconds; // 总时长秒数
        private Long remainingTimeSeconds; // 添加剩余时间秒数
    }

    @Data
    public static class PaginationInfo {
        private int currentPage;
        private int totalPages;
        private int totalRecords;
        private int pageSize;

        public PaginationInfo(int currentPage, int totalPages, int totalRecords, int pageSize) {
            this.currentPage = currentPage;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
            this.pageSize = pageSize;
        }
    }


}








