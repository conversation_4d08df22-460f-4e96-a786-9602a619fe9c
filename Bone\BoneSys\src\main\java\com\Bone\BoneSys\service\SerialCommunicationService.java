package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.fazecast.jSerialComm.SerialPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

/**
 * 串口通信服务
 * 负责与硬件控制板的串口通信
 * 
 * 通信参数：
 * - 波特率：115200
 * - 停止位：1
 * - 数据位：8
 * - 校验位：None
 */
@Service
public class SerialCommunicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(SerialCommunicationService.class);
    
    // 串口通信参数常量
    private static final int BAUD_RATE = 115200;
    private static final int DATA_BITS = 8;
    private static final int STOP_BITS = SerialPort.ONE_STOP_BIT;
    private static final int PARITY = SerialPort.NO_PARITY;
    private static final int TIMEOUT_READ = 1000; // 读取超时1秒
    private static final int TIMEOUT_WRITE = 1000; // 写入超时1秒
    private static final int RESPONSE_WAIT_TIME = 100; // 等待响应时间100ms
    
    @Value("${serial.port.name:}")
    private String configuredPortName;
    
    @Value("${serial.port.auto-detect:true}")
    private boolean autoDetectPort;
    
    @Value("${hardware.simulator.enabled:false}")
    private boolean hardwareSimulatorEnabled;
    
    @Autowired(required = false)
    private HardwareSimulatorService hardwareSimulatorService;
    
    private SerialPort serialPort;
    private volatile boolean isConnected = false;
    
    /**
     * 初始化串口连接
     */
    @PostConstruct
    public void initialize() {
        try {
            connectToSerialPort();
            logger.info("Serial communication service initialized successfully");
        } catch (Exception e) {
            logger.warn("Failed to initialize serial communication service: {}", e.getMessage());
            // 不抛出异常，允许服务启动，后续可以重试连接
        }
    }
    
    /**
     * 连接到串口
     */
    private void connectToSerialPort() throws SerialCommunicationException {
        try {
            // 如果已经连接，先断开
            if (serialPort != null && serialPort.isOpen()) {
                serialPort.closePort();
            }
            
            // 选择串口
            serialPort = selectSerialPort();
            if (serialPort == null) {
                throw new SerialCommunicationException("No suitable serial port found");
            }
            
            // 配置串口参数
            serialPort.setBaudRate(BAUD_RATE);
            serialPort.setNumDataBits(DATA_BITS);
            serialPort.setNumStopBits(STOP_BITS);
            serialPort.setParity(PARITY);
            
            // 设置超时
            serialPort.setComPortTimeouts(
                SerialPort.TIMEOUT_READ_BLOCKING | SerialPort.TIMEOUT_WRITE_BLOCKING,
                TIMEOUT_READ,
                TIMEOUT_WRITE
            );
            
            // 打开串口
            if (serialPort.openPort()) {
                isConnected = true;
                logger.info("Connected to serial port: {} at baud rate: {}", 
                    serialPort.getSystemPortName(), BAUD_RATE);
            } else {
                throw new SerialCommunicationException("Failed to open serial port: " + serialPort.getSystemPortName());
            }
            
        } catch (Exception e) {
            isConnected = false;
            if (e instanceof SerialCommunicationException) {
                throw e;
            }
            throw new SerialCommunicationException("Failed to connect to serial port", e);
        }
    }
    
    /**
     * 选择合适的串口
     */
    private SerialPort selectSerialPort() {
        SerialPort[] availablePorts = SerialPort.getCommPorts();
        
        if (availablePorts.length == 0) {
            logger.warn("No serial ports available");
            return null;
        }
        
        // 如果配置了特定端口名，优先使用
        if (configuredPortName != null && !configuredPortName.trim().isEmpty()) {
            for (SerialPort port : availablePorts) {
                if (port.getSystemPortName().equals(configuredPortName.trim())) {
                    logger.info("Using configured serial port: {}", configuredPortName);
                    return port;
                }
            }
            logger.warn("Configured port {} not found, will auto-detect", configuredPortName);
        }
        
        // 自动检测端口
        if (autoDetectPort) {
            // 优先选择USB串口或者包含特定关键词的端口
            for (SerialPort port : availablePorts) {
                String portName = port.getSystemPortName().toLowerCase();
                String portDesc = port.getPortDescription().toLowerCase();
                
                // 优先选择USB转串口设备
                if (portDesc.contains("usb") || portDesc.contains("serial") || 
                    portName.contains("usb") || portName.startsWith("com")) {
                    logger.info("Auto-detected serial port: {} ({})", 
                        port.getSystemPortName(), port.getPortDescription());
                    return port;
                }
            }
            
            // 如果没有找到USB串口，使用第一个可用端口
            SerialPort firstPort = availablePorts[0];
            logger.info("Using first available serial port: {} ({})", 
                firstPort.getSystemPortName(), firstPort.getPortDescription());
            return firstPort;
        }
        
        return null;
    }
    
    /**
     * 发送命令到硬件控制板
     * 
     * @param command 要发送的命令
     * @return 硬件返回的响应
     * @throws SerialCommunicationException 通信异常
     */
    public synchronized String sendCommand(String command) throws SerialCommunicationException {
        // 如果启用了硬件模拟器，使用模拟器响应
        if (hardwareSimulatorEnabled && hardwareSimulatorService != null) {
            logger.debug("Using hardware simulator for command: {}", command.trim());
            String response = hardwareSimulatorService.generateSimulatedResponse(command);
            logger.debug("Simulator response: {}", response.trim());
            return response;
        }
        
        // 使用真实串口通信
        if (!isConnected || serialPort == null || !serialPort.isOpen()) {
            throw new SerialCommunicationException("Serial port is not connected");
        }
        
        try {
            logger.debug("Sending command: {}", command.trim());
            
            String response = sendCommandInternal(command);
            
            logger.debug("Received response: {}", response.trim());
            return response;
            
        } catch (Exception e) {
            logger.error("Failed to send command: {}", command.trim(), e);
            
            // 尝试重连
            if (e.getMessage().contains("port") || e.getMessage().contains("connection")) {
                logger.info("Attempting to reconnect serial port...");
                try {
                    reconnect();
                    // 重连成功后重试一次
                    return sendCommandInternal(command);
                } catch (Exception reconnectEx) {
                    logger.error("Failed to reconnect serial port", reconnectEx);
                }
            }
            
            throw new SerialCommunicationException("Failed to send command: " + command.trim(), e);
        }
    }
    
    /**
     * 异步发送命令
     */
    public CompletableFuture<String> sendCommandAsync(String command) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return sendCommand(command);
            } catch (SerialCommunicationException e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 内部命令发送实现
     */
    private String sendCommandInternal(String command) throws Exception {
        // 清空输入缓冲区
        if (serialPort.bytesAvailable() > 0) {
            byte[] buffer = new byte[serialPort.bytesAvailable()];
            serialPort.readBytes(buffer, buffer.length);
            logger.debug("Cleared {} bytes from input buffer", buffer.length);
        }
        
        // 发送命令
        byte[] commandBytes = command.getBytes(StandardCharsets.UTF_8);
        int bytesWritten = serialPort.writeBytes(commandBytes, commandBytes.length);
        
        if (bytesWritten != commandBytes.length) {
            throw new SerialCommunicationException(
                String.format("Failed to write complete command. Expected: %d, Written: %d", 
                    commandBytes.length, bytesWritten));
        }
        
        // 等待响应
        Thread.sleep(RESPONSE_WAIT_TIME);
        
        // 读取响应
        StringBuilder responseBuilder = new StringBuilder();
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < TIMEOUT_READ) {
            if (serialPort.bytesAvailable() > 0) {
                byte[] buffer = new byte[serialPort.bytesAvailable()];
                int bytesRead = serialPort.readBytes(buffer, buffer.length);
                
                if (bytesRead > 0) {
                    String chunk = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                    responseBuilder.append(chunk);
                    
                    // 检查是否收到完整响应（以\r\n结尾）
                    if (responseBuilder.toString().contains("\r\n")) {
                        break;
                    }
                }
            } else {
                // 没有数据可读，短暂等待
                Thread.sleep(10);
            }
        }
        
        String response = responseBuilder.toString();
        if (response.isEmpty()) {
            throw new SerialCommunicationException("No response received from hardware");
        }
        
        return response;
    }
    
    /**
     * 检查串口连接状态
     */
    public boolean isConnected() {
        return isConnected && serialPort != null && serialPort.isOpen();
    }
    
    /**
     * 获取当前串口信息
     */
    public String getPortInfo() {
        if (serialPort != null) {
            return String.format("%s (%s)", serialPort.getSystemPortName(), serialPort.getPortDescription());
        }
        return "No port connected";
    }
    
    /**
     * 重新连接串口
     */
    public synchronized void reconnect() throws SerialCommunicationException {
        logger.info("Reconnecting serial port...");
        disconnect();
        connectToSerialPort();
    }
    
    /**
     * 断开串口连接
     */
    public synchronized void disconnect() {
        try {
            if (serialPort != null && serialPort.isOpen()) {
                serialPort.closePort();
                logger.info("Disconnected from serial port: {}", serialPort.getSystemPortName());
            }
            isConnected = false;
            serialPort = null;
        } catch (Exception e) {
            logger.error("Error disconnecting from serial port", e);
        }
    }
    
    /**
     * 获取可用串口列表
     */
    public String[] getAvailablePorts() {
        SerialPort[] ports = SerialPort.getCommPorts();
        String[] portNames = new String[ports.length];
        for (int i = 0; i < ports.length; i++) {
            portNames[i] = ports[i].getSystemPortName() + " - " + ports[i].getPortDescription();
        }
        return portNames;
    }
    
    /**
     * 测试串口连接
     */
    public boolean testConnection() {
        try {
            if (!isConnected()) {
                return false;
            }
            
            // 发送一个简单的测试命令
            String testCommand = "TEST\r\n";
            sendCommand(testCommand);
            return true;
            
        } catch (Exception e) {
            logger.debug("Connection test failed: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 服务销毁时清理资源
     */
    @PreDestroy
    public void cleanup() {
        disconnect();
        logger.info("Serial communication service cleaned up");
    }
}