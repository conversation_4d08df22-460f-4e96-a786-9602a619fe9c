# 治疗头推荐系统优化设计文档

## 概述

本设计文档描述了治疗头推荐系统的优化架构，主要目标是实现基于身体部位贴片需求的精确治疗头推荐和指示灯控制。系统将根据每个部位的贴片数量和类型（浅部/深部）智能计算总需求，检查可用性，并推荐最优的治疗头组合。

## 架构设计

### 系统架构图

```mermaid
graph TB
    A[前端治疗参数设置] --> B[TreatmentHeadAvailabilityRequest]
    B --> C[TreatmentHeadRecommendationService]
    C --> D[HardwareService]
    D --> E[串口通信层]
    E --> F[治疗头硬件设备]
    
    C --> G[需求计算模块]
    C --> H[可用性检查模块]
    C --> I[推荐算法模块]
    C --> J[指示灯控制模块]
    
    G --> K[总需求计算]
    G --> L[浅部需求统计]
    G --> M[深部需求统计]
    
    H --> N[浅部治疗头筛选]
    H --> O[深部治疗头筛选]
    H --> P[充足性验证]
    
    I --> Q[优先级排序]
    I --> R[推荐组合生成]
    I --> S[推荐理由生成]
    
    J --> T[指示灯颜色分配]
    J --> U[TWSC指令发送]
    J --> V[指示灯状态管理]
```

### 核心组件设计

#### 1. 请求处理层 (Request Processing Layer)

**TreatmentHeadAvailabilityRequest**
- 接收前端的贴片需求数据
- 支持新格式：`List<BodyPartPatchRequest> bodyPartPatches`
- 保持向后兼容：支持旧格式的自动转换
- 提供智能计算方法：`calculateTotalRequiredCount()`, `getShallowPatchCount()`, `getDeepPatchCount()`

**BodyPartPatchRequest**
- 表示单个身体部位的贴片需求
- 包含：部位名称、贴片类型(SHALLOW/DEEP)、贴片数量(1-4)
- 支持数据验证和格式化输出

#### 2. 核心业务逻辑层 (Business Logic Layer)

**TreatmentHeadRecommendationService**
- 主要业务逻辑处理服务
- 协调各个子模块完成推荐流程
- 提供统一的错误处理和日志记录

**需求计算模块 (Requirement Calculation Module)**
```java
// 核心计算逻辑
public class RequirementCalculator {
    public int calculateTotalRequired(List<BodyPartPatchRequest> patches);
    public int calculateShallowRequired(List<BodyPartPatchRequest> patches);
    public int calculateDeepRequired(List<BodyPartPatchRequest> patches);
    public Map<String, Integer> calculateByType(List<BodyPartPatchRequest> patches);
}
```

**可用性检查模块 (Availability Check Module)**
```java
// 可用性检查逻辑
public class AvailabilityChecker {
    public List<TreatmentHeadInfo> filterShallowAvailable(List<TreatmentHeadInfo> allHeads);
    public List<TreatmentHeadInfo> filterDeepAvailable(List<TreatmentHeadInfo> allHeads);
    public boolean isHeadAvailable(TreatmentHeadInfo head);
    public AvailabilityResult checkSufficiency(int shallowAvailable, int shallowRequired, 
                                              int deepAvailable, int deepRequired);
}
```

**推荐算法模块 (Recommendation Algorithm Module)**
```java
// 推荐算法逻辑
public class RecommendationAlgorithm {
    public List<TreatmentHeadInfo> sortByPriority(List<TreatmentHeadInfo> heads);
    public List<TreatmentHeadRecommendation> generateBodyPartBasedRecommendations(
        List<TreatmentHeadInfo> shallowHeads, List<TreatmentHeadInfo> deepHeads,
        List<BodyPartPatchRequest> bodyPartPatches);
    public String generateRecommendationReason(TreatmentHeadInfo head, int priority, String patchType, String bodyPart);
    public Map<String, List<TreatmentHeadInfo>> allocateHeadsByBodyPart(
        List<TreatmentHeadInfo> availableHeads, List<BodyPartPatchRequest> bodyPartPatches);
}
```

#### 3. 硬件通信层 (Hardware Communication Layer)

**HardwareService**
- 封装串口通信操作
- 提供治疗头状态查询：`syncAllTreatmentHeads()`
- 提供指示灯控制：`setTreatmentHeadLights()`, `turnOffTreatmentHeadLights()`
- 提供治疗参数发送：`sendTreatmentParams()`

**指示灯控制模块 (Light Control Module)**
```java
// 指示灯控制逻辑
public class LightController {
    public void lightUpRecommendedHeads(List<TreatmentHeadRecommendation> recommendations);
    public void assignLightColorsByBodyPart(List<TreatmentHeadRecommendation> recommendations, 
                                           List<BodyPartPatchRequest> bodyPartPatches);
    public Map<String, String> generateBodyPartColorMapping(List<BodyPartPatchRequest> bodyPartPatches);
    public void turnOffAllLights();
    public boolean sendLightCommand(int headNumber, int lightColor);
}
```

## 组件和接口设计

### 数据传输对象 (DTOs)

#### TreatmentHeadAvailabilityRequest
```java
public class TreatmentHeadAvailabilityRequest {
    private String treatmentMode; // ON_SITE | TAKE_AWAY
    private List<BodyPartPatchRequest> bodyPartPatches; // 新格式
    
    // 向后兼容字段
    @Deprecated private int requiredCount;
    @Deprecated private List<String> bodyParts;
    @Deprecated private String patchType;
    
    // 智能计算方法
    public int calculateTotalRequiredCount();
    public int getShallowPatchCount();
    public int getDeepPatchCount();
}
```

#### BodyPartPatchRequest
```java
public class BodyPartPatchRequest {
    private String bodyPart;     // 身体部位名称
    private String patchType;    // SHALLOW | DEEP
    private int patchCount;      // 1-4个贴片
    
    // 验证方法
    public boolean isValid();
    public String getValidationError();
}
```

#### TreatmentHeadAvailabilityResponse
```java
public class TreatmentHeadAvailabilityResponse {
    private boolean sufficient;                              // 是否充足
    private int availableCount;                             // 可用总数
    private int requiredCount;                              // 需求总数
    private List<TreatmentHeadInfo> availableHeads;         // 可用治疗头列表
    private List<TreatmentHeadRecommendation> recommendations; // 推荐列表
    private String message;                                 // 详细消息
    
    // 扩展信息
    private AvailabilityDetail availabilityDetail;         // 详细可用性信息
}
```

#### AvailabilityDetail (新增)
```java
public class AvailabilityDetail {
    private int shallowAvailable;    // 浅部可用数量
    private int shallowRequired;     // 浅部需求数量
    private boolean shallowSufficient; // 浅部是否充足
    
    private int deepAvailable;       // 深部可用数量
    private int deepRequired;        // 深部需求数量
    private boolean deepSufficient;  // 深部是否充足
    
    private String detailedMessage;  // 详细说明
}
```

### 核心算法设计

#### 1. 需求计算算法
```java
public class RequirementCalculationAlgorithm {
    
    /**
     * 计算总需求数量
     * 算法：遍历所有部位需求，累加贴片数量
     */
    public int calculateTotalRequired(List<BodyPartPatchRequest> patches) {
        return patches.stream()
            .mapToInt(BodyPartPatchRequest::getPatchCount)
            .sum();
    }
    
    /**
     * 按类型分类统计需求
     * 算法：按贴片类型分组，分别统计数量
     */
    public Map<String, Integer> calculateByType(List<BodyPartPatchRequest> patches) {
        return patches.stream()
            .collect(Collectors.groupingBy(
                BodyPartPatchRequest::getPatchType,
                Collectors.summingInt(BodyPartPatchRequest::getPatchCount)
            ));
    }
}
```

#### 2. 可用性检查算法
```java
public class AvailabilityCheckAlgorithm {
    
    private static final int MIN_BATTERY_LEVEL = 60;
    private static final int FULL_BATTERY_LEVEL = 100;
    
    /**
     * 治疗头可用性判断算法
     * 条件：(充电中且电量>60%) 或 (充电完成且电量=100%)
     */
    public boolean isHeadAvailable(TreatmentHeadInfo head) {
        String status = head.getStatus();
        int batteryLevel = head.getBatteryLevel();
        
        return ("CHARGING".equals(status) && batteryLevel > MIN_BATTERY_LEVEL) ||
               ("CHARGED".equals(status) && batteryLevel == FULL_BATTERY_LEVEL);
    }
    
    /**
     * 贴片类型匹配算法
     * 浅部(SHALLOW): 1-10号治疗头
     * 深部(DEEP): 11-20号治疗头
     */
    public boolean isHeadMatchPatchType(TreatmentHeadInfo head, String patchType) {
        int headNumber = head.getHeadNumber();
        
        switch (patchType.toUpperCase()) {
            case "SHALLOW": return headNumber >= 1 && headNumber <= 10;
            case "DEEP": return headNumber >= 11 && headNumber <= 20;
            default: return true;
        }
    }
}
```

#### 3. 按部位分配治疗头算法
```java
public class BodyPartAllocationAlgorithm {
    
    /**
     * 按身体部位分配治疗头算法
     * 核心逻辑：为每个身体部位分配对应数量和类型的治疗头
     */
    public Map<String, List<TreatmentHeadInfo>> allocateHeadsByBodyPart(
            List<TreatmentHeadInfo> shallowAvailable, 
            List<TreatmentHeadInfo> deepAvailable,
            List<BodyPartPatchRequest> bodyPartPatches) {
        
        Map<String, List<TreatmentHeadInfo>> allocation = new HashMap<>();
        
        // 排序可用治疗头
        List<TreatmentHeadInfo> sortedShallow = sortByPriority(shallowAvailable);
        List<TreatmentHeadInfo> sortedDeep = sortByPriority(deepAvailable);
        
        int shallowIndex = 0;
        int deepIndex = 0;
        
        // 为每个身体部位分配治疗头
        for (BodyPartPatchRequest patch : bodyPartPatches) {
            List<TreatmentHeadInfo> allocatedHeads = new ArrayList<>();
            
            for (int i = 0; i < patch.getPatchCount(); i++) {
                if ("SHALLOW".equalsIgnoreCase(patch.getPatchType())) {
                    if (shallowIndex < sortedShallow.size()) {
                        allocatedHeads.add(sortedShallow.get(shallowIndex++));
                    }
                } else if ("DEEP".equalsIgnoreCase(patch.getPatchType())) {
                    if (deepIndex < sortedDeep.size()) {
                        allocatedHeads.add(sortedDeep.get(deepIndex++));
                    }
                }
            }
            
            allocation.put(patch.getBodyPart(), allocatedHeads);
        }
        
        return allocation;
    }
}
```

#### 4. 推荐排序算法
```java
public class RecommendationSortingAlgorithm {
    
    /**
     * 治疗头优先级排序算法
     * 排序规则：
     * 1. 电量高优先 (降序)
     * 2. 使用次数少优先 (升序)
     * 3. 治疗头编号小优先 (升序，作为稳定排序的依据)
     */
    public List<TreatmentHeadInfo> sortByPriority(List<TreatmentHeadInfo> heads) {
        return heads.stream()
            .sorted((h1, h2) -> {
                // 首先按电量降序
                int batteryCompare = Integer.compare(h2.getBatteryLevel(), h1.getBatteryLevel());
                if (batteryCompare != 0) return batteryCompare;
                
                // 电量相同时按使用次数升序
                int usageCompare = Integer.compare(h1.getUsageCount(), h2.getUsageCount());
                if (usageCompare != 0) return usageCompare;
                
                // 最后按编号升序（稳定排序）
                return Integer.compare(h1.getHeadNumber(), h2.getHeadNumber());
            })
            .collect(Collectors.toList());
    }
}
```

#### 5. 指示灯颜色分配算法
```java
public class LightColorAssignmentAlgorithm {
    
    private static final int[] LIGHT_COLORS = {1, 2, 3}; // 橙、蓝、绿
    private static final String[] COLOR_NAMES = {"橙色", "蓝色", "绿色"};
    
    /**
     * 指示灯颜色分配算法
     * 规则：按身体部位分配颜色，同一部位的所有治疗头使用相同颜色
     * 不同部位按顺序分配橙色、蓝色、绿色，超过3个部位时循环使用
     */
    public void assignLightColorsByBodyPart(List<TreatmentHeadRecommendation> recommendations,
                                           List<BodyPartPatchRequest> bodyPartPatches) {
        // 创建部位到颜色的映射
        Map<String, Integer> bodyPartColorMap = new HashMap<>();
        Map<String, String> bodyPartColorNameMap = new HashMap<>();
        
        // 为每个部位分配颜色
        for (int i = 0; i < bodyPartPatches.size(); i++) {
            String bodyPart = bodyPartPatches.get(i).getBodyPart();
            int colorIndex = i % LIGHT_COLORS.length;
            
            bodyPartColorMap.put(bodyPart, LIGHT_COLORS[colorIndex]);
            bodyPartColorNameMap.put(bodyPart, COLOR_NAMES[colorIndex]);
        }
        
        // 为推荐的治疗头分配对应部位的颜色
        for (TreatmentHeadRecommendation rec : recommendations) {
            String bodyPart = rec.getTargetBodyPart(); // 需要在推荐中记录目标部位
            
            rec.setLightColor(bodyPartColorMap.get(bodyPart));
            rec.setLightColorName(bodyPartColorNameMap.get(bodyPart));
        }
    }
    
    /**
     * 生成部位颜色映射表
     * 用于前端显示部位选择时的颜色提示
     */
    public Map<String, String> generateBodyPartColorMapping(List<BodyPartPatchRequest> bodyPartPatches) {
        Map<String, String> colorMapping = new HashMap<>();
        
        for (int i = 0; i < bodyPartPatches.size(); i++) {
            String bodyPart = bodyPartPatches.get(i).getBodyPart();
            int colorIndex = i % COLOR_NAMES.length;
            colorMapping.put(bodyPart, COLOR_NAMES[colorIndex]);
        }
        
        return colorMapping;
    }
}
```

## 数据模型设计

### 实体关系图
```mermaid
erDiagram
    TreatmentHeadAvailabilityRequest ||--o{ BodyPartPatchRequest : contains
    TreatmentHeadAvailabilityRequest ||--|| TreatmentHeadAvailabilityResponse : generates
    TreatmentHeadAvailabilityResponse ||--o{ TreatmentHeadRecommendation : contains
    TreatmentHeadAvailabilityResponse ||--|| AvailabilityDetail : includes
    TreatmentHeadRecommendation ||--|| TreatmentHeadInfo : references
    
    BodyPartPatchRequest {
        string bodyPart
        string patchType
        int patchCount
    }
    
    TreatmentHeadInfo {
        int headNumber
        int slotNumber
        int batteryLevel
        int usageCount
        string status
    }
    
    TreatmentHeadRecommendation {
        int headNumber
        int slotNumber
        int batteryLevel
        int usageCount
        string status
        int lightColor
        string lightColorName
        int priority
        string recommendationReason
        string compartmentType
        string targetBodyPart
    }
    
    AvailabilityDetail {
        int shallowAvailable
        int shallowRequired
        boolean shallowSufficient
        int deepAvailable
        int deepRequired
        boolean deepSufficient
        string detailedMessage
    }
```

### 状态转换图
```mermaid
stateDiagram-v2
    [*] --> RequestReceived : 接收贴片需求
    RequestReceived --> RequirementCalculation : 计算需求数量
    RequirementCalculation --> AvailabilityCheck : 检查可用性
    
    AvailabilityCheck --> Insufficient : 数量不足
    AvailabilityCheck --> Sufficient : 数量充足
    
    Insufficient --> ResponseGeneration : 生成不足响应
    Sufficient --> RecommendationGeneration : 生成推荐
    
    RecommendationGeneration --> LightControl : 控制指示灯
    LightControl --> ResponseGeneration : 生成成功响应
    
    ResponseGeneration --> [*] : 返回响应
```

## 错误处理设计

### 异常层次结构
```java
// 基础异常类
public class TreatmentHeadRecommendationException extends Exception {
    private String errorCode;
    private String userMessage;
    private String technicalMessage;
}

// 具体异常类型
public class InsufficientTreatmentHeadsException extends TreatmentHeadRecommendationException {
    private AvailabilityDetail availabilityDetail;
}

public class HardwareCommunicationException extends TreatmentHeadRecommendationException {
    private String hardwareCommand;
    private String hardwareResponse;
}

public class InvalidRequestException extends TreatmentHeadRecommendationException {
    private List<String> validationErrors;
}
```

### 错误处理策略
1. **输入验证错误**: 返回详细的验证错误信息，不影响系统稳定性
2. **硬件通信错误**: 记录错误日志，提供重试机制，返回友好的错误提示
3. **数量不足错误**: 返回详细的不足信息，指导用户等待或更换设备
4. **指示灯控制错误**: 记录错误但不影响推荐结果，提供降级处理

## 测试策略

### 单元测试设计
```java
@Test
public class TreatmentHeadRecommendationServiceTest {
    
    @Test
    void testMixedPatchTypeRecommendation() {
        // 测试混合贴片类型的推荐
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("颈部", "DEEP", 1)
        );
        // 验证推荐结果的正确性
    }
    
    @Test
    void testInsufficientTreatmentHeads() {
        // 测试治疗头数量不足的情况
    }
    
    @Test
    void testLightColorAssignment() {
        // 测试指示灯颜色分配的正确性
    }
    
    @Test
    void testBackwardCompatibility() {
        // 测试向后兼容性
    }
}
```

### 集成测试设计
```java
@Test
public class TreatmentHeadRecommendationIntegrationTest {
    
    @Test
    void testEndToEndRecommendationFlow() {
        // 测试完整的推荐流程
    }
    
    @Test
    void testHardwareCommunication() {
        // 测试与硬件的通信
    }
    
    @Test
    void testConcurrentRequests() {
        // 测试并发请求处理
    }
}
```

## 性能优化设计

### 缓存策略
- **治疗头状态缓存**: 缓存治疗头状态信息，减少串口查询频率
- **推荐结果缓存**: 对相同需求的推荐结果进行短期缓存
- **指示灯状态缓存**: 缓存当前指示灯状态，避免重复发送指令

### 并发处理
- **请求队列**: 使用队列处理并发的推荐请求
- **锁机制**: 对硬件通信操作加锁，确保指令顺序执行
- **异步处理**: 指示灯控制采用异步处理，不阻塞主流程

### 资源管理
- **连接池**: 管理串口连接资源
- **内存管理**: 及时释放大对象，避免内存泄漏
- **日志管理**: 控制日志输出量，避免磁盘空间占用过多