package com.Bone.BoneSys;

import com.Bone.BoneSys.controller.TreatmentProcessController;
import com.Bone.BoneSys.controller.TreatmentProcessController.*;
import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.entity.enums.*;
import com.Bone.BoneSys.repository.*;
import com.Bone.BoneSys.service.HardwareService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TreatmentProcessController单元测试
 * 测试治疗进程启动接口的功能完整性
 */
@SpringBootTest
@TestPropertySource(properties = {
    "hardware.simulator.enabled=true"
})
public class TreatmentProcessControllerTest {

    @Autowired
    private TreatmentProcessController treatmentProcessController;

    @MockBean
    private RecordRepository recordRepository;
    
    @MockBean
    private ProcessRepository processRepository;
    
    @MockBean
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @MockBean
    private HardwareService hardwareService;

    private com.Bone.BoneSys.entity.Record mockRecord;
    private com.Bone.BoneSys.entity.Process mockProcess;

    @BeforeEach
    void setUp() {
        // 创建模拟档案
        mockRecord = new com.Bone.BoneSys.entity.Record();
        mockRecord.setId(1L);
        
        // 创建模拟进程
        mockProcess = new com.Bone.BoneSys.entity.Process();
        mockProcess.setId(100L);
        mockProcess.setRecord(mockRecord);
        mockProcess.setStatus(ProcessStatus.IN_PROGRESS);
    }

    @Test
    void testStartTreatment_Success() {
        System.out.println("🧪 测试治疗进程启动 - 成功场景");
        
        // 设置模拟数据
        when(recordRepository.findById(1L)).thenReturn(Optional.of(mockRecord));
        when(processRepository.save(any(com.Bone.BoneSys.entity.Process.class))).thenReturn(mockProcess);
        when(treatmentDetailRepository.saveAll(anyList())).thenReturn(Arrays.asList());
        when(hardwareService.startTreatment(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(true);
        
        // 创建启动请求
        StartTreatmentRequest request = new StartTreatmentRequest();
        request.setRecordId(1L);
        request.setTreatmentMode(TreatmentMode.ON_SITE);
        
        TreatmentDetailRequest detail1 = new TreatmentDetailRequest();
        detail1.setBodyPart("胸部");
        detail1.setHeadNumber(1);
        detail1.setDuration(20);
        detail1.setIntensity(45);
        detail1.setFrequency(1000);
        detail1.setPatchType(PatchType.SHALLOW);
        detail1.setPatchQuantity(2);
        
        TreatmentDetailRequest detail2 = new TreatmentDetailRequest();
        detail2.setBodyPart("腰部");
        detail2.setHeadNumber(11);
        detail2.setDuration(25);
        detail2.setIntensity(60);
        detail2.setFrequency(100);
        detail2.setPatchType(PatchType.DEEP);
        detail2.setPatchQuantity(1);
        
        request.setTreatmentDetails(Arrays.asList(detail1, detail2));
        
        // 执行启动
        ApiResponse<StartTreatmentResponse> response = treatmentProcessController.startTreatment(request);
        
        // 验证响应
        assertNotNull(response, "响应不应为空");
        assertEquals(200, response.getCode(), "响应码应为200");
        assertEquals("治疗进程启动成功", response.getMessage(), "响应消息应正确");
        assertNotNull(response.getData(), "响应数据不应为空");
        
        // 验证响应数据
        StartTreatmentResponse responseData = response.getData();
        assertEquals(100L, responseData.getProcessId(), "进程ID应正确");
        assertEquals("STARTED", responseData.getStatus(), "状态应为STARTED");
        assertEquals(2, responseData.getTotalHeads(), "总治疗头数应为2");
        assertEquals(2, responseData.getStartedHeads().size(), "启动的治疗头数应为2");
        assertTrue(responseData.getStartedHeads().contains(1), "应包含治疗头1");
        assertTrue(responseData.getStartedHeads().contains(11), "应包含治疗头11");
        
        System.out.println("✅ 治疗进程启动成功验证通过");
        System.out.println("   进程ID: " + responseData.getProcessId());
        System.out.println("   启动状态: " + responseData.getStatus());
        System.out.println("   启动治疗头: " + responseData.getStartedHeads());
        
        // 验证方法调用
        verify(recordRepository, times(1)).findById(1L);
        verify(processRepository, times(1)).save(any(com.Bone.BoneSys.entity.Process.class));
        verify(treatmentDetailRepository, times(1)).saveAll(anyList());
        verify(hardwareService, times(2)).startTreatment(anyInt(), anyInt(), anyInt(), anyInt());
    }

    @Test
    void testStartTreatment_RecordNotFound() {
        System.out.println("🧪 测试治疗进程启动 - 档案不存在");
        
        // 设置模拟数据
        when(recordRepository.findById(999L)).thenReturn(Optional.empty());
        
        // 创建启动请求
        StartTreatmentRequest request = new StartTreatmentRequest();
        request.setRecordId(999L);
        request.setTreatmentMode(TreatmentMode.ON_SITE);
        request.setTreatmentDetails(Arrays.asList());
        
        // 执行启动
        ApiResponse<StartTreatmentResponse> response = treatmentProcessController.startTreatment(request);
        
        // 验证响应
        assertNotNull(response, "响应不应为空");
        assertEquals(404, response.getCode(), "响应码应为404");
        assertEquals("档案不存在", response.getMessage(), "响应消息应正确");
        
        System.out.println("✅ 档案不存在验证通过");
        
        // 验证方法调用
        verify(recordRepository, times(1)).findById(999L);
        verify(processRepository, never()).save(any(com.Bone.BoneSys.entity.Process.class));
    }

    @Test
    void testStartTreatment_PartialHardwareFailure() {
        System.out.println("🧪 测试治疗进程启动 - 部分硬件启动失败");
        
        // 设置模拟数据
        when(recordRepository.findById(1L)).thenReturn(Optional.of(mockRecord));
        when(processRepository.save(any(com.Bone.BoneSys.entity.Process.class))).thenReturn(mockProcess);
        when(treatmentDetailRepository.saveAll(anyList())).thenReturn(Arrays.asList());
        
        // 模拟部分硬件启动失败
        when(hardwareService.startTreatment(1, 20, 45, 1000)).thenReturn(true);  // 成功
        when(hardwareService.startTreatment(11, 25, 60, 100)).thenReturn(false); // 失败
        
        // 创建启动请求
        StartTreatmentRequest request = new StartTreatmentRequest();
        request.setRecordId(1L);
        request.setTreatmentMode(TreatmentMode.ON_SITE);
        
        TreatmentDetailRequest detail1 = new TreatmentDetailRequest();
        detail1.setBodyPart("胸部");
        detail1.setHeadNumber(1);
        detail1.setDuration(20);
        detail1.setIntensity(45);
        detail1.setFrequency(1000);
        detail1.setPatchType(PatchType.SHALLOW);
        detail1.setPatchQuantity(2);
        
        TreatmentDetailRequest detail2 = new TreatmentDetailRequest();
        detail2.setBodyPart("腰部");
        detail2.setHeadNumber(11);
        detail2.setDuration(25);
        detail2.setIntensity(60);
        detail2.setFrequency(100);
        detail2.setPatchType(PatchType.DEEP);
        detail2.setPatchQuantity(1);
        
        request.setTreatmentDetails(Arrays.asList(detail1, detail2));
        
        // 执行启动
        ApiResponse<StartTreatmentResponse> response = treatmentProcessController.startTreatment(request);
        
        // 验证响应
        assertNotNull(response, "响应不应为空");
        assertEquals(200, response.getCode(), "响应码应为200");
        assertNotNull(response.getData(), "响应数据不应为空");
        
        // 验证响应数据
        StartTreatmentResponse responseData = response.getData();
        assertEquals(1, responseData.getStartedHeads().size(), "启动的治疗头数应为1");
        assertTrue(responseData.getStartedHeads().contains(1), "应包含成功启动的治疗头1");
        assertFalse(responseData.getStartedHeads().contains(11), "不应包含失败的治疗头11");
        assertTrue(responseData.getMessage().contains("1/2"), "消息应显示部分成功");
        
        System.out.println("✅ 部分硬件启动失败验证通过");
        System.out.println("   成功启动: " + responseData.getStartedHeads().size() + "/" + responseData.getTotalHeads());
    }

    @Test
    void testStartTreatment_AllHardwareFailure() {
        System.out.println("🧪 测试治疗进程启动 - 所有硬件启动失败");
        
        // 设置模拟数据
        when(recordRepository.findById(1L)).thenReturn(Optional.of(mockRecord));
        when(processRepository.save(any(com.Bone.BoneSys.entity.Process.class))).thenReturn(mockProcess);
        when(treatmentDetailRepository.saveAll(anyList())).thenReturn(Arrays.asList());
        when(hardwareService.startTreatment(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(false);
        
        // 创建启动请求
        StartTreatmentRequest request = new StartTreatmentRequest();
        request.setRecordId(1L);
        request.setTreatmentMode(TreatmentMode.ON_SITE);
        
        TreatmentDetailRequest detail = new TreatmentDetailRequest();
        detail.setBodyPart("胸部");
        detail.setHeadNumber(1);
        detail.setDuration(20);
        detail.setIntensity(45);
        detail.setFrequency(1000);
        detail.setPatchType(PatchType.SHALLOW);
        detail.setPatchQuantity(2);
        
        request.setTreatmentDetails(Arrays.asList(detail));
        
        // 执行启动
        ApiResponse<StartTreatmentResponse> response = treatmentProcessController.startTreatment(request);
        
        // 验证响应
        assertNotNull(response, "响应不应为空");
        assertEquals(500, response.getCode(), "响应码应为500");
        assertTrue(response.getMessage().contains("所有治疗头启动失败"), "响应消息应包含失败信息");
        
        System.out.println("✅ 所有硬件启动失败验证通过");
        
        // 验证进程被标记为取消
        verify(processRepository, times(2)).save(any(com.Bone.BoneSys.entity.Process.class));
    }

    @Test
    void testStartTreatment_HardwareException() {
        System.out.println("🧪 测试治疗进程启动 - 硬件异常");
        
        // 设置模拟数据
        when(recordRepository.findById(1L)).thenReturn(Optional.of(mockRecord));
        when(processRepository.save(any(com.Bone.BoneSys.entity.Process.class))).thenReturn(mockProcess);
        when(treatmentDetailRepository.saveAll(anyList())).thenReturn(Arrays.asList());
        when(hardwareService.startTreatment(anyInt(), anyInt(), anyInt(), anyInt()))
            .thenThrow(new RuntimeException("硬件通信异常"));
        
        // 创建启动请求
        StartTreatmentRequest request = new StartTreatmentRequest();
        request.setRecordId(1L);
        request.setTreatmentMode(TreatmentMode.ON_SITE);
        
        TreatmentDetailRequest detail = new TreatmentDetailRequest();
        detail.setBodyPart("胸部");
        detail.setHeadNumber(1);
        detail.setDuration(20);
        detail.setIntensity(45);
        detail.setFrequency(1000);
        detail.setPatchType(PatchType.SHALLOW);
        detail.setPatchQuantity(2);
        
        request.setTreatmentDetails(Arrays.asList(detail));
        
        // 执行启动
        ApiResponse<StartTreatmentResponse> response = treatmentProcessController.startTreatment(request);
        
        // 验证响应
        assertNotNull(response, "响应不应为空");
        assertEquals(500, response.getCode(), "响应码应为500");
        assertTrue(response.getMessage().contains("所有治疗头启动失败"), "响应消息应包含失败信息");
        
        System.out.println("✅ 硬件异常处理验证通过");
    }

    @Test
    void testStartTreatment_ValidateIntensityLevels() {
        System.out.println("🧪 测试治疗强度档位验证");
        
        // 设置模拟数据
        when(recordRepository.findById(1L)).thenReturn(Optional.of(mockRecord));
        when(processRepository.save(any(com.Bone.BoneSys.entity.Process.class))).thenReturn(mockProcess);
        when(treatmentDetailRepository.saveAll(anyList())).thenReturn(Arrays.asList());
        when(hardwareService.startTreatment(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(true);
        
        // 测试有效强度档位
        int[] validIntensities = {30, 45, 60};
        for (int intensity : validIntensities) {
            StartTreatmentRequest request = new StartTreatmentRequest();
            request.setRecordId(1L);
            request.setTreatmentMode(TreatmentMode.ON_SITE);
            
            TreatmentDetailRequest detail = new TreatmentDetailRequest();
            detail.setBodyPart("胸部");
            detail.setHeadNumber(1);
            detail.setDuration(20);
            detail.setIntensity(intensity);
            detail.setFrequency(1000);
            detail.setPatchType(PatchType.SHALLOW);
            detail.setPatchQuantity(2);
            
            request.setTreatmentDetails(Arrays.asList(detail));
            
            // 执行启动
            ApiResponse<StartTreatmentResponse> response = treatmentProcessController.startTreatment(request);
            
            // 验证响应
            assertEquals(200, response.getCode(), "强度档位" + intensity + "应该有效");
            
            System.out.println("   ✅ 强度档位" + intensity + "验证通过");
        }
        
        System.out.println("✅ 治疗强度档位验证通过");
    }

    @Test
    void testStartTreatment_TreatmentModes() {
        System.out.println("🧪 测试治疗模式");
        
        // 设置模拟数据
        when(recordRepository.findById(1L)).thenReturn(Optional.of(mockRecord));
        when(processRepository.save(any(com.Bone.BoneSys.entity.Process.class))).thenReturn(mockProcess);
        when(treatmentDetailRepository.saveAll(anyList())).thenReturn(Arrays.asList());
        when(hardwareService.startTreatment(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(true);
        
        // 测试现场治疗模式
        StartTreatmentRequest onSiteRequest = new StartTreatmentRequest();
        onSiteRequest.setRecordId(1L);
        onSiteRequest.setTreatmentMode(TreatmentMode.ON_SITE);
        
        TreatmentDetailRequest detail = new TreatmentDetailRequest();
        detail.setBodyPart("胸部");
        detail.setHeadNumber(1);
        detail.setDuration(20);
        detail.setIntensity(45);
        detail.setFrequency(1000);
        detail.setPatchType(PatchType.SHALLOW);
        detail.setPatchQuantity(2);
        
        onSiteRequest.setTreatmentDetails(Arrays.asList(detail));
        
        ApiResponse<StartTreatmentResponse> onSiteResponse = treatmentProcessController.startTreatment(onSiteRequest);
        assertEquals(200, onSiteResponse.getCode(), "现场治疗模式应该有效");
        
        // 测试取走治疗模式
        StartTreatmentRequest takeAwayRequest = new StartTreatmentRequest();
        takeAwayRequest.setRecordId(1L);
        takeAwayRequest.setTreatmentMode(TreatmentMode.TAKE_AWAY);
        takeAwayRequest.setTreatmentDetails(Arrays.asList(detail));
        
        ApiResponse<StartTreatmentResponse> takeAwayResponse = treatmentProcessController.startTreatment(takeAwayRequest);
        assertEquals(200, takeAwayResponse.getCode(), "取走治疗模式应该有效");
        
        System.out.println("✅ 治疗模式验证通过");
        System.out.println("   现场治疗模式: 有效");
        System.out.println("   取走治疗模式: 有效");
    }
}
