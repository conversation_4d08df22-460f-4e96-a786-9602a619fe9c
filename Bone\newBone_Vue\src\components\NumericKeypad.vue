<template>
  <div v-if="visible" class="keypad-container" :style="{ top: top + 'px', left: left + 'px' }">
    <div class="keypad-panel" role="dialog" aria-label="数字小键盘">
      <div class="keypad-header">
        <span class="keypad-title">  </span>
        <button class="keypad-close" @click="onClose" aria-label="关闭">×</button>
      </div>
      <div class="keypad-grid">
        <button v-for="n in digits" :key="n" class="key" @click="emitInput(n)">{{ n }}</button>
        <button class="key action" @click="emitDelete">删除</button>
        <button class="key" @click="emitInput('0')">0</button>
        <button class="key action primary" @click="emitConfirm">确定</button>
      </div>
      <div class="keypad-actions">
        <button class="key secondary" @click="emitClear">清空</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

const props = defineProps<{ visible: boolean; top: number; left: number }>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'input', value: string): void
  (e: 'delete'): void
  (e: 'clear'): void
  (e: 'confirm'): void
}>()

const digits = ['1','2','3','4','5','6','7','8','9']

const onClose = () => emit('close')
const emitInput = (v: string) => emit('input', v)
const emitDelete = () => emit('delete')
const emitClear = () => emit('clear')
const emitConfirm = () => emit('confirm')
</script>

<style scoped>
/* 固定定位到屏幕，不使用遮罩，不变暗 */
.keypad-container {
  position: fixed;
  z-index: 2000;
}

/* 面板缩小以贴合登录页视觉 */
.keypad-panel {
  width: 320px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.15);
  padding: 10px 12px 10px;
  user-select: none;
  border: 1px solid rgba(96,96,96,0.15);
}

.keypad-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.keypad-title {
  font-size: 16px;
  color: #3b3b3b;
  font-family: 'Microsoft YaHei', sans-serif;
}

.keypad-close {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  font-size: 22px;
  line-height: 1;
  cursor: pointer;
}

.keypad-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.key {
  height: 48px;
  border-radius: 10px;
  border: 1px solid rgba(96,96,96,0.25);
  background: linear-gradient(#ffffff, #f6f6f6);
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  font-size: 20px;
  color: #3b3b3b;
  font-family: 'Microsoft YaHei', sans-serif;
  cursor: pointer;
}

.key:active {
  transform: scale(0.98);
}

.key.action {
  background: #f6f7f9;
}

.key.action.primary {
  background: #FFE4B5;
  color: #000000;
  border-color: #FFE4B5;
}

.key.secondary {
  width: 100%;
  margin-top: 8px;
  height: 40px;
}

.keypad-actions {
  display: flex;
  gap: 8px;
}
</style> 