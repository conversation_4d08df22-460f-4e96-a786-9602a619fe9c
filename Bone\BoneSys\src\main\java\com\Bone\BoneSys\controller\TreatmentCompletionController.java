package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.TreatmentCompletionMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 治疗完成监控控制器
 * 提供治疗完成检测和通知管理的API接口
 */
@RestController
@RequestMapping("/api/treatment/completion")
@CrossOrigin(origins = "*")
public class TreatmentCompletionController {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentCompletionController.class);
    
    @Autowired
    private TreatmentCompletionMonitorService completionMonitorService;
    
    /**
     * 手动触发治疗完成检查
     * POST /api/treatment/completion/check
     */
    @PostMapping("/check")
    public ApiResponse<String> triggerManualCheck() {
        try {
            completionMonitorService.triggerManualCheck();
            logger.info("手动触发治疗完成检查成功");
            return ApiResponse.success("治疗完成检查已触发", "检查任务已执行");
        } catch (Exception e) {
            logger.error("手动触发治疗完成检查失败", e);
            return ApiResponse.error(500, "触发治疗完成检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取监控状态统计
     * GET /api/treatment/completion/statistics
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getMonitoringStatistics() {
        try {
            Map<String, Object> statistics = completionMonitorService.getMonitoringStatistics();
            return ApiResponse.success("监控状态统计获取成功", statistics);
        } catch (Exception e) {
            logger.error("获取监控状态统计失败", e);
            return ApiResponse.error(500, "获取监控状态统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置患者完成状态
     * POST /api/treatment/completion/reset-patient/{patientId}
     */
    @PostMapping("/reset-patient/{patientId}")
    public ApiResponse<String> resetPatientCompletionStatus(@PathVariable Long patientId) {
        try {
            if (patientId <= 0) {
                return ApiResponse.badRequest("患者ID必须大于0");
            }
            
            completionMonitorService.resetPatientCompletionStatus(patientId);
            logger.info("患者 {} 的完成状态已重置", patientId);
            
            return ApiResponse.success("患者完成状态重置成功", 
                                     String.format("患者 %d 的完成状态已重置", patientId));
        } catch (Exception e) {
            logger.error("重置患者 {} 完成状态失败", patientId, e);
            return ApiResponse.error(500, "重置患者完成状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置待取回状态
     * POST /api/treatment/completion/reset-pickup
     */
    @PostMapping("/reset-pickup")
    public ApiResponse<String> resetPickupStatus(@RequestBody Map<String, Object> request) {
        try {
            String compartmentType = (String) request.get("compartmentType");
            @SuppressWarnings("unchecked")
            List<Integer> headNumbers = (List<Integer>) request.get("headNumbers");
            
            if (compartmentType == null || compartmentType.trim().isEmpty()) {
                return ApiResponse.badRequest("仓位类型不能为空");
            }
            
            if (headNumbers == null || headNumbers.isEmpty()) {
                return ApiResponse.badRequest("治疗头编号列表不能为空");
            }
            
            completionMonitorService.resetPickupStatus(compartmentType, headNumbers);
            logger.info("治疗头 {} 的待取回状态已重置", headNumbers);
            
            return ApiResponse.success("待取回状态重置成功", 
                                     String.format("治疗头 %s 的待取回状态已重置", headNumbers));
        } catch (Exception e) {
            logger.error("重置待取回状态失败", e);
            return ApiResponse.error(500, "重置待取回状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除所有完成状态记录
     * POST /api/treatment/completion/clear-all
     */
    @PostMapping("/clear-all")
    public ApiResponse<String> clearAllCompletionStatus() {
        try {
            completionMonitorService.clearAllCompletionStatus();
            logger.info("所有完成状态记录已清除");
            
            return ApiResponse.success("所有完成状态已清除", "所有治疗完成状态记录已清除");
        } catch (Exception e) {
            logger.error("清除所有完成状态失败", e);
            return ApiResponse.error(500, "清除所有完成状态失败: " + e.getMessage());
        }
    }
}
