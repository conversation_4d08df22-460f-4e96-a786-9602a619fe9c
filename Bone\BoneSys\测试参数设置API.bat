@echo off
chcp 65001 > nul
echo ========================================
echo 治疗参数设置API测试脚本
echo ========================================
echo.

echo [1/4] 测试治疗参数检查API...
echo 请求: POST /api/treatment-parameters/check-availability
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"P001001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"肩颈部\",\"color\":\"#FF6B6B\",\"parameters\":{\"time\":\"15分钟\",\"intensity\":\"30\",\"frequency\":\"1000\",\"depth\":\"深部\",\"count\":2}}]}" | jq .
echo.

echo [2/4] 测试治疗头推荐生成API...
echo 请求: POST /api/treatment-parameters/generate-recommendations
curl -s -X POST http://localhost:8080/api/treatment-parameters/generate-recommendations ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"P001001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"肩颈部\",\"color\":\"#FF6B6B\",\"parameters\":{\"time\":\"15分钟\",\"intensity\":\"30\",\"frequency\":\"1000\",\"depth\":\"深部\",\"count\":2}}]}" | jq .
echo.

echo [3/4] 测试治疗头推荐服务状态...
echo 请求: GET /api/hardware/heads
curl -s "http://localhost:8080/api/hardware/heads?page=1&size=5" | jq .
echo.

echo [4/4] 测试数据库连接...
echo 请求: GET /api/dashboard/main
curl -s http://localhost:8080/api/dashboard/main | jq .
echo.

echo ========================================
echo API测试完成！
echo ========================================
echo.
echo 检查要点：
echo 1. 参数检查API应返回 code: 200 和可用性信息
echo 2. 推荐生成API应返回 code: 200 和推荐治疗头列表
echo 3. 治疗头API应返回治疗头数据
echo 4. 主界面API应返回系统状态
echo.
echo 如果出现错误：
echo - 404: API路径不存在，检查Controller路径映射
echo - 500: 服务器内部错误，检查后端日志
echo - 连接失败: 后端服务未启动
echo.
pause
