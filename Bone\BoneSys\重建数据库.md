# 数据库重建指南

## 🎯 目的
重新初始化数据库，确保有合理的治疗头基础数据，方便硬件查询时同步更新。

## 🔧 重建步骤

### 1. 停止当前应用
```bash
# 如果应用正在运行，先停止
# 在运行 ./gradlew bootRun 的终端中按 Ctrl+C
```

### 2. 重建数据库
```bash
# 方法1：使用MySQL命令行
mysql -u root -p -e "DROP DATABASE IF EXISTS bonesys;"
mysql -u root -p < BoneSys/SQL/create_database_enhanced.sql

# 方法2：使用MySQL Workbench
# 1. 打开MySQL Workbench
# 2. 删除现有的bonesys数据库
# 3. 执行 BoneSys/SQL/create_database_enhanced.sql 脚本
```

### 3. 验证初始化数据
```sql
-- 连接到数据库
USE bonesys;

-- 检查治疗头初始化数据
SELECT head_number, slot_number, realtime_status, battery_level 
FROM treatment_heads 
ORDER BY head_number;

-- 应该看到20个治疗头，状态都是TREATING，slot_number都是NULL
```

### 4. 重新启动应用
```bash
cd BoneSys
./gradlew bootRun
```

### 5. 观察启动日志
应该看到以下关键日志：
```
Starting application startup synchronization...
Synchronizing treatment heads from hardware...
Received serial_response confirmation, waiting for serial_data
Completed response with accumulated serial_data: TRZI09...
Parsed X treatment heads from hardware response
Updated treatment head X to TREATING status (not in treatment chamber)
Successfully synced X treatment heads from hardware
Application startup synchronization completed successfully
```

## 📊 预期结果

### 初始状态（重建后）
- ✅ 20个治疗头记录，编号1-20
- ✅ 所有状态为TREATING（不在治疗仓）
- ✅ 所有slot_number为NULL
- ✅ 所有battery_level为NULL

### 硬件同步后
- ✅ 在治疗仓中的治疗头：状态更新为CHARGING或CHARGED
- ✅ 在治疗仓中的治疗头：slot_number更新为实际槽位
- ✅ 在治疗仓中的治疗头：battery_level更新为实际电量
- ✅ 不在治疗仓中的治疗头：保持TREATING状态

## 🔍 验证同步结果

### 查看同步后的数据
```sql
-- 查看所有治疗头状态
SELECT 
    head_number,
    slot_number,
    realtime_status,
    battery_level,
    CASE 
        WHEN realtime_status = 'CHARGING' THEN '充电中'
        WHEN realtime_status = 'CHARGED' THEN '充电完成'
        WHEN realtime_status = 'TREATING' THEN '治疗中'
        ELSE '未知'
    END as status_desc
FROM treatment_heads 
ORDER BY head_number;

-- 查看在治疗仓中的治疗头
SELECT head_number, slot_number, battery_level, realtime_status
FROM treatment_heads 
WHERE slot_number IS NOT NULL
ORDER BY slot_number;

-- 查看不在治疗仓中的治疗头
SELECT head_number, realtime_status
FROM treatment_heads 
WHERE slot_number IS NULL
ORDER BY head_number;
```

## 🎯 成功标准

### 数据完整性
- [ ] 20个治疗头记录全部存在
- [ ] 编号1-20无重复无遗漏
- [ ] 硬件查询的治疗头状态正确更新
- [ ] 不在硬件响应中的治疗头状态为TREATING

### 功能正确性
- [ ] 启动时自动同步成功
- [ ] WebSocket通信正常
- [ ] TRZI指令解析正确
- [ ] 状态逻辑符合预期

## 🚨 常见问题

### 问题1：数据库连接失败
```
解决方案：
1. 确认MySQL服务正在运行
2. 检查用户名密码是否正确
3. 确认数据库权限设置
```

### 问题2：硬件同步失败
```
解决方案：
1. 检查WebSocket连接状态
2. 确认硬件设备在线
3. 查看详细错误日志
```

### 问题3：治疗头状态不正确
```
解决方案：
1. 手动触发同步：POST /api/hardware/sync-treatment-heads
2. 检查TRZI响应数据格式
3. 查看解析日志确认数据正确性
```

---

## 🎉 完成！

按照以上步骤重建数据库后，系统将拥有完整的治疗头基础数据，硬件查询时能够正确更新现有记录，实现完美的数据同步！
