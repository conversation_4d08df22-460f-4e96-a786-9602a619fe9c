@echo off
echo ========================================
echo System Issues Diagnosis and Fix Script
echo ========================================
echo.

echo [Step 1] Check compartment_type field in database...
mysql -u root -p bonesys -e "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'bonesys' AND TABLE_NAME = 'treatment_heads' AND COLUMN_NAME = 'compartment_type';"

if %errorlevel% neq 0 (
    echo ERROR: Database connection failed, please check MySQL service
    pause
    exit /b 1
)
echo.

echo [Step 2] Check compartment_type data...
mysql -u root -p bonesys -e "SELECT head_number, compartment_type, CASE WHEN compartment_type IS NULL THEN 'NOT_SET' WHEN compartment_type = 'SHALLOW' THEN 'SHALLOW_OK' WHEN compartment_type = 'DEEP' THEN 'DEEP_OK' ELSE 'UNKNOWN' END as status FROM treatment_heads ORDER BY head_number;"
echo.

echo [Step 3] Fix compartment_type if needed...
set /p fix_compartment="Do you need to fix compartment_type field? (y/n): "
if /i "%fix_compartment%"=="y" (
    echo Fixing compartment_type field...
    mysql -u root -p bonesys -e "ALTER TABLE treatment_heads ADD COLUMN IF NOT EXISTS compartment_type VARCHAR(10) NULL DEFAULT NULL COMMENT 'Compartment type: SHALLOW or DEEP' AFTER slot_number; UPDATE treatment_heads SET compartment_type = 'SHALLOW' WHERE head_number BETWEEN 1 AND 10 AND compartment_type IS NULL; UPDATE treatment_heads SET compartment_type = 'DEEP' WHERE head_number BETWEEN 11 AND 20 AND compartment_type IS NULL;"
    echo compartment_type field fixed successfully
)
echo.

echo [Step 4] Test backend API connection...
echo Testing health check:
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/api/health
echo.

echo Testing treatment heads API:
curl -s -w "Status: %%{http_code}\n" "http://localhost:8080/api/hardware/heads?page=1&size=3"
echo.

echo [Step 5] Test treatment parameters API...
echo Testing shallow patch:
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability -H "Content-Type: application/json" -d "{\"patientId\":\"TEST001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"wrist\",\"color\":\"#00FF00\",\"parameters\":{\"time\":\"10min\",\"intensity\":\"25\",\"frequency\":\"100\",\"depth\":\"浅部\",\"count\":2}}]}" -w "Status: %%{http_code}\n"
echo.

echo Testing deep patch:
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability -H "Content-Type: application/json" -d "{\"patientId\":\"TEST002\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"shoulder\",\"color\":\"#FF0000\",\"parameters\":{\"time\":\"15min\",\"intensity\":\"30\",\"frequency\":\"1000\",\"depth\":\"深部\",\"count\":3}}]}" -w "Status: %%{http_code}\n"
echo.

echo [Step 6] Check sync status...
echo Testing sync status API:
curl -s http://localhost:8080/api/hardware/treatment-heads/sync/status -w "Status: %%{http_code}\n"
echo.

echo [Step 7] Trigger manual sync...
echo Triggering manual sync:
curl -s -X POST http://localhost:8080/api/hardware/treatment-heads/sync/trigger -w "Status: %%{http_code}\n"
echo.

echo [Step 8] Verify results...
mysql -u root -p bonesys -e "SELECT 'Shallow heads' as type, COUNT(*) as total, COUNT(CASE WHEN compartment_type = 'SHALLOW' THEN 1 END) as correct_type FROM treatment_heads WHERE head_number BETWEEN 1 AND 10 UNION ALL SELECT 'Deep heads' as type, COUNT(*) as total, COUNT(CASE WHEN compartment_type = 'DEEP' THEN 1 END) as correct_type FROM treatment_heads WHERE head_number BETWEEN 11 AND 20;"
echo.

echo ========================================
echo Diagnosis and Fix Complete!
echo ========================================
echo.
echo Check Results:
echo 1. compartment_type field should exist and not be NULL
echo 2. Heads 1-10 should be SHALLOW, heads 11-20 should be DEEP
echo 3. API status code 200 means success
echo 4. Sync service should be running every 10 seconds
echo.
echo If issues persist:
echo 1. Check if backend is running on port 8080
echo 2. Check if frontend is running on port 5173
echo 3. Check vite.config.ts proxy configuration
echo 4. Restart backend application
echo.
echo You can also visit: http://localhost:5173/api-test
echo.
pause
