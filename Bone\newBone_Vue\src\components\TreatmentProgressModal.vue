<template>
  <div v-if="visible" class="modal-overlay" @click.self="handleOverlayClick">
    <div class="progress-modal">
      <div class="progress-container">
        <div class="progress-header">
          <h3 class="progress-title">{{ progressData.title }}</h3>
          <div class="progress-stats">
            <span class="stat-item total">总计: {{ progressData.total }}</span>
            <span class="stat-item completed">完成: {{ progressData.completed }}</span>
            <span class="stat-item failed">失败: {{ progressData.failed }}</span>
          </div>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-bar-container">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: progressData.progress + '%' }"
            ></div>
          </div>
          <span class="progress-percentage">{{ Math.round(progressData.progress) }}%</span>
        </div>
        
        <!-- 当前操作 -->
        <div v-if="progressData.currentOperation" class="current-operation">
          {{ progressData.currentOperation }}
        </div>
        
        <!-- 失败信息 -->
        <div v-if="progressData.failedHeads && progressData.failedHeads.length > 0" class="failed-heads">
          <div class="failed-header">
            <span class="failed-icon">⚠️</span>
            <span class="failed-title">失败的治疗头</span>
          </div>
          <div class="failed-list">
            <div 
              v-for="(failedHead, index) in progressData.failedHeads" 
              :key="index"
              class="failed-item"
            >
              <span class="failed-location">{{ failedHead.compartmentType }}{{ failedHead.slotNumber }}号卡槽</span>
              <span class="failed-reason">{{ failedHead.reason }}</span>
            </div>
          </div>
        </div>

        <!-- 确认按钮，只在进度完成时显示 -->
        <div v-if="isCompleted" class="confirm-button-container">
          <button class="confirm-button" @click="confirmAndClose">
            确认
          </button>
        </div>
      </div>
      
      <!-- 关闭按钮，只在未完成时显示 -->
      <img
        v-if="!isCompleted"
        class="close-button"
        src="@/assets/images/交互界面/参数设置/×.png"
        alt="关闭"
        @click="closeModal"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, onMounted, onUnmounted, watch } from 'vue'

// Props
const props = defineProps<{
  visible: boolean
  treatmentMode?: 'local' | 'takeaway' // 治疗模式：本地治疗 | 取走治疗
}>()

// Emits
const emit = defineEmits<{
  close: []
  confirm: []
  'progress-complete': [{ completed: number, failed: number, failedHeads: any[] }]
}>()

// 进度条数据
const progressData = ref({
  title: '',
  total: 0,
  completed: 0,
  failed: 0,
  progress: 0,
  currentOperation: '',
  failedHeads: [] as Array<{
    compartmentType: string
    slotNumber: number
    reason: string
  }>
})

// 判断是否完成
const isCompleted = computed(() => {
  return progressData.value.total > 0 && 
         (progressData.value.completed + progressData.value.failed >= progressData.value.total)
})

// WebSocket连接
let ws: WebSocket | null = null

// 初始化进度数据
const initializeProgress = () => {
  progressData.value = {
    title: props.treatmentMode === 'takeaway' ? '参数下载进度' : '治疗头启动进度',
    total: 0,
    completed: 0,
    failed: 0,
    progress: 0,
    currentOperation: props.treatmentMode === 'takeaway' ? '准备下载治疗参数...' : '准备启动治疗头...',
    failedHeads: []
  }
}

// 初始化WebSocket连接
const initWebSocket = () => {
  try {
    ws = new WebSocket('ws://localhost:8080/ws/notifications')
    
    ws.onopen = () => {
      console.log('进度弹窗WebSocket连接成功')
    }
    
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        if (message.type === 'STARTUP_PROGRESS' || message.type === 'DOWNLOAD_PROGRESS') {
          updateProgress(message.data)
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }
    
    ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
    }
    
    ws.onclose = () => {
      console.log('WebSocket连接关闭')
    }
  } catch (error) {
    console.error('初始化WebSocket失败:', error)
  }
}

// 更新进度数据
const updateProgress = (data: any) => {
  progressData.value = {
    title: data.currentOperation?.includes('下载') ? '参数下载进度' : '治疗头启动进度',
    total: data.total || 0,
    completed: data.completed || 0,
    failed: data.failed || 0,
    progress: data.progress || 0,
    currentOperation: data.currentOperation || '',
    failedHeads: data.failedHeads || []
  }
  
  // 如果所有治疗头都处理完成，发送完成事件
  if (data.completed + data.failed >= data.total && data.total > 0) {
    setTimeout(() => {
      emit('progress-complete', {
        completed: data.completed,
        failed: data.failed,
        failedHeads: data.failedHeads
      })
    }, 1000)
  }
}

// 监听visible变化，初始化或清理
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initializeProgress()
    initWebSocket()
  } else {
    // 清理WebSocket连接
    if (ws) {
      ws.close()
      ws = null
    }
  }
})

// 确认并关闭
const confirmAndClose = () => {
  emit('confirm')
}

// 关闭弹窗（只在未完成时允许）
const closeModal = () => {
  if (!isCompleted.value) {
    emit('close')
  }
}

// 处理遮罩点击（只在未完成时允许关闭）
const handleOverlayClick = () => {
  if (!isCompleted.value) {
    emit('close')
  }
}

// 组件卸载时清理WebSocket连接
onUnmounted(() => {
  if (ws) {
    ws.close()
    ws = null
  }
})

// 暴露方法给父组件
defineExpose({
  updateProgress,
  setProgress: (data: any) => updateProgress(data)
})
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-modal {
  position: relative;
  max-width: 600px;
  width: 90%;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
  animation: modalAppear 0.3s ease-out;
}

@keyframes modalAppear {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

/* 进度条容器样式 */
.progress-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 18px;
  padding: 30px;
  border: 3px solid #5cd4c8;
  box-shadow: 0 4px 20px rgba(92, 212, 200, 0.2);
}

/* 进度条头部 */
.progress-header {
  margin-bottom: 20px;
}

.progress-title {
  font-size: 24px;
  font-weight: 600;
  color: #2d3436;
  margin: 0 0 12px 0;
  text-align: center;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 12px;
}

.stat-item {
  font-size: 16px;
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.stat-item.total {
  background: #e3f2fd;
  color: #1976d2;
}

.stat-item.completed {
  background: #e8f5e8;
  color: #388e3c;
}

.stat-item.failed {
  background: #ffebee;
  color: #d32f2f;
}

/* 进度条样式 */
.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 16px;
}

.progress-bar {
  flex: 1;
  height: 16px;
  background: #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #5cd4c8 0%, #4ecdc4 50%, #45b7b8 100%);
  border-radius: 8px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-percentage {
  font-size: 16px;
  font-weight: 600;
  color: #2d3436;
  min-width: 50px;
  text-align: right;
}

/* 当前操作文本 */
.current-operation {
  text-align: center;
  font-size: 18px;
  color: #636e72;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(92, 212, 200, 0.1);
  border-radius: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* 失败信息样式 */
.failed-heads {
  margin-top: 20px;
  border-top: 2px solid #dee2e6;
  padding-top: 20px;
}

.failed-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.failed-icon {
  font-size: 20px;
  margin-right: 10px;
}

.failed-title {
  font-size: 18px;
  font-weight: 600;
  color: #d63031;
}

.failed-list {
  max-height: 150px;
  overflow-y: auto;
}

.failed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  margin-bottom: 6px;
  background: #fff5f5;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.failed-location {
  font-weight: 600;
  color: #d63031;
  font-size: 16px;
}

.failed-reason {
  font-size: 14px;
  color: #636e72;
  text-align: right;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 确认按钮 */
.confirm-button-container {
  margin-top: 24px;
  text-align: center;
}

.confirm-button {
  background: linear-gradient(135deg, #5cd4c8 0%, #4ecdc4 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(92, 212, 200, 0.3);
}

.confirm-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(92, 212, 200, 0.4);
}

.confirm-button:active {
  transform: translateY(0);
}

/* 关闭按钮 */
.close-button {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: transform 0.2s ease;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.close-button:hover {
  transform: scale(1.1);
}

.close-button:active {
  transform: scale(0.95);
}
</style> 