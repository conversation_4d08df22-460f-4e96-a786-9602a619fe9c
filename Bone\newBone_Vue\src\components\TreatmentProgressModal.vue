<template>
  <div v-if="visible" class="modal-overlay" @click.self="handleOverlayClick">
    <div class="progress-modal">
      <div class="progress-container">
        <div class="progress-header">
          <h3 class="progress-title">{{ progressData.title }}</h3>
          <div class="progress-stats">
            <span class="stat-item total">总计: {{ progressData.total }}</span>
            <span class="stat-item completed">完成: {{ progressData.completed }}</span>
            <span class="stat-item failed">失败: {{ progressData.failed }}</span>
          </div>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-bar-container">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: progressData.progress + '%' }"
            ></div>
          </div>
          <span class="progress-percentage">{{ Math.round(progressData.progress) }}%</span>
        </div>
        
        <!-- 当前操作 -->
        <div v-if="progressData.currentOperation" class="current-operation">
          {{ progressData.currentOperation }}
        </div>
        
        <!-- 失败信息 -->
        <div v-if="progressData.failedHeads && progressData.failedHeads.length" class="failed-list">
          <div class="failed-header">
            <span class="failed-icon">⚠️</span>
            <span class="failed-title">启动失败的治疗头</span>
          </div>
          <div 
            v-for="(item, index) in progressData.failedHeads" 
            :key="index" 
            class="failed-item"
          >
            <span class="failed-location">{{ item.location }}</span>
            <span class="failed-reason">{{ item.reason }}</span>
          </div>
        </div>

        <!-- 确认按钮（完成后显示） -->
        <div class="confirm-button-container" v-if="isCompleted">
          <button class="confirm-button" @click="confirmAndClose">
            确认
          </button>
        </div>
      </div>
      
      <!-- 关闭按钮，只在未完成时显示 -->
      <img
        v-if="!isCompleted"
        class="close-button"
        :src="closeIcon"
        alt="关闭"
        @click="closeModal"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import closeIcon from '@/assets/images/交互界面/参数设置/×.png'
import { defineProps, defineEmits, computed, ref, onMounted, onUnmounted, watch } from 'vue'

// Props
const props = defineProps<{
  visible: boolean
  treatmentMode?: 'local' | 'takeaway' // 治疗模式：本地治疗 | 取走治疗
}>()

// Emits
const emit = defineEmits<{
  close: []
  confirm: []
  'progress-complete': [{ completed: number, failed: number, failedHeads: any[] }]
}>()

// 遮罩点击关闭
const handleOverlayClick = () => emit('close')

// 内部状态
const ws = ref<WebSocket | null>(null)
const isCompleted = computed(() => progressData.value.progress >= 100)
const progressData = ref({
  title: '',
  total: 0,
  completed: 0,
  failed: 0,
  progress: 0,
  currentOperation: '',
  failedHeads: [] as Array<{ location: string; reason: string }>
})

// 关闭与确认
const closeModal = () => emit('close')
const confirmAndClose = () => {
  emit('confirm')
  emit('close')
}

// 初始化进度数据
const initializeProgress = () => {
  progressData.value = {
    title: props.treatmentMode === 'takeaway' ? '参数下载进度' : '治疗头启动进度',
    total: 0,
    completed: 0,
    failed: 0,
    progress: 0,
    currentOperation: props.treatmentMode === 'takeaway' ? '准备下载治疗参数...' : '准备启动治疗头...',
    failedHeads: []
  }
}

// 解析并应用进度数据（兼容两种结构）
function applyProgressPayload(payload: any) {
  if (!payload) return

  // 新结构：{ type: 'STARTUP_PROGRESS'|'DOWNLOAD_PROGRESS', data: { ... } }
  if (payload.type === 'STARTUP_PROGRESS' || payload.type === 'DOWNLOAD_PROGRESS') {
    const d = payload.data || {}
    progressData.value.title = payload.type === 'DOWNLOAD_PROGRESS' ? '参数下载进度' : '治疗头启动进度'
    progressData.value.total = d.total ?? progressData.value.total
    progressData.value.completed = d.completed ?? progressData.value.completed
    progressData.value.failed = d.failed ?? progressData.value.failed
    progressData.value.progress = d.progress ?? progressData.value.progress
    progressData.value.currentOperation = d.currentOperation ?? progressData.value.currentOperation
    if (Array.isArray(d.failedHeads)) progressData.value.failedHeads = d.failedHeads
    return
  }

  // 旧结构：直接含有进度字段
  const d = payload
  if (typeof d === 'object') {
    progressData.value.total = d.total ?? progressData.value.total
    progressData.value.completed = d.completed ?? progressData.value.completed
    progressData.value.failed = d.failed ?? progressData.value.failed
    progressData.value.progress = d.progress ?? progressData.value.progress
    progressData.value.currentOperation = d.currentOperation ?? progressData.value.currentOperation
    if (Array.isArray(d.failedHeads)) progressData.value.failedHeads = d.failedHeads
  }
}

// 初始化WebSocket连接（切换到通知通道）
const initWebSocket = () => {
  const url = 'ws://localhost:8080/ws/notifications'
  try {
    ws.value = new WebSocket(url)
    ws.value.onopen = () => {
      // 通知通道为广播，无需发送start动作
      // 保留占位，便于后续扩展订阅过滤
      // ws.value?.send(JSON.stringify({ action: 'subscribe', topic: 'progress' }))
    }
    ws.value.onmessage = (evt) => {
      try {
        const msg = JSON.parse(evt.data)
        applyProgressPayload(msg)

        if (progressData.value.progress >= 100) {
          emit('progress-complete', {
            completed: progressData.value.completed,
            failed: progressData.value.failed,
            failedHeads: progressData.value.failedHeads
          })
        }
      } catch (e) {
        console.error('解析进度数据失败：', e)
      }
    }
    ws.value.onerror = (e) => {
      console.error('WebSocket error', e)
    }
    ws.value.onclose = () => {
      // 不自动重连，避免与页面生命周期冲突
    }
  } catch (e) {
    console.error('WebSocket 初始化失败：', e)
  }
}

onMounted(() => {
  if (props.visible) {
    initializeProgress()
    initWebSocket()
  }
})

onUnmounted(() => {
  ws.value?.close()
})

watch(() => props.visible, (v) => {
  if (v) {
    initializeProgress()
    initWebSocket()
  } else {
    ws.value?.close()
  }
})
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-modal {
  position: relative;
  width: 560px;
  max-width: 90vw;
  background: #ffffff;
  border-radius: 16px;
  padding: 20px 24px 28px;
  box-shadow: 0 16px 48px rgba(0,0,0,0.18);
  border: 2px solid #5cd4c8;
  box-shadow: 0 4px 20px rgba(92, 212, 200, 0.2);
  font-family: 'Microsoft YaHei','微软雅黑',sans-serif;
}

/* 进度条头部 */
.progress-header {
  margin-bottom: 20px;
}

.progress-title {
  font-size: 19px; /* 4号 */
  font-weight: 600;
  color: #2d3436;
  margin: 0 0 12px 0;
  text-align: center;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 12px;
}

.stat-item {
  font-size: 16.67px; /* 6号 */
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.stat-item.total {
  background: #e3f2fd;
  color: #1976d2;
}

.stat-item.completed {
  background: #e6f7f2;
  color: #2e7d32;
}

.stat-item.failed {
  background: #ffebee;
  color: #e27172; /* 淡红 */
}

/* 进度条 */
.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 10px 0 14px;
}

.progress-bar {
  flex: 1;
  height: 16px;
  background: #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  /* 保留原有渐变，不做修改 */
  background: linear-gradient(90deg, #5cd4c8 0%, #4ecdc4 50%, #45b7b8 100%);
  border-radius: 8px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-percentage {
  font-size: 16.67px; /* 6号 */
  font-weight: 600;
  color: #b9babb; /* 浅灰 */
  min-width: 50px;
  text-align: right;
}

/* 当前操作 */
.current-operation {
  text-align: center;
  font-size: 19px; /* 4号 */
  color: #6a6767; /* 中灰 */
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(92, 212, 200, 0.1);
  border-radius: 8px;
  animation: pulse 2s infinite;
}

/* 失败列表 */
.failed-list {
  background: #fafafa;
  border-radius: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
}

.failed-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.failed-icon {
  font-size: 16px;
}

.failed-title {
  font-size: 33.33px; /* 4号 */
  font-weight: 600;
  color: #e27172; /* 淡红 */
}

.failed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  margin-bottom: 6px;
  background: #fff5f5;
  border-radius: 8px;
  border: 1px solid #ffe0e0;
}

.failed-location {
  font-weight: 600;
  color: #e27172; /* 淡红 */
  font-size: 19px; /* 4号 */
}

.failed-reason {
  font-size: 16.67px; /* 6号 */
  color: #6a6767; /* 中灰 */
  text-align: right;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 确认按钮 */
.confirm-button-container {
  display: flex;
  justify-content: center;
  margin-top: 14px;
}

.confirm-button {
  background: linear-gradient(135deg, #5cd4c8 0%, #4ecdc4 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(92, 212, 200, 0.3);
}

.confirm-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(92, 212, 200, 0.35);
}

.confirm-button:active {
  transform: translateY(0);
  box-shadow: 0 3px 10px rgba(92, 212, 200, 0.25);
}

/* 关闭按钮 */
.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 36px;
  height: 36px;
  cursor: pointer;
  transition: transform 0.2s ease;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.close-button:hover {
  transform: scale(1.1);
}

.close-button:active {
  transform: scale(0.95);
}

/* 动效 */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(92,212,200,0.25); }
  70% { box-shadow: 0 0 0 10px rgba(92,212,200,0); }
  100% { box-shadow: 0 0 0 0 rgba(92,212,200,0); }
}
</style>
