# FREEBONE医疗系统 - 最终测试步骤

## 🎯 测试目标

验证以下功能完全正常：
1. **数据库结构修改** - 治疗头约束调整，贴片数量1-6支持
2. **启动时自动同步** - 软件启动时自动查询硬件治疗头状态
3. **智能状态管理** - 根据治疗头位置和电量智能设置状态
4. **治疗详情自动更新** - 检测归还的治疗头并更新状态
5. **WebSocket通信优化** - 移除固定延迟，提升响应速度

## 🔧 1. 数据库重建测试

### 1.1 重建数据库
```bash
# 1. 删除现有数据库（如果存在）
mysql -u root -p -e "DROP DATABASE IF EXISTS bonesys;"

# 2. 使用新的数据库脚本重建
mysql -u root -p < BoneSys/SQL/create_database_enhanced.sql
```

### 1.2 验证数据库结构
```sql
-- 检查治疗头表约束
SHOW CREATE TABLE bonesys.treatment_heads;

-- 检查治疗参数预设表
DESCRIBE bonesys.treatment_parameter_presets;

-- 验证贴片数量范围字段
SELECT preset_name, body_part, min_patch_count, max_patch_count 
FROM bonesys.treatment_parameter_presets;
```

## 🚀 2. 应用启动测试

### 2.1 启动应用并观察日志
```bash
cd BoneSys
./gradlew bootRun
```

### 2.2 关键日志检查点
观察以下日志信息：
- ✅ `Starting application startup synchronization...`
- ✅ `Synchronizing treatment heads from hardware...`
- ✅ `Processing WebSocket message: {"type": "serial_response"}`
- ✅ `Processing WebSocket message: {"type": "serial_data"}`
- ✅ `Parsed X treatment heads from hardware response`
- ✅ `Successfully synced X treatment heads from hardware`
- ✅ `Application startup synchronization completed successfully`

## 🔍 3. 硬件通信测试

### 3.1 TRZI指令测试
```bash
# 测试TRZI查询指令
curl -X POST http://localhost:8080/api/hardware/sync-treatment-heads
```

### 3.2 验证响应时间
- ⏱️ **期望延迟**: 2-5秒（硬件处理时间）
- ⏱️ **不应超过**: 10秒
- ✅ **无固定延迟**: 不再有人为的3秒延迟

### 3.3 检查解析结果
观察日志中的解析信息：
- ✅ 正确识别100%电量（3字符）
- ✅ 正确识别其他电量（2字符）
- ✅ 正确解析槽位号
- ✅ 智能状态设置

## 📊 4. 数据库同步验证

### 4.1 查询治疗头数据
```sql
-- 查看同步后的治疗头数据
SELECT head_number, slot_number, battery_level, realtime_status 
FROM bonesys.treatment_heads 
ORDER BY head_number;
```

### 4.2 验证状态逻辑
检查状态设置是否正确：
- ✅ **在治疗仓 + 电量100%** → `CHARGED`
- ✅ **在治疗仓 + 电量<100%** → `CHARGING`
- ✅ **不在治疗仓** → `TREATING`

### 4.3 检查约束
```sql
-- 验证槽号约束（应该允许重复）
SELECT slot_number, COUNT(*) as count 
FROM bonesys.treatment_heads 
GROUP BY slot_number 
HAVING count > 1;
```

## 🎮 5. 治疗参数设置测试

### 5.1 测试贴片数量范围
```bash
# 测试1-6个贴片的参数设置
curl -X POST http://localhost:8080/api/treatment-parameters/check-availability \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": "TEST001",
    "treatmentMode": "takeaway",
    "bodyParts": [{
      "name": "肩颈部",
      "parameters": {
        "count": 6
      }
    }]
  }'
```

### 5.2 验证参数预设
```sql
-- 检查参数预设的贴片数量范围
SELECT preset_name, body_part, recommended_count, min_patch_count, max_patch_count
FROM bonesys.treatment_parameter_presets
WHERE body_part = '肩颈部';
```

## 🔄 6. 治疗详情状态更新测试

### 6.1 模拟治疗流程
1. 创建治疗记录（状态：AWAITING_RETURN）
2. 将治疗头放回治疗仓
3. 触发硬件同步
4. 验证状态自动更新为RETURNED

### 6.2 验证自动更新
```sql
-- 查看治疗详情状态变化
SELECT id, head_number_used, status, updated_at 
FROM bonesys.treatment_details 
WHERE status = 'RETURNED' 
ORDER BY updated_at DESC;
```

## 🧪 7. 完整集成测试

### 7.1 运行自动化测试
```bash
# 运行完整测试套件
./gradlew test

# 运行特定的硬件测试
./gradlew test --tests BoneSysApplicationTests
./gradlew test --tests WebSocketHardwareTest
```

### 7.2 性能测试
```bash
# 测试多次同步的性能
for i in {1..5}; do
  echo "Test $i:"
  time curl -X POST http://localhost:8080/api/hardware/sync-treatment-heads
  echo "---"
done
```

## ✅ 8. 验收标准

### 8.1 功能验收
- [ ] 应用启动时自动同步治疗头数据
- [ ] TRZI指令解析完全正确（支持可变电量字段）
- [ ] 治疗头状态智能设置（充电中/充电完成/治疗中）
- [ ] 治疗详情状态自动更新（RETURNED）
- [ ] 贴片数量支持1-6个
- [ ] 槽号和编号可以不一一对应

### 8.2 性能验收
- [ ] WebSocket响应时间 < 10秒
- [ ] 无人为固定延迟
- [ ] 数据库操作无约束冲突
- [ ] 启动同步成功率 > 95%

### 8.3 稳定性验收
- [ ] 连续运行24小时无异常
- [ ] 多次同步无数据丢失
- [ ] 异常情况下优雅降级
- [ ] 日志信息完整清晰

## 🎯 9. 预期结果

### 9.1 成功指标
- ✅ **硬件通信**: WebSocket连接稳定，TRZI指令100%成功
- ✅ **数据解析**: 智能识别2/3字符电量，解析准确率100%
- ✅ **状态管理**: 治疗头状态实时准确，自动更新及时
- ✅ **用户体验**: 响应速度快，操作流畅
- ✅ **系统稳定**: 长期运行稳定，异常处理完善

### 9.2 关键指标
- 🎯 **同步成功率**: ≥ 95%
- 🎯 **响应时间**: ≤ 10秒
- 🎯 **数据准确率**: 100%
- 🎯 **系统可用性**: ≥ 99%

---

## 🚀 开始测试

按照以上步骤逐一执行测试，确保每个功能点都能正常工作。如有问题，请查看详细日志并根据错误信息进行调试。

**祝测试顺利！** 🎉
