package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.User;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import com.Bone.BoneSys.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/data")
@CrossOrigin(origins = "*")
public class DataInitController {

    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    @Autowired
    private UserRepository userRepository;

    @PostMapping("/init-treatment-heads")
    public ResponseEntity<Map<String, Object>> initTreatmentHeads() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 清空现有数据
            treatmentHeadRepository.deleteAll();
            
            // 创建20个治疗头
            List<TreatmentHead> treatmentHeads = new ArrayList<>();
            
            // 上仓浅部治疗头 (槽号1-10)
            treatmentHeads.add(createTreatmentHead(1, 1, 0, TreatmentHeadStatus.CHARGING, 50, 5, 100, 500));
            treatmentHeads.add(createTreatmentHead(2, 2, 0, TreatmentHeadStatus.CHARGING, 60, 4, 80, 500));
            treatmentHeads.add(createTreatmentHead(3, 3, 0, TreatmentHeadStatus.CHARGED, 100, 10, 200, 500));
            treatmentHeads.add(createTreatmentHead(4, 4, 0, TreatmentHeadStatus.CHARGED, 100, 8, 160, 500));
            treatmentHeads.add(createTreatmentHead(5, 5, 0, TreatmentHeadStatus.CHARGING, 70, 3, 60, 500));
            treatmentHeads.add(createTreatmentHead(6, 6, 0, TreatmentHeadStatus.CHARGED, 100, 12, 240, 500));
            treatmentHeads.add(createTreatmentHead(7, 7, 0, TreatmentHeadStatus.TREATING, 85, 6, 120, 500));
            treatmentHeads.add(createTreatmentHead(8, 8, 0, TreatmentHeadStatus.CHARGED, 100, 9, 180, 500));
            treatmentHeads.add(createTreatmentHead(9, 9, 0, TreatmentHeadStatus.CHARGING, 80, 7, 140, 500));
            treatmentHeads.add(createTreatmentHead(10, 10, 0, TreatmentHeadStatus.CHARGED, 100, 5, 100, 500));
            
            // 下仓深部治疗头 (槽号11-20)
            treatmentHeads.add(createTreatmentHead(11, 11, 0, TreatmentHeadStatus.CHARGING, 65, 8, 160, 500));
            treatmentHeads.add(createTreatmentHead(12, 12, 0, TreatmentHeadStatus.CHARGED, 100, 11, 220, 500));
            treatmentHeads.add(createTreatmentHead(13, 13, 0, TreatmentHeadStatus.CHARGING, 55, 4, 80, 500));
            treatmentHeads.add(createTreatmentHead(14, 14, 0, TreatmentHeadStatus.CHARGED, 100, 6, 120, 500));
            treatmentHeads.add(createTreatmentHead(15, 15, 0, TreatmentHeadStatus.TREATING, 90, 9, 180, 500));
            treatmentHeads.add(createTreatmentHead(16, 16, 0, TreatmentHeadStatus.CHARGED, 100, 13, 260, 500));
            treatmentHeads.add(createTreatmentHead(17, 17, 0, TreatmentHeadStatus.CHARGING, 75, 5, 100, 500));
            treatmentHeads.add(createTreatmentHead(18, 18, 0, TreatmentHeadStatus.CHARGED, 100, 10, 200, 500));
            treatmentHeads.add(createTreatmentHead(19, 19, 0, TreatmentHeadStatus.CHARGING, 85, 7, 140, 500));
            treatmentHeads.add(createTreatmentHead(20, 20, 0, TreatmentHeadStatus.CHARGED, 100, 8, 160, 500));
            
            // 保存所有治疗头
            treatmentHeadRepository.saveAll(treatmentHeads);
            
            response.put("success", true);
            response.put("message", "Treatment heads initialized successfully");
            response.put("count", treatmentHeads.size());
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to initialize treatment heads: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/init-user")
    public ResponseEntity<Map<String, Object>> initUser() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查是否已存在用户
            if (!userRepository.existsById(1)) {
                User user = new User();
                user.setId(1);
                user.setUsername("admin");
                user.setPassword("123456");
                
                userRepository.save(user);
                
                response.put("success", true);
                response.put("message", "用户数据初始化成功");
            } else {
                response.put("success", true);
                response.put("message", "用户已存在，无需初始化");
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "用户数据初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/init-all")
    public ResponseEntity<Map<String, Object>> initAllData() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 初始化用户
            initUser();
            
            // 初始化治疗头
            initTreatmentHeads();
            
            response.put("success", true);
            response.put("message", "所有基础数据初始化成功");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "数据初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getDataStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            long userCount = userRepository.count();
            long treatmentHeadCount = treatmentHeadRepository.count();
            
            Map<String, Long> counts = new HashMap<>();
            counts.put("users", userCount);
            counts.put("treatment_heads", treatmentHeadCount);
            
            response.put("success", true);
            response.put("counts", counts);
            response.put("message", "数据状态查询成功");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "数据状态查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
    
    private TreatmentHead createTreatmentHead(int headNumber, int slotNumber, int lightColor, 
                                            TreatmentHeadStatus status, int batteryLevel, 
                                            int totalUsageCount, int totalUsageMinutes, int maxUsageCount) {
        TreatmentHead head = new TreatmentHead();
        head.setHeadNumber(headNumber);
        head.setSlotNumber(slotNumber);
        head.setLightColor(lightColor);
        head.setRealtimeStatus(status);
        head.setBatteryLevel(batteryLevel);
        head.setTotalUsageCount(totalUsageCount);
        head.setTotalUsageMinutes(totalUsageMinutes);
        head.setMaxUsageCount(maxUsageCount);
        return head;
    }
}