# FREEBONE医疗系统设计文档

## 概述

本FREEBONE医疗系统采用前后端分离架构，后端使用SpringBoot 3.4.7提供RESTful API服务，前端使用Vue 3构建用户界面。系统通过串口通信控制治疗头硬件设备，支持现场治疗和带走治疗两种模式。系统设计遵循按页面组织数据的原则，为每个UI界面提供专门的数据接口。

### 核心特性
- 基于页面的数据组织和传输（固定UI界面少于20页）
- 串口硬件通信控制（6种硬件指令）
- 双重密码认证机制（厂家密码+用户密码）
- 实时设备状态监控和管理
- 治疗进程全生命周期管理
- 患者档案和统计数据管理

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "前端层 (Vue 3)"
        A[登录界面] --> B[主界面]
        B --> C[新建档案界面]
        B --> D[档案管理界面]
        B --> E[进程管理界面]
        C --> F[患者信息录入]
        F --> G[参数设置界面]
        G --> H[治疗头检查]
        H --> I[贴片指导界面]
        I --> J[治疗进程界面]
        D --> K[个人信息界面]
        E --> L[治疗头管理界面]
        B --> M[系统设置界面]
    end
    
    subgraph "后端层 (SpringBoot 3.4.7)"
        N[Controller层] --> O[Service层]
        O --> P[Repository层]
        O --> Q[串口通信服务]
        O --> R[硬件指令解析器]
    end
    
    subgraph "数据层"
        S[(MySQL 8.0)]
    end
    
    subgraph "硬件层"
        T[治疗头设备]
        U[控制板]
    end
    
    A -.-> N
    N --> S
    Q --> U
    U --> T
```

### 技术栈架构
- **前端**: Vue 3 + Vue Router + Axios + Element Plus UI
- **后端**: SpringBoot 3.4.7 + Spring Data JPA + Spring Web + Spring Security
- **数据库**: MySQL 8.0
- **硬件通信**: jSerialComm库进行串口通信
- **认证**: JWT Token认证
- **构建工具**: Gradle (后端) + Vite (前端)

## 组件和接口设计

### 后端API接口设计

#### 1. 用户认证模块
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@RequestBody LoginRequest request) {
        // 验证用户名和密码（支持厂家密码和用户密码）
        // 生成JWT token
        // 更新last_updated_at字段
    }
    
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout() {
        // 清除token，登出处理
    }
}
```

#### 2. 主界面数据模块
```java
@RestController
@RequestMapping("/api/dashboard")
public class DashboardController {
    
    @GetMapping("/main")
    public ResponseEntity<ApiResponse<DashboardResponse>> getMainDashboard() {
        // 返回主界面所需的系统信息和快捷操作
    }
}
```

#### 3. 患者档案管理模块
```java
@RestController
@RequestMapping("/api/patients")
public class PatientController {
    
    @GetMapping("/recent")
    public ResponseEntity<ApiResponse<RecentPatientsResponse>> getRecentPatients(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        // 返回新建档案界面所需的最近患者信息
    }
    
    @PostMapping
    public ResponseEntity<ApiResponse<PatientResponse>> createPatient(@RequestBody PatientRequest request) {
        // 创建新患者，验证就诊卡号唯一性
    }
    
    @GetMapping("/{patientId}/profile")
    public ResponseEntity<ApiResponse<PatientProfileResponse>> getPatientProfile(@PathVariable Long patientId) {
        // 返回个人信息界面所需的患者详细信息
    }
    
    @PutMapping("/{patientId}")
    public ResponseEntity<ApiResponse<PatientResponse>> updatePatient(
            @PathVariable Long patientId, @RequestBody PatientRequest request) {
        // 更新患者基本信息
    }
}
```

#### 4. 档案管理模块
```java
@RestController
@RequestMapping("/api/records")
public class RecordController {
    
    @GetMapping("/list")
    public ResponseEntity<ApiResponse<RecordListResponse>> getRecordList(
            @RequestParam(required = false) String patientCardId,
            @RequestParam(required = false) String patientName,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        // 返回档案管理界面所需的档案列表数据
    }
}
```

#### 5. 治疗参数设置模块
```java
@RestController
@RequestMapping("/api/treatment")
public class TreatmentController {
    
    @GetMapping("/body-parts")
    public ResponseEntity<ApiResponse<BodyPartsResponse>> getBodyParts() {
        // 返回参数设置界面所需的身体部位配置
    }
    
    @PostMapping("/settings/validate")
    public ResponseEntity<ApiResponse<ValidationResponse>> validateTreatmentSettings(
            @RequestBody TreatmentSettingsRequest request) {
        // 验证治疗参数的有效性和安全性
    }
    
    @PostMapping("/start")
    public ResponseEntity<ApiResponse<TreatmentResponse>> startTreatment(
            @RequestBody StartTreatmentRequest request) {
        // 开始治疗进程
    }
    
    @GetMapping("/status/{processId}")
    public ResponseEntity<ApiResponse<TreatmentStatusResponse>> getTreatmentStatus(
            @PathVariable Long processId) {
        // 获取治疗进程状态
    }
    
    @PostMapping("/complete/{processId}")
    public ResponseEntity<ApiResponse<TreatmentCompleteResponse>> completeTreatment(
            @PathVariable Long processId) {
        // 完成治疗进程
    }
    
    @PostMapping("/terminate/{processId}")
    public ResponseEntity<ApiResponse<Void>> terminateTreatment(
            @PathVariable Long processId, @RequestBody TerminateRequest request) {
        // 终止治疗进程
    }
}
```

#### 6. 治疗头设备管理模块
```java
@RestController
@RequestMapping("/api/treatment-heads")
public class TreatmentHeadController {
    
    @GetMapping("/availability")
    public ResponseEntity<ApiResponse<AvailabilityResponse>> checkAvailability(
            @RequestParam int requiredCount) {
        // 检查治疗头可用性
    }
    
    @GetMapping("/list")
    public ResponseEntity<ApiResponse<TreatmentHeadListResponse>> getTreatmentHeadList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        // 返回治疗头管理界面所需的设备列表
    }
    
    @PostMapping("/patch-selection")
    public ResponseEntity<ApiResponse<PatchSelectionResponse>> getPatchSelection(
            @RequestBody PatchSelectionRequest request) {
        // 返回贴片选择界面所需的指导信息
    }
}
```

#### 7. 进程管理模块
```java
@RestController
@RequestMapping("/api/processes")
public class ProcessController {
    
    @GetMapping("/list")
    public ResponseEntity<ApiResponse<ProcessListResponse>> getProcessList(
            @RequestParam(required = false) String patientCardId,
            @RequestParam(required = false) String patientName,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        // 返回进程管理界面所需的进程列表
    }
}
```

#### 8. 硬件通信模块
```java
@RestController
@RequestMapping("/api/hardware")
public class HardwareController {
    
    @GetMapping("/treatment-heads/query")
    public ResponseEntity<ApiResponse<HardwareQueryResponse>> queryTreatmentHeads() {
        // 通过串口查询治疗头状态
    }
    
    @PostMapping("/treatment-heads/light-control")
    public ResponseEntity<ApiResponse<Void>> controlLights(@RequestBody LightControlRequest request) {
        // 控制治疗头指示灯
    }
    
    @PostMapping("/treatment-heads/send-parameters")
    public ResponseEntity<ApiResponse<Void>> sendParameters(@RequestBody SendParametersRequest request) {
        // 发送治疗参数到治疗头
    }
    
    @PostMapping("/treatment-heads/start-treatment")
    public ResponseEntity<ApiResponse<Void>> startHardwareTreatment(@RequestBody StartHardwareTreatmentRequest request) {
        // 启动治疗头工作
    }
    
    @PostMapping("/treatment-heads/stop-treatment")
    public ResponseEntity<ApiResponse<Void>> stopHardwareTreatment(@RequestBody StopHardwareTreatmentRequest request) {
        // 停止治疗头工作
    }
}
```

#### 9. 系统设置模块
```java
@RestController
@RequestMapping("/api/settings")
public class SettingsController {
    
    @PostMapping("/parameters")
    public ResponseEntity<ApiResponse<SettingsResponse>> saveParameters(@RequestBody SettingsRequest request) {
        // 保存系统设置参数
    }
    
    @GetMapping("/parameters")
    public ResponseEntity<ApiResponse<SettingsResponse>> getParameters() {
        // 获取系统设置参数
    }
}
```

### 前端页面组件设计

#### 1. 页面路由配置
```javascript
const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', component: LoginPage, name: 'Login' },
  { path: '/main', component: MainPage, name: 'Main' },
  { path: '/patient/new', component: NewPatientPage, name: 'NewPatient' },
  { path: '/patient/create', component: CreatePatientPage, name: 'CreatePatient' },
  { path: '/patient/settings', component: PatientSettingsPage, name: 'PatientSettings' },
  { path: '/patient/archive', component: PatientArchivePage, name: 'PatientArchive' },
  { path: '/patient/profile/:id', component: PatientProfilePage, name: 'PatientProfile' },
  { path: '/treatment/patch-guide', component: PatchGuidePage, name: 'PatchGuide' },
  { path: '/treatment/process', component: TreatmentProcessPage, name: 'TreatmentProcess' },
  { path: '/process/management', component: ProcessManagementPage, name: 'ProcessManagement' },
  { path: '/treatment-head/management', component: TreatmentHeadManagementPage, name: 'TreatmentHeadManagement' },
  { path: '/settings', component: SystemSettingsPage, name: 'SystemSettings' }
]
```

#### 2. 页面数据结构设计

**登录页面数据**
```javascript
const loginPageData = {
  username: '',
  password: '',
  isFactoryPassword: false,
  loading: false,
  errorMessage: ''
}
```

**主界面数据**
```javascript
const mainPageData = {
  systemInfo: {
    currentTime: '',
    systemStatus: 'NORMAL'
  },
  quickActions: []
}
```

**新建档案页面数据**
```javascript
const newPatientPageData = {
  recentPatients: [],
  pagination: {
    currentPage: 1,
    totalPages: 1
  },
  loading: false
}
```

**患者参数设置页面数据**
```javascript
const patientSettingsData = {
  bodyParts: [],
  selectedBodyParts: [],
  treatmentParams: {
    duration: 20,
    intensity: 500.00,
    frequency: 1000,
    patchType: 'DEEP',
    patchQuantity: 1
  },
  presetConfigurations: []
}
```

**治疗进程页面数据**
```javascript
const treatmentProcessData = {
  processId: null,
  status: 'IN_PROGRESS',
  treatmentSessions: [],
  overallProgress: 0,
  canTerminate: true,
  canAddSession: true
}
```

## 数据模型设计

### 实体类设计

#### 1. 用户实体
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    private Integer id;
    
    @Column(unique = true, nullable = false)
    private String username;
    
    @Column(name = "factory_password_hash", nullable = false)
    private String factoryPasswordHash;
    
    @Column(name = "user_password_hash", nullable = false)
    private String userPasswordHash;
    
    @Column(name = "last_updated_at")
    private LocalDateTime lastUpdatedAt;
    
    // getters and setters
}
```

#### 2. 患者实体
```java
@Entity
@Table(name = "patients")
public class Patient {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "patient_card_id", unique = true, nullable = false)
    private String patientCardId;
    
    @Column(nullable = false)
    private String name;
    
    private String gender;
    private String age;
    
    @Column(name = "contact_info")
    private String contactInfo;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @OneToMany(mappedBy = "patient", cascade = CascadeType.ALL)
    private List<Record> records;
    
    // getters and setters
}
```

#### 3. 档案实体
```java
@Entity
@Table(name = "records")
public class Record {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "record_number", unique = true, nullable = false)
    private String recordNumber;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id", nullable = false)
    private Patient patient;
    
    @Column(name = "diagnosis_description")
    private String diagnosisDescription;
    
    @Column(name = "sessions_completed_count")
    private Integer sessionsCompletedCount = 0;
    
    @Column(name = "created_at")
    private LocalDate createdAt;
    
    @OneToMany(mappedBy = "record", cascade = CascadeType.ALL)
    private List<Process> processes;
    
    @OneToMany(mappedBy = "record", cascade = CascadeType.ALL)
    private List<BodyPartStat> bodyPartStats;
    
    // getters and setters
}
```

#### 4. 治疗进程实体
```java
@Entity
@Table(name = "processes")
public class Process {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "record_id", nullable = false)
    private Record record;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "treatment_mode", nullable = false)
    private TreatmentMode treatmentMode; // ON_SITE, TAKE_AWAY
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ProcessStatus status; // IN_PROGRESS, COMPLETED, CANCELLED
    
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    @OneToMany(mappedBy = "process", cascade = CascadeType.ALL)
    private List<TreatmentDetail> treatmentDetails;
    
    // getters and setters
}
```

#### 5. 治疗详情实体
```java
@Entity
@Table(name = "treatment_details")
public class TreatmentDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "process_id", nullable = false)
    private Process process;
    
    @Column(name = "head_serial_number_used", nullable = false)
    private String headSerialNumberUsed;
    
    @Column(name = "body_part", nullable = false)
    private String bodyPart;
    
    @Column(nullable = false)
    private Integer duration; // 分钟
    
    @Column(nullable = false, precision = 5, scale = 2)
    private BigDecimal intensity; // mW/C
    
    @Column(nullable = false)
    private Integer frequency; // Hz
    
    @Enumerated(EnumType.STRING)
    @Column(name = "patch_type", nullable = false)
    private PatchType patchType; // DEEP, SHALLOW
    
    @Column(name = "patch_quantity", nullable = false)
    private Integer patchQuantity; // 1-4
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TreatmentDetailStatus status; // TREATING, COMPLETED, AWAITING_RETURN, RETURNED, TERMINATED
    
    // getters and setters
}
```

#### 6. 治疗头实体
```java
@Entity
@Table(name = "treatment_heads")
public class TreatmentHead {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "head_id")
    private Long headId;
    
    @Column(name = "slot_number", unique = true)
    private Integer slotNumber;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "realtime_status")
    private TreatmentHeadStatus realtimeStatus; // IDLE, TREATING, CHARGING, FULL
    
    @Column(name = "battery_level")
    private Integer batteryLevel; // 0-100
    
    @Column(name = "total_usage_count")
    private Integer totalUsageCount = 0;
    
    @Column(name = "total_usage_minutes")
    private Integer totalUsageMinutes = 0;
    
    @Column(name = "max_usage_count")
    private Integer maxUsageCount = 500;
    
    // getters and setters
}
```

### DTO设计

#### 1. 响应DTO
```java
// 统一响应格式
public class ApiResponse<T> {
    private Integer code;
    private String message;
    private T data;
    private LocalDateTime timestamp;
    
    // constructors, getters and setters
}

// 登录响应
public class LoginResponse {
    private String token;
    private UserInfo userInfo;
    
    // getters and setters
}

// 最近患者响应
public class RecentPatientsResponse {
    private List<RecentPatientInfo> recentPatients;
    private PaginationInfo pagination;
    
    // getters and setters
}

// 治疗头可用性响应
public class AvailabilityResponse {
    private Integer availableHeads;
    private Integer requiredHeads;
    private Boolean isInsufficient;
    private List<TreatmentHeadInfo> availableHeadsList;
    private String message;
    
    // getters and setters
}

// 治疗状态响应
public class TreatmentStatusResponse {
    private Long processId;
    private String status;
    private List<TreatmentSessionInfo> treatmentSessions;
    private Integer overallProgress;
    private Boolean canTerminate;
    private Boolean canAddSession;
    
    // getters and setters
}
```

#### 2. 请求DTO
```java
// 登录请求
public class LoginRequest {
    private String username;
    private String password;
    private Boolean isFactoryPassword;
    
    // getters and setters
}

// 治疗设置请求
public class TreatmentSettingsRequest {
    private List<String> selectedBodyParts;
    private Integer duration;
    private BigDecimal intensity;
    private Integer frequency;
    private String patchType;
    private Integer patchQuantity;
    
    // getters and setters
}

// 开始治疗请求
public class StartTreatmentRequest {
    private Long recordId;
    private String treatmentMode;
    private List<TreatmentDetailRequest> treatmentDetails;
    
    // getters and setters
}
```

## 串口通信设计

### 串口通信服务
```java
@Service
@Slf4j
public class SerialCommunicationService {
    
    private SerialPort serialPort;
    private static final int BAUD_RATE = 115200;
    private static final int DATA_BITS = 8;
    private static final int STOP_BITS = SerialPort.ONE_STOP_BIT;
    private static final int PARITY = SerialPort.NO_PARITY;
    
    @Autowired
    private HardwareCommandParser commandParser;
    
    @PostConstruct
    public void initializeSerialPort() {
        try {
            SerialPort[] ports = SerialPort.getCommPorts();
            if (ports.length > 0) {
                serialPort = ports[0]; // 选择第一个可用端口
                serialPort.setBaudRate(BAUD_RATE);
                serialPort.setNumDataBits(DATA_BITS);
                serialPort.setNumStopBits(STOP_BITS);
                serialPort.setParity(PARITY);
                
                if (serialPort.openPort()) {
                    log.info("串口连接成功: {}", serialPort.getSystemPortName());
                } else {
                    log.error("串口连接失败");
                    throw new SerialCommunicationException("串口连接失败");
                }
            }
        } catch (Exception e) {
            log.error("初始化串口失败", e);
            throw new SerialCommunicationException("初始化串口失败", e);
        }
    }
    
    /**
     * 查询治疗头信息
     * 指令: TRZI\r\n
     * 响应: TRZI+治疗头数量(2 char)+((治疗头编号(2char)+治疗头电量(2char)+治疗次数(3char)+槽编号（2char))*治疗头数量\r\n
     */
    public List<TreatmentHeadInfo> queryTreatmentHeads() {
        String command = "TRZI\r\n";
        String response = sendCommand(command);
        return commandParser.parseTreatmentHeadQuery(response);
    }
    
    /**
     * 点亮治疗头指示灯
     * 指令: TWSC+治疗头数量(2 char)+(治疗头编号+颜色 3char)*治疗头数量\r\n
     * 响应: TWSC+治疗头数量(2 char)+(治疗头编号+颜色+槽编号)(5char)*治疗头数量\r\n
     */
    public boolean lightUpTreatmentHeads(List<TreatmentHeadLight> lights) {
        StringBuilder command = new StringBuilder("TWSC");
        command.append(String.format("%02d", lights.size()));
        
        for (TreatmentHeadLight light : lights) {
            command.append(String.format("%02d", light.getHeadId()));
            command.append(String.format("%03d", light.getColorCode()));
        }
        command.append("\r\n");
        
        String response = sendCommand(command.toString());
        return commandParser.validateLightUpResponse(response, lights);
    }
    
    /**
     * 关闭治疗头指示灯
     * 指令: TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n
     * 响应: TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n
     */
    public boolean lightOffTreatmentHeads(List<Integer> headIds) {
        StringBuilder command = new StringBuilder("TWSN");
        command.append(String.format("%02d", headIds.size()));
        
        for (Integer headId : headIds) {
            command.append(String.format("%02d", headId));
        }
        command.append("\r\n");
        
        String response = sendCommand(command.toString());
        return commandParser.validateLightOffResponse(response, headIds);
    }
    
    /**
     * 发送治疗参数到治疗头
     * 指令: TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n
     * 响应: TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n
     */
    public boolean downloadParameters(TreatmentParameters params, List<Integer> headIds) {
        StringBuilder command = new StringBuilder("TWSDT");
        command.append(String.format("%02d", params.getDuration()));
        command.append(String.format("%03d", params.getIntensity().intValue()));
        command.append("F");
        command.append(params.getFrequency() == 1000 ? "1" : "0");
        command.append("ID");
        command.append(String.format("%02d", headIds.size()));
        
        for (Integer headId : headIds) {
            command.append(String.format("%02d", headId));
        }
        command.append("\r\n");
        
        String response = sendCommand(command.toString());
        return commandParser.validateParameterDownloadResponse(response, params, headIds);
    }
    
    /**
     * 启动治疗头工作
     * 指令: TWS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)\r\n
     * 响应: TWS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)\r\n
     */
    public boolean startTreatment(Integer headId, TreatmentParameters params) {
        StringBuilder command = new StringBuilder("TWS");
        command.append(String.format("%02d", headId));
        command.append(String.format("%02d", params.getDuration()));
        command.append(String.format("%03d", params.getIntensity().intValue()));
        command.append("F");
        command.append(params.getFrequency() == 1000 ? "1" : "0");
        command.append("\r\n");
        
        String response = sendCommand(command.toString());
        return commandParser.validateStartTreatmentResponse(response, headId, params);
    }
    
    /**
     * 停止治疗头工作
     * 指令: TWZO+治疗头编号(2char)+\r\n
     * 响应: TWZO+治疗头编号(2char)+\r\n
     */
    public boolean stopTreatment(Integer headId) {
        String command = String.format("TWZO%02d\r\n", headId);
        String response = sendCommand(command);
        return commandParser.validateStopTreatmentResponse(response, headId);
    }
    
    private String sendCommand(String command) {
        try {
            if (serialPort == null || !serialPort.isOpen()) {
                throw new SerialCommunicationException("串口未连接");
            }
            
            // 发送指令
            byte[] commandBytes = command.getBytes(StandardCharsets.UTF_8);
            serialPort.writeBytes(commandBytes, commandBytes.length);
            
            // 等待响应
            Thread.sleep(100); // 等待100ms
            
            // 读取响应
            byte[] responseBuffer = new byte[1024];
            int bytesRead = serialPort.readBytes(responseBuffer, responseBuffer.length);
            
            if (bytesRead > 0) {
                String response = new String(responseBuffer, 0, bytesRead, StandardCharsets.UTF_8);
                log.debug("发送指令: {}, 接收响应: {}", command.trim(), response.trim());
                return response;
            } else {
                throw new SerialCommunicationException("未收到硬件响应");
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new SerialCommunicationException("串口通信被中断", e);
        } catch (Exception e) {
            log.error("串口通信异常", e);
            throw new SerialCommunicationException("串口通信异常", e);
        }
    }
    
    @PreDestroy
    public void closeSerialPort() {
        if (serialPort != null && serialPort.isOpen()) {
            serialPort.closePort();
            log.info("串口连接已关闭");
        }
    }
}
```

### 硬件指令解析器
```java
@Component
@Slf4j
public class HardwareCommandParser {
    
    /**
     * 解析治疗头查询响应
     * 格式: TRZI+治疗头数量(2 char)+((治疗头编号(2char)+治疗头电量(2char)+治疗次数(3char)+槽编号（2char))*治疗头数量
     */
    public List<TreatmentHeadInfo> parseTreatmentHeadQuery(String response) {
        List<TreatmentHeadInfo> heads = new ArrayList<>();
        
        if (response == null || !response.startsWith("TRZI")) {
            log.error("无效的治疗头查询响应: {}", response);
            return heads;
        }
        
        try {
            String data = response.substring(4).replace("\r\n", "");
            int count = Integer.parseInt(data.substring(0, 2));
            
            for (int i = 0; i < count; i++) {
                int offset = 2 + i * 9;
                if (offset + 9 <= data.length()) {
                    int headId = Integer.parseInt(data.substring(offset, offset + 2));
                    int battery = Integer.parseInt(data.substring(offset + 2, offset + 4));
                    int usageCount = Integer.parseInt(data.substring(offset + 4, offset + 7));
                    int slotNumber = Integer.parseInt(data.substring(offset + 7, offset + 9));
                    
                    heads.add(new TreatmentHeadInfo(headId, battery, usageCount, slotNumber));
                }
            }
        } catch (Exception e) {
            log.error("解析治疗头查询响应失败", e);
        }
        
        return heads;
    }
    
    /**
     * 验证点亮指示灯响应
     */
    public boolean validateLightUpResponse(String response, List<TreatmentHeadLight> lights) {
        if (response == null || !response.startsWith("TWSC")) {
            return false;
        }
        
        try {
            String data = response.substring(4).replace("\r\n", "");
            int count = Integer.parseInt(data.substring(0, 2));
            return count == lights.size();
        } catch (Exception e) {
            log.error("验证点亮指示灯响应失败", e);
            return false;
        }
    }
    
    /**
     * 验证关闭指示灯响应
     */
    public boolean validateLightOffResponse(String response, List<Integer> headIds) {
        if (response == null || !response.startsWith("TWSN")) {
            return false;
        }
        
        try {
            String data = response.substring(4).replace("\r\n", "");
            int count = Integer.parseInt(data.substring(0, 2));
            return count == headIds.size();
        } catch (Exception e) {
            log.error("验证关闭指示灯响应失败", e);
            return false;
        }
    }
    
    /**
     * 验证参数下载响应
     */
    public boolean validateParameterDownloadResponse(String response, TreatmentParameters params, List<Integer> headIds) {
        if (response == null || !response.startsWith("TWSDT")) {
            return false;
        }
        
        try {
            String data = response.substring(5).replace("\r\n", "");
            // 验证时间、声强、频率等参数是否匹配
            int duration = Integer.parseInt(data.substring(0, 2));
            int intensity = Integer.parseInt(data.substring(2, 5));
            String frequencyFlag = data.substring(6, 7);
            int count = Integer.parseInt(data.substring(9, 11));
            
            return duration == params.getDuration() &&
                   intensity == params.getIntensity().intValue() &&
                   frequencyFlag.equals(params.getFrequency() == 1000 ? "1" : "0") &&
                   count == headIds.size();
        } catch (Exception e) {
            log.error("验证参数下载响应失败", e);
            return false;
        }
    }
    
    /**
     * 验证启动治疗响应
     */
    public boolean validateStartTreatmentResponse(String response, Integer headId, TreatmentParameters params) {
        if (response == null || !response.startsWith("TWS")) {
            return false;
        }
        
        try {
            String data = response.substring(3).replace("\r\n", "");
            int responseHeadId = Integer.parseInt(data.substring(0, 2));
            int duration = Integer.parseInt(data.substring(2, 4));
            int intensity = Integer.parseInt(data.substring(4, 7));
            String frequencyFlag = data.substring(8, 9);
            
            return responseHeadId.equals(headId) &&
                   duration == params.getDuration() &&
                   intensity == params.getIntensity().intValue() &&
                   frequencyFlag.equals(params.getFrequency() == 1000 ? "1" : "0");
        } catch (Exception e) {
            log.error("验证启动治疗响应失败", e);
            return false;
        }
    }
    
    /**
     * 验证停止治疗响应
     */
    public boolean validateStopTreatmentResponse(String response, Integer headId) {
        if (response == null || !response.startsWith("TWZO")) {
            return false;
        }
        
        try {
            String data = response.substring(4).replace("\r\n", "");
            int responseHeadId = Integer.parseInt(data.substring(0, 2));
            return responseHeadId.equals(headId);
        } catch (Exception e) {
            log.error("验证停止治疗响应失败", e);
            return false;
        }
    }
}
```

## 错误处理设计

### 全局异常处理
```java
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(SerialCommunicationException.class)
    public ResponseEntity<ApiResponse<Void>> handleSerialCommunicationException(SerialCommunicationException e) {
        log.error("串口通信异常", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error(1001, "硬件通信异常：" + e.getMessage()));
    }
    
    @ExceptionHandler(TreatmentHeadNotFoundException.class)
    public ResponseEntity<ApiResponse<Void>> handleTreatmentHeadNotFoundException(TreatmentHeadNotFoundException e) {
        log.error("治疗头未找到", e);
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.error(1002, "治疗头不可用：" + e.getMessage()));
    }
    
    @ExceptionHandler(InvalidTreatmentParametersException.class)
    public ResponseEntity<ApiResponse<Void>> handleInvalidTreatmentParametersException(InvalidTreatmentParametersException e) {
        log.error("治疗参数无效", e);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.error(400, "治疗参数无效：" + e.getMessage()));
    }
    
    @ExceptionHandler(PatientCardIdDuplicateException.class)
    public ResponseEntity<ApiResponse<Void>> handlePatientCardIdDuplicateException(PatientCardIdDuplicateException e) {
        log.error("患者卡号重复", e);
        return ResponseEntity.status(HttpStatus.CONFLICT)
            .body(ApiResponse.error(1003, "患者信息重复：" + e.getMessage()));
    }
    
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ApiResponse<Void>> handleAuthenticationException(AuthenticationException e) {
        log.error("认证失败", e);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(ApiResponse.error(401, "认证失败：" + e.getMessage()));
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleGenericException(Exception e) {
        log.error("系统异常", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error(500, "系统内部错误"));
    }
}
```

### 前端错误处理
```javascript
// API请求拦截器
axios.interceptors.response.use(
  response => {
    const { code, message } = response.data;
    if (code !== 200) {
      handleApiError(code, message);
      return Promise.reject(new Error(message));
    }
    return response;
  },
  error => {
    const { status, data } = error.response || {};
    
    switch (status) {
      case 401:
        // 未授权，跳转到登录页
        localStorage.removeItem('token');
        router.push('/login');
        break;
      case 1001:
        // 硬件通信异常
        showErrorMessage('硬件通信异常，请检查设备连接');
        break;
      case 1002:
        // 治疗头不可用
        showErrorMessage('治疗头设备不可用，请检查设备状态');
        break;
      case 1003:
        // 患者信息重复
        showErrorMessage('患者信息重复，请检查就诊卡号');
        break;
      default:
        showErrorMessage(data?.message || '系统异常，请稍后重试');
    }
    
    return Promise.reject(error);
  }
);

function handleApiError(code, message) {
  switch (code) {
    case 400:
      showErrorMessage('请求参数错误：' + message);
      break;
    case 404:
      showErrorMessage('资源不存在：' + message);
      break;
    default:
      showErrorMessage(message || '操作失败');
  }
}
```

## 测试策略

### 单元测试
- Service层业务逻辑测试
- 串口通信服务测试（使用Mock SerialPort）
- 硬件指令解析器测试
- 数据访问层测试

### 集成测试
- API接口测试
- 数据库集成测试
- 串口硬件集成测试（使用真实硬件或模拟器）

### 前端测试
- Vue组件单元测试
- 页面交互测试
- API调用测试
- 路由导航测试

### 硬件测试
- 串口通信协议测试
- 治疗头指令响应测试
- 异常情况处理测试
- 并发指令处理测试