package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.sync.SyncResult;
import com.Bone.BoneSys.dto.sync.SyncStatus;
import com.Bone.BoneSys.service.TreatmentHeadSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 治疗头同步管理API控制器
 * 提供同步状态查询、手动触发同步等功能
 */
@RestController
@RequestMapping("/api/hardware/treatment-heads/sync")
@CrossOrigin(origins = "*")
public class TreatmentHeadSyncController {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadSyncController.class);
    
    @Autowired
    private TreatmentHeadSyncService syncService;
    
    /**
     * 获取同步状态
     * GET /api/hardware/treatment-heads/sync/status
     */
    @GetMapping("/status")
    public ApiResponse<SyncStatus> getSyncStatus() {
        try {
            SyncStatus status = syncService.getSyncStatus();
            return ApiResponse.success("Sync status retrieved successfully", status);
            
        } catch (Exception e) {
            logger.error("Failed to get sync status", e);
            return ApiResponse.error(500, "Failed to retrieve sync status: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发同步
     * POST /api/hardware/treatment-heads/sync/trigger
     */
    @PostMapping("/trigger")
    public ApiResponse<SyncResult> triggerManualSync() {
        try {
            logger.info("Manual sync triggered via API");
            SyncResult result = syncService.triggerManualSync();
            
            if (result.isSuccess()) {
                return ApiResponse.success("Manual sync completed successfully", result);
            } else {
                return ApiResponse.error(500, "Manual sync failed: " + result.getErrorMessage(), result);
            }
            
        } catch (Exception e) {
            logger.error("Failed to trigger manual sync", e);
            return ApiResponse.error(500, "Failed to trigger manual sync: " + e.getMessage());
        }
    }
    
    /**
     * 获取同步配置
     * GET /api/hardware/treatment-heads/sync/config
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getSyncConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("enabled", syncService.isSyncEnabled());
            config.put("interval", syncService.getSyncInterval());
            config.put("startupDelay", syncService.getStartupDelay());
            
            return ApiResponse.success("Sync configuration retrieved successfully", config);
            
        } catch (Exception e) {
            logger.error("Failed to get sync configuration", e);
            return ApiResponse.error(500, "Failed to retrieve sync configuration: " + e.getMessage());
        }
    }
    
    /**
     * 获取同步统计信息
     * GET /api/hardware/treatment-heads/sync/stats
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getSyncStats() {
        try {
            SyncStatus status = syncService.getSyncStatus();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalSyncCount", status.getTotalSyncCount());
            stats.put("successfulSyncCount", status.getSuccessfulSyncCount());
            stats.put("failedSyncCount", status.getFailedSyncCount());
            stats.put("successRate", status.getSuccessRate());
            stats.put("failureRate", status.getFailureRate());
            stats.put("averageExecutionTime", status.getAverageExecutionTime());
            stats.put("isHealthy", status.isHealthy());
            stats.put("statusDescription", status.getStatusDescription());
            stats.put("performanceSummary", status.getPerformanceSummary());
            
            return ApiResponse.success("Sync statistics retrieved successfully", stats);
            
        } catch (Exception e) {
            logger.error("Failed to get sync statistics", e);
            return ApiResponse.error(500, "Failed to retrieve sync statistics: " + e.getMessage());
        }
    }
    
    /**
     * 获取同步健康状态
     * GET /api/hardware/treatment-heads/sync/health
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> getSyncHealth() {
        try {
            SyncStatus status = syncService.getSyncStatus();
            
            Map<String, Object> health = new HashMap<>();
            health.put("healthy", status.isHealthy());
            health.put("status", status.getCurrentStatus());
            health.put("lastSyncTime", status.getLastSyncTime());
            health.put("lastSyncSuccess", status.isLastSyncSuccess());
            
            if (!status.isLastSyncSuccess() && status.getLastErrorMessage() != null) {
                health.put("lastError", status.getLastErrorMessage());
            }
            
            // 根据健康状态返回不同的HTTP状态码
            if (status.isHealthy()) {
                return ApiResponse.success("Sync service is healthy", health);
            } else {
                return ApiResponse.error(503, "Sync service is unhealthy", health);
            }
            
        } catch (Exception e) {
            logger.error("Failed to get sync health status", e);
            return ApiResponse.error(500, "Failed to retrieve sync health status: " + e.getMessage());
        }
    }
}