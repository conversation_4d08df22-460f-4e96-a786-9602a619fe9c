package com.Bone.BoneSys.service;

import com.Bone.BoneSys.entity.TreatmentDetail;
import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.entity.BodyPartStat;
import com.Bone.BoneSys.entity.Record;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.repository.TreatmentDetailRepository;
import com.Bone.BoneSys.repository.ProcessRepository;
import com.Bone.BoneSys.repository.BodyPartStatRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 治疗详情自动状态更新服务
 * 负责根据治疗时间自动更新治疗详情状态
 */
@Service
public class TreatmentDetailAutoUpdateService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentDetailAutoUpdateService.class);
    
    // 远端治疗提醒时间（分钟）
    private static final int REMOTE_REMINDER_MINUTES = 15;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;

    @Autowired
    private RecordRepository recordRepository;
    
    /**
     * 定时检查并自动更新治疗详情状态
     * 每30秒执行一次
     */
    @Scheduled(fixedRate = 30000) // 30秒执行一次
    @Transactional
    public void autoUpdateTreatmentDetailStatus() {
        try {
            logger.debug("开始检查治疗详情状态自动更新...");
            
            // 获取所有正在治疗中的治疗详情
            List<TreatmentDetail> treatingDetails = treatmentDetailRepository
                .findByStatus(TreatmentDetailStatus.TREATING);
            
            if (treatingDetails.isEmpty()) {
                logger.debug("没有正在治疗中的详情，跳过状态更新");
                return;
            }
            
            int updatedCount = 0;
            LocalDateTime now = LocalDateTime.now();
            
            for (TreatmentDetail detail : treatingDetails) {
                try {
                    Process process = detail.getProcess();
                    if (process == null || process.getStartTime() == null) {
                        logger.warn("治疗详情 {} 的进程信息不完整，跳过", detail.getId());
                        continue;
                    }
                    
                    // 计算已经过的时间（分钟）
                    long elapsedMinutes = ChronoUnit.MINUTES.between(process.getStartTime(), now);
                    int treatmentDurationMinutes = detail.getDuration();
                    
                    boolean shouldUpdate = false;
                    TreatmentDetailStatus newStatus = null;
                    
                    if (process.getTreatmentMode() == TreatmentMode.ON_SITE) {
                        // 本地治疗：治疗时间结束后变成已完成状态
                        if (elapsedMinutes >= treatmentDurationMinutes) {
                            shouldUpdate = true;
                            newStatus = TreatmentDetailStatus.COMPLETED;
                            logger.info("本地治疗详情 {} 时间到期（{}分钟），自动更新为完成状态", 
                                       detail.getId(), elapsedMinutes);
                        }
                    } else if (process.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
                        // 远端治疗：治疗时间+提醒时间后变成待取回状态
                        long totalRequiredMinutes = treatmentDurationMinutes + REMOTE_REMINDER_MINUTES;
                        if (elapsedMinutes >= totalRequiredMinutes) {
                            shouldUpdate = true;
                            newStatus = TreatmentDetailStatus.AWAITING_RETURN;
                            logger.info("远端治疗详情 {} 时间到期（{}分钟，包含{}分钟提醒时间），自动更新为待取回状态", 
                                       detail.getId(), elapsedMinutes, REMOTE_REMINDER_MINUTES);
                        }
                    }
                    
                    if (shouldUpdate && newStatus != null) {
                        // 更新状态
                        detail.setStatus(newStatus);
                        treatmentDetailRepository.save(detail);
                        updatedCount++;
                        
                        // 如果是完成状态，需要更新统计数据
                        if (newStatus == TreatmentDetailStatus.COMPLETED) {
                            updateBodyPartStats(detail);
                            checkAndCompleteProcess(process.getId());
                        } else if (newStatus == TreatmentDetailStatus.AWAITING_RETURN) {
                            // 远端治疗进入待取回状态时，更新部位统计
                            updateBodyPartStats(detail);
                            checkAndUpdateProcessForAwaitingReturn(process.getId());
                        }
                        
                        logger.info("治疗详情 {} 状态已自动更新为 {}", detail.getId(), newStatus);
                    }
                    
                } catch (Exception e) {
                    logger.error("更新治疗详情 {} 状态失败", detail.getId(), e);
                }
            }
            
            if (updatedCount > 0) {
                logger.info("治疗详情状态自动更新完成，共更新 {} 个详情", updatedCount);
            } else {
                logger.debug("没有需要更新状态的治疗详情");
            }
            
        } catch (Exception e) {
            logger.error("治疗详情状态自动更新失败", e);
        }
    }
    
    /**
     * 手动触发状态更新检查
     */
    public void triggerManualUpdate() {
        logger.info("手动触发治疗详情状态更新检查");
        autoUpdateTreatmentDetailStatus();
    }

    /**
     * 更新身体部位统计数据
     */
    private void updateBodyPartStats(TreatmentDetail detail) {
        try {
            Record record = detail.getProcess().getRecord();
            String bodyPart = detail.getBodyPart();

            // 查找或创建身体部位统计记录
            BodyPartStat stat = bodyPartStatRepository
                .findByRecordIdAndBodyPart(record.getId(), bodyPart)
                .orElse(new BodyPartStat());

            if (stat.getId() == null) {
                // 新建统计记录
                stat.setRecord(record);
                stat.setBodyPart(bodyPart);
                stat.setTotalUsageCount(1);
                stat.setTotalDurationMinutes(detail.getDuration());
            } else {
                // 更新现有统计记录
                stat.setTotalUsageCount(stat.getTotalUsageCount() + 1);
                stat.setTotalDurationMinutes(stat.getTotalDurationMinutes() + detail.getDuration());
            }

            bodyPartStatRepository.save(stat);
            logger.debug("已更新身体部位统计：档案ID={}, 部位={}, 次数={}, 时长={}分钟",
                        record.getId(), bodyPart, stat.getTotalUsageCount(), stat.getTotalDurationMinutes());

        } catch (Exception e) {
            logger.error("更新身体部位统计失败：治疗详情ID={}", detail.getId(), e);
        }
    }

    /**
     * 检查并完成进程
     */
    private void checkAndCompleteProcess(Long processId) {
        try {
            List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(processId);
            Process process = processRepository.findById(processId).orElse(null);

            if (process == null || process.getStatus() != ProcessStatus.IN_PROGRESS) {
                return;
            }

            // 统计各种状态的治疗详情数量
            long completedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.COMPLETED).count();
            long returnedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.RETURNED).count();
            long terminatedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.TERMINATED).count();
            long totalCount = details.size();

            if (process.getTreatmentMode() == TreatmentMode.ON_SITE) {
                // 本地治疗逻辑
                if (terminatedCount == totalCount) {
                    process.setStatus(ProcessStatus.CANCELLED);
                    process.setEndTime(LocalDateTime.now());
                } else if ((completedCount + returnedCount + terminatedCount) == totalCount && (completedCount > 0 || returnedCount > 0)) {
                    process.setStatus(ProcessStatus.COMPLETED);
                    process.setEndTime(LocalDateTime.now());
                    updateRecordCompletionCount(process.getRecord());
                }

                if (process.getStatus() != ProcessStatus.IN_PROGRESS) {
                    processRepository.save(process);
                    logger.info("本地治疗进程 {} 状态已更新为 {}", processId, process.getStatus());
                }
            }

        } catch (Exception e) {
            logger.error("检查并完成进程失败：进程ID={}", processId, e);
        }
    }

    /**
     * 检查并更新进程状态（远端治疗）
     */
    private void checkAndUpdateProcessForAwaitingReturn(Long processId) {
        try {
            List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(processId);
            Process process = processRepository.findById(processId).orElse(null);

            if (process == null || process.getStatus() != ProcessStatus.IN_PROGRESS) {
                return;
            }

            // 统计各种状态的治疗详情数量
            long awaitingReturnCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.AWAITING_RETURN).count();
            long returnedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.RETURNED).count();
            long terminatedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.TERMINATED).count();
            long totalCount = details.size();

            if (process.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
                // 远端治疗逻辑

                // 检查是否全部详情都进入最终状态（AWAITING_RETURN 或 TERMINATED），且尚未计数
                long finalStateCount = awaitingReturnCount + terminatedCount;
                if (finalStateCount == totalCount && !process.getRemoteCounted()) {
                    // 远端治疗临床完成：全部详情都不再治疗，更新档案完成次数
                    updateRecordCompletionCount(process.getRecord());
                    process.setRemoteCounted(true);
                    processRepository.save(process);
                    logger.info("远端治疗进程 {} 临床完成，全部详情已进入最终状态（待取回/终止），档案完成次数已更新", processId);
                }

                if (terminatedCount == totalCount) {
                    process.setStatus(ProcessStatus.CANCELLED);
                    process.setEndTime(LocalDateTime.now());
                } else if (returnedCount == totalCount) {
                    process.setStatus(ProcessStatus.COMPLETED);
                    process.setEndTime(LocalDateTime.now());
                    // 注意：这里不再重复更新档案完成次数，因为在临床完成时已经更新过
                }

                if (process.getStatus() != ProcessStatus.IN_PROGRESS) {
                    processRepository.save(process);
                    logger.info("远端治疗进程 {} 状态已更新为 {}", processId, process.getStatus());
                }
            }

        } catch (Exception e) {
            logger.error("检查并更新远端治疗进程状态失败：进程ID={}", processId, e);
        }
    }

    /**
     * 更新档案完成次数
     */
    private void updateRecordCompletionCount(Record record) {
        try {
            record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);
            recordRepository.save(record);
            logger.debug("已更新档案完成次数：档案ID={}, 完成次数={}",
                        record.getId(), record.getSessionsCompletedCount());
        } catch (Exception e) {
            logger.error("更新档案完成次数失败：档案ID={}", record.getId(), e);
        }
    }
}
