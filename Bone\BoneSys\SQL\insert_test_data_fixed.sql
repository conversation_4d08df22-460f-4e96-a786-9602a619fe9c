-- ========================================
-- FREEBONE医疗系统 - 修复后的测试数据插入脚本
-- 根据create_database_enhanced.sql的表结构修复
-- ========================================

USE bonesys;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 清理现有测试数据（保留治疗头和系统参数数据）
-- ========================================

DELETE FROM body_part_stats;
DELETE FROM treatment_details;
DELETE FROM processes;
DELETE FROM records;
DELETE FROM patients;

-- 重置自增ID
ALTER TABLE patients AUTO_INCREMENT = 1;
ALTER TABLE records AUTO_INCREMENT = 1;
ALTER TABLE processes AUTO_INCREMENT = 1;
ALTER TABLE treatment_details AUTO_INCREMENT = 1;
ALTER TABLE body_part_stats AUTO_INCREMENT = 1;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 2. 插入患者数据（5个患者）
-- ========================================

INSERT INTO `patients` (`patient_card_id`, `name`, `gender`, `age`, `contact_info`, `created_at`) VALUES
('P001001', '张三', '男', '45', '13800138001', '2024-01-01 08:00:00'),
('P001002', '李四', '女', '38', '13800138002', '2024-01-02 09:00:00'),
('P001003', '王五', '男', '52', '13800138003', '2024-01-03 10:00:00'),
('P001004', '赵六', '女', '29', '13800138004', '2024-01-04 11:00:00'),
('P001005', '钱七', '男', '61', '13800138005', '2024-01-05 12:00:00');

-- ========================================
-- 3. 插入档案数据（根据实际表结构）
-- ========================================

INSERT INTO `records` (`record_number`, `patient_id`, `diagnosis_description`, `sessions_completed_count`, `created_at`) VALUES
-- 张三的档案
('R20240101001', 1, '颈椎病，腰椎间盘突出', 8, '2024-01-15'),
('R20240301001', 1, '肩周炎复发', 12, '2024-03-20'),

-- 李四的档案  
('R20240102001', 2, '腰肌劳损', 6, '2024-01-18'),
('R20240202001', 2, '颈椎病', 10, '2024-02-25'),
('R20240402001', 2, '肩关节炎', 4, '2024-04-10'),

-- 王五的档案
('R20240103001', 3, '腰椎间盘突出症', 15, '2024-01-22'),
('R20240303001', 3, '颈椎病伴神经根型', 8, '2024-03-15'),

-- 赵六的档案
('R20240104001', 4, '肩周炎', 9, '2024-01-25'),
('R20240304001', 4, '腰肌劳损', 6, '2024-03-28'),

-- 钱七的档案
('R20240105001', 5, '退行性关节炎', 12, '2024-01-30'),
('R20240405001', 5, '颈椎病', 7, '2024-04-05');

-- ========================================
-- 4. 插入进程数据（包含treatment_mode字段）
-- ========================================

INSERT INTO `processes` (`record_id`, `treatment_mode`, `status`, `start_time`, `end_time`) VALUES
-- 张三档案1的进程
(1, 'ON_SITE', 'COMPLETED', '2024-01-15 09:00:00', '2024-01-15 09:45:00'),
(1, 'ON_SITE', 'COMPLETED', '2024-01-17 14:30:00', '2024-01-17 15:15:00'),
(1, 'ON_SITE', 'COMPLETED', '2024-01-20 10:00:00', '2024-01-20 10:50:00'),
(1, 'ON_SITE', 'IN_PROGRESS', '2024-01-22 16:00:00', NULL),

-- 张三档案2的进程
(2, 'TAKE_AWAY', 'COMPLETED', '2024-03-20 08:30:00', '2024-03-20 09:20:00'),
(2, 'TAKE_AWAY', 'COMPLETED', '2024-03-25 15:00:00', '2024-03-25 15:45:00'),
(2, 'TAKE_AWAY', 'IN_PROGRESS', '2024-03-28 11:00:00', NULL),

-- 李四档案1的进程
(3, 'ON_SITE', 'COMPLETED', '2024-01-18 13:00:00', '2024-01-18 13:40:00'),
(3, 'ON_SITE', 'COMPLETED', '2024-01-22 09:30:00', '2024-01-22 10:15:00'),
(3, 'ON_SITE', 'COMPLETED', '2024-01-25 14:00:00', '2024-01-25 14:45:00'),

-- 李四档案2的进程
(4, 'TAKE_AWAY', 'COMPLETED', '2024-02-25 10:00:00', '2024-02-25 10:50:00'),
(4, 'TAKE_AWAY', 'COMPLETED', '2024-03-01 16:30:00', '2024-03-01 17:20:00'),

-- 李四档案3的进程
(5, 'ON_SITE', 'COMPLETED', '2024-04-10 11:30:00', '2024-04-10 12:15:00'),
(5, 'ON_SITE', 'IN_PROGRESS', '2024-04-15 14:00:00', NULL),

-- 王五档案1的进程
(6, 'TAKE_AWAY', 'COMPLETED', '2024-01-22 15:00:00', '2024-01-22 16:00:00'),
(6, 'TAKE_AWAY', 'COMPLETED', '2024-01-28 09:00:00', '2024-01-28 10:00:00'),
(6, 'TAKE_AWAY', 'COMPLETED', '2024-02-05 13:30:00', '2024-02-05 14:30:00'),

-- 王五档案2的进程
(7, 'ON_SITE', 'COMPLETED', '2024-03-15 10:30:00', '2024-03-15 11:20:00'),
(7, 'ON_SITE', 'IN_PROGRESS', '2024-03-20 15:00:00', NULL),

-- 赵六档案1的进程
(8, 'ON_SITE', 'COMPLETED', '2024-01-25 08:00:00', '2024-01-25 08:45:00'),
(8, 'ON_SITE', 'COMPLETED', '2024-02-01 14:30:00', '2024-02-01 15:15:00'),

-- 赵六档案2的进程
(9, 'TAKE_AWAY', 'COMPLETED', '2024-03-28 11:00:00', '2024-03-28 11:50:00'),
(9, 'TAKE_AWAY', 'IN_PROGRESS', '2024-04-02 16:30:00', NULL),

-- 钱七档案1的进程
(10, 'TAKE_AWAY', 'COMPLETED', '2024-01-30 09:30:00', '2024-01-30 10:30:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2024-02-10 13:00:00', '2024-02-10 14:00:00'),

-- 钱七档案2的进程
(11, 'ON_SITE', 'COMPLETED', '2024-04-05 15:30:00', '2024-04-05 16:20:00'),
(11, 'ON_SITE', 'IN_PROGRESS', '2024-04-12 10:00:00', NULL);

-- ========================================
-- 5. 插入治疗详情数据（每个进程1-3个治疗详情）
-- ========================================

INSERT INTO `treatment_details` (`process_id`, `head_number_used`, `body_part`, `duration`, `intensity`, `frequency`, `patch_type`, `patch_quantity`, `status`) VALUES
-- 张三档案1进程1（已完成）- 肩颈部+腰背部
(1, 1, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(1, 11, '腰背部', 25, 60.00, 100, 'DEEP', 2, 'COMPLETED'),

-- 张三档案1进程2（已完成）- 上肢
(2, 2, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),

-- 张三档案1进程3（已完成）- 肩颈部+下肢+髋部
(3, 3, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(3, 12, '下肢', 20, 45.00, 100, 'DEEP', 2, 'COMPLETED'),
(3, 13, '髋部', 20, 45.00, 1000, 'DEEP', 1, 'COMPLETED'),

-- 张三档案1进程4（进行中）- 腰背部
(4, 14, '腰背部', 25, 60.00, 100, 'DEEP', 2, 'AWAITING_RETURN'),

-- 张三档案2进程1（已完成）- 肩颈部+上肢
(5, 4, '肩颈部', 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),
(5, 5, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 张三档案2进程2（已完成）- 腰背部+下肢
(6, 15, '腰背部', 30, 60.00, 100, 'DEEP', 3, 'RETURNED'),
(6, 16, '下肢', 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 张三档案2进程3（进行中）- 肩颈部
(7, 6, '肩颈部', 25, 60.00, 1000, 'SHALLOW', 3, 'AWAITING_RETURN'),

-- 李四档案1进程1（已完成）- 肩颈部
(8, 7, '肩颈部', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),

-- 李四档案1进程2（已完成）- 腰背部+髋部
(9, 17, '腰背部', 20, 30.00, 100, 'DEEP', 1, 'COMPLETED'),
(9, 18, '髋部', 20, 45.00, 1000, 'DEEP', 1, 'COMPLETED'),

-- 李四档案1进程3（已完成）- 上肢+下肢
(10, 8, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(10, 19, '下肢', 20, 45.00, 100, 'DEEP', 2, 'COMPLETED'),

-- 李四档案2进程1（已完成）- 肩颈部+腰背部
(11, 9, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'RETURNED'),
(11, 20, '腰背部', 25, 60.00, 100, 'DEEP', 2, 'RETURNED'),

-- 李四档案2进程2（已完成）- 下肢
(12, 1, '下肢', 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 李四档案3进程1（已完成）- 肩颈部+上肢
(13, 10, '肩颈部', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(13, 2, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),

-- 李四档案3进程2（进行中）- 腰背部
(14, 11, '腰背部', 20, 30.00, 100, 'DEEP', 1, 'TREATING'),

-- 王五档案1进程1（已完成）- 肩颈部+腰背部+下肢
(15, 3, '肩颈部', 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),
(15, 12, '腰背部', 30, 60.00, 100, 'DEEP', 3, 'RETURNED'),
(15, 13, '下肢', 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 王五档案1进程2（已完成）- 髋部+上肢
(16, 14, '髋部', 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),
(16, 4, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 王五档案1进程3（已完成）- 肩颈部
(17, 5, '肩颈部', 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),

-- 王五档案2进程1（已完成）- 腰背部+下肢
(18, 15, '腰背部', 20, 45.00, 100, 'DEEP', 2, 'COMPLETED'),
(18, 16, '下肢', 20, 45.00, 100, 'DEEP', 2, 'COMPLETED'),

-- 王五档案2进程2（进行中）- 肩颈部+上肢
(19, 6, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'TREATING'),
(19, 7, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'TREATING'),

-- 赵六档案1进程1（已完成）- 肩颈部+腰背部
(20, 8, '肩颈部', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(20, 17, '腰背部', 20, 30.00, 100, 'DEEP', 1, 'COMPLETED'),

-- 赵六档案1进程2（已完成）- 上肢
(21, 9, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),

-- 赵六档案2进程1（已完成）- 下肢+髋部
(22, 18, '下肢', 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),
(22, 19, '髋部', 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),

-- 赵六档案2进程2（进行中）- 肩颈部
(23, 10, '肩颈部', 15, 30.00, 1000, 'SHALLOW', 1, 'AWAITING_RETURN'),

-- 钱七档案1进程1（已完成）- 肩颈部+腰背部+下肢
(24, 1, '肩颈部', 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),
(24, 20, '腰背部', 30, 60.00, 100, 'DEEP', 3, 'RETURNED'),
(24, 11, '下肢', 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 钱七档案1进程2（已完成）- 髋部
(25, 12, '髋部', 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),

-- 钱七档案2进程1（已完成）- 肩颈部+上肢
(26, 2, '肩颈部', 25, 60.00, 1000, 'SHALLOW', 3, 'COMPLETED'),
(26, 3, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),

-- 钱七档案2进程2（进行中）- 腰背部+下肢
(27, 13, '腰背部', 30, 60.00, 100, 'DEEP', 3, 'TREATING'),
(27, 14, '下肢', 20, 45.00, 100, 'DEEP', 2, 'TREATING');

-- ========================================
-- 6. 插入身体部位统计数据（根据实际表结构）
-- ========================================

INSERT INTO `body_part_stats` (`record_id`, `body_part`, `total_usage_count`, `total_duration_minutes`) VALUES
-- 张三档案1的统计
(1, '肩颈部', 2, 40),
(1, '腰背部', 1, 25),
(1, '上肢', 1, 15),
(1, '下肢', 1, 20),
(1, '髋部', 1, 20),

-- 张三档案2的统计
(2, '肩颈部', 1, 25),
(2, '上肢', 1, 15),
(2, '腰背部', 1, 30),
(2, '下肢', 1, 20),

-- 李四档案1的统计
(3, '肩颈部', 1, 15),
(3, '腰背部', 1, 20),
(3, '髋部', 1, 20),
(3, '上肢', 1, 15),
(3, '下肢', 1, 20),

-- 李四档案2的统计
(4, '肩颈部', 1, 20),
(4, '腰背部', 1, 25),
(4, '下肢', 1, 20),

-- 李四档案3的统计
(5, '肩颈部', 1, 15),
(5, '上肢', 1, 15),

-- 王五档案1的统计
(6, '肩颈部', 2, 50),
(6, '腰背部', 1, 30),
(6, '下肢', 1, 20),
(6, '髋部', 1, 20),
(6, '上肢', 1, 15),

-- 王五档案2的统计
(7, '腰背部', 1, 20),
(7, '下肢', 1, 20),

-- 赵六档案1的统计
(8, '肩颈部', 1, 15),
(8, '腰背部', 1, 20),
(8, '上肢', 1, 15),

-- 赵六档案2的统计
(9, '下肢', 1, 20),
(9, '髋部', 1, 20),

-- 钱七档案1的统计
(10, '肩颈部', 1, 25),
(10, '腰背部', 1, 30),
(10, '下肢', 1, 20),
(10, '髋部', 1, 20),

-- 钱七档案2的统计
(11, '肩颈部', 1, 25),
(11, '上肢', 1, 15);

-- ========================================
-- 7. 更新档案的会话完成计数
-- ========================================

UPDATE records r SET sessions_completed_count = (
    SELECT COUNT(*)
    FROM processes p
    WHERE p.record_id = r.id AND p.status = 'COMPLETED'
);

-- ========================================
-- 8. 更新治疗头使用统计
-- ========================================

UPDATE treatment_heads th SET 
    total_usage_count = (
        SELECT COUNT(*)
        FROM treatment_details td
        WHERE td.head_number_used = th.head_number
        AND td.status IN ('COMPLETED', 'RETURNED')
    ),
    total_usage_minutes = (
        SELECT COALESCE(SUM(td.duration), 0)
        FROM treatment_details td
        WHERE td.head_number_used = th.head_number
        AND td.status IN ('COMPLETED', 'RETURNED')
    );

-- ========================================
-- 验证插入结果
-- ========================================

SELECT '=== 修复后的测试数据插入完成 ===' as message;

SELECT 
    '患者数量' as item,
    COUNT(*) as count
FROM patients
UNION ALL
SELECT 
    '档案数量' as item,
    COUNT(*) as count
FROM records
UNION ALL
SELECT 
    '进程数量' as item,
    COUNT(*) as count
FROM processes
UNION ALL
SELECT 
    '治疗详情数量' as item,
    COUNT(*) as count
FROM treatment_details
UNION ALL
SELECT 
    '身体部位统计数量' as item,
    COUNT(*) as count
FROM body_part_stats;

-- 显示治疗头使用情况
SELECT 
    head_number,
    total_usage_count,
    total_usage_minutes,
    CASE 
        WHEN total_usage_count > 0 THEN ROUND((total_usage_count / max_usage_count) * 100, 2)
        ELSE 0
    END as usage_percentage
FROM treatment_heads
WHERE total_usage_count > 0
ORDER BY head_number;

SELECT '=== 数据修复并插入完成 ===' as message;