import http from '@/utils/axios'

/**
 * 患者管理相关API
 */

export const getPatients = (params: { page?: number; size?: number; search?: string }) => {
  return http.get('/patients', { params })
}

export const createPatient = (patientData: any) => {
  return http.post('/patients', patientData)
}

export const updatePatient = (id: string, patientData: any) => {
  return http.put(`/patients/${id}`, patientData)
}

export const deletePatient = (id: string) => {
  return http.delete(`/patients/${id}`)
}

export const getPatientByCardId = (cardId: string) => {
  return http.get('/debug/patient-info', { params: { patientCardId: cardId } })
}

// 新增：通过patientId获取患者详细信息
export const getPatientById = (patientId: string, params?: { historyPage?: number; historySize?: number }) => {
  return http.get(`/patients/${patientId}`, { params })
}

// 获取下一个患者编号
export const getNextPatientNumber = () => {
  return http.get('/patients/next-number')
}

// 获取患者诊断详情
export const getPatientDiagnoses = (patientId: string | number) => {
  return http.get(`/patients/${patientId}/diagnoses`)
}

// 更新患者诊断信息
export const updatePatientDiagnosis = (patientId: string, diagnosis: string) => {
  return http.put(`/patients/${patientId}`, {
    diagnosis: diagnosis
  });
};
