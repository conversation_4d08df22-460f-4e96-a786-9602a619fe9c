package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.entity.enums.PatchType;
import com.Bone.BoneSys.repository.*;
import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.hardware.TreatmentParamsRequest;
import com.Bone.BoneSys.exception.TreatmentHeadNotTakenException;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.service.TreatmentHeadReturnDetectionService;
import com.Bone.BoneSys.websocket.NotificationWebSocketHandler;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/treatment-process")
@Slf4j
public class TreatmentProcessController {

    @Autowired
    private ProcessRepository processRepository;

    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;

    @Autowired
    private RecordRepository recordRepository;

    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;

    @Autowired
    private TreatmentHeadReturnDetectionService returnDetectionService;

    @Autowired
    private com.Bone.BoneSys.service.TreatmentHeadQuerySchedulerService querySchedulerService;

    @Autowired
    private NotificationWebSocketHandler notificationHandler;

    /**
     * 开始治疗进程
     * POST /api/treatment-process/start
     */
    @PostMapping("/start")
    public ApiResponse<StartTreatmentResponse> startTreatment(@RequestBody StartTreatmentRequest request) {
        try {
            log.info("API request: start treatment process for record {}", request.getRecordId());

            // 触发进入档案查询（在开始治疗进程时）
            try {
                querySchedulerService.triggerEnterRecordQuery();
                log.info("进入档案治疗，已触发治疗头查询");
            } catch (Exception e) {
                log.warn("触发治疗头查询失败，但继续治疗流程: {}", e.getMessage());
            }

            // 1. 验证档案是否存在
            var recordOpt = recordRepository.findById(request.getRecordId());
            if (recordOpt.isEmpty()) {
                return ApiResponse.notFound("档案不存在");
            }
            var record = recordOpt.get();

            // 2. 创建治疗进程
            var process = new com.Bone.BoneSys.entity.Process();
            process.setRecord(record);
            process.setTreatmentMode(request.getTreatmentMode());
            process.setStatus(ProcessStatus.IN_PROGRESS);
            process.setStartTime(LocalDateTime.now());
            process = processRepository.save(process);

            // 3. 创建治疗详情并启动硬件治疗
            var details = new java.util.ArrayList<TreatmentDetail>();
            var startedHeads = new java.util.ArrayList<Integer>();
            var notTakenHeads = new java.util.ArrayList<Integer>();

            // 启动进度统计（仅针对本地治疗推送 STARTUP_PROGRESS）
            final int totalHeads = request.getTreatmentDetails().size();
            int completed = 0;
            int failed = 0;
            if (request.getTreatmentMode() == TreatmentMode.ON_SITE) {
                // 初始进度
                sendStartupProgress(totalHeads, completed, failed, 0, "开始启动治疗头");
            }

            for (TreatmentDetailRequest detailReq : request.getTreatmentDetails()) {
                // 创建治疗详情记录
                var detail = new TreatmentDetail();
                detail.setProcess(process);
                detail.setBodyPart(detailReq.getBodyPart());
                detail.setHeadNumberUsed(detailReq.getHeadNumber());
                detail.setDuration(detailReq.getDuration());
                detail.setIntensity(new BigDecimal(detailReq.getIntensity()));
                detail.setFrequency(detailReq.getFrequency());
                detail.setPatchType(detailReq.getPatchType());
                detail.setPatchQuantity(detailReq.getPatchQuantity());
                detail.setStatus(TreatmentDetailStatus.TREATING);
                details.add(detail);

                // 🚨 硬件集成：根据治疗模式发送不同的硬件指令
                try {
                    boolean success;

                    if (request.getTreatmentMode() == TreatmentMode.ON_SITE) {
                        // 本地治疗：发送TWZS指令（发送参数并启动治疗）
                        // 推送：正在启动某治疗头
                        sendStartupProgress(totalHeads, completed, failed, detailReq.getHeadNumber(),
                                "正在启动治疗头 " + detailReq.getHeadNumber());

                        success = hardwareService.startTreatment(
                            detailReq.getHeadNumber(),
                            detailReq.getDuration(),
                            detailReq.getIntensity(),
                            detailReq.getFrequency()
                        );

                        if (success) {
                            log.info("Successfully sent TWZS command to start treatment on head {} for body part {}",
                                    detailReq.getHeadNumber(), detailReq.getBodyPart());
                            completed++;
                            // 推送：启动成功
                            sendStartupProgress(totalHeads, completed, failed, detailReq.getHeadNumber(),
                                    "治疗头 " + detailReq.getHeadNumber() + " 启动成功");
                        }

                    } else if (request.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
                        // 远端治疗：发送TWSDT指令（仅发送参数，不启动治疗）
                        TreatmentParamsRequest paramsRequest = new TreatmentParamsRequest();
                        paramsRequest.setDuration(detailReq.getDuration());
                        paramsRequest.setIntensity(detailReq.getIntensity());
                        paramsRequest.setFrequency(detailReq.getFrequency());
                        paramsRequest.setHeadNumbers(Arrays.asList(detailReq.getHeadNumber()));

                        success = hardwareService.sendTreatmentParams(paramsRequest);

                        if (success) {
                            log.info("Successfully sent TWSDT command to send parameters to head {} for body part {}",
                                    detailReq.getHeadNumber(), detailReq.getBodyPart());
                        }

                        // 远端治疗模式下，治疗详情状态设置为治疗中（参数已发送，等待治疗头按钮手动启动）
                        detail.setStatus(TreatmentDetailStatus.TREATING);

                    } else {
                        throw new IllegalArgumentException("Unsupported treatment mode: " + request.getTreatmentMode());
                    }

                    if (success) {
                        startedHeads.add(detailReq.getHeadNumber());
                    } else {
                        log.warn("Failed to send hardware command to head {} for body part {}",
                                detailReq.getHeadNumber(), detailReq.getBodyPart());
                        detail.setStatus(TreatmentDetailStatus.TERMINATED);
                        if (request.getTreatmentMode() == TreatmentMode.ON_SITE) {
                            failed++;
                            // 推送：启动失败
                            sendStartupProgress(totalHeads, completed, failed, detailReq.getHeadNumber(),
                                    "治疗头 " + detailReq.getHeadNumber() + " 启动失败");
                        }
                    }

                } catch (TreatmentHeadNotTakenException e) {
                    log.warn("Treatment head {} not taken from chamber: {}", detailReq.getHeadNumber(), e.getMessage());
                    detail.setStatus(TreatmentDetailStatus.TERMINATED);
                    notTakenHeads.add(detailReq.getHeadNumber());
                    if (request.getTreatmentMode() == TreatmentMode.ON_SITE) {
                        failed++;
                        sendStartupProgress(totalHeads, completed, failed, detailReq.getHeadNumber(),
                                "治疗头 " + detailReq.getHeadNumber() + " 未取出");
                    }
                } catch (Exception e) {
                    log.error("Error sending hardware command to head {}: {}", detailReq.getHeadNumber(), e.getMessage());
                    detail.setStatus(TreatmentDetailStatus.TERMINATED);
                    if (request.getTreatmentMode() == TreatmentMode.ON_SITE) {
                        failed++;
                        sendStartupProgress(totalHeads, completed, failed, detailReq.getHeadNumber(),
                                "治疗头 " + detailReq.getHeadNumber() + " 启动异常");
                    }
                }
            }

            // 4. 保存治疗详情
            treatmentDetailRepository.saveAll(details);

            // 最终进度（仅本地治疗）
            if (request.getTreatmentMode() == TreatmentMode.ON_SITE) {
                sendStartupProgress(request.getTreatmentDetails().size(),
                        startedHeads.size(),
                        request.getTreatmentDetails().size() - startedHeads.size(),
                        0,
                        "所有治疗头启动完成");
            }

            // 5. 检查是否有任何治疗头启动成功
            if (startedHeads.isEmpty()) {
                // 如果没有任何治疗头启动成功，标记进程为失败
                process.setStatus(ProcessStatus.CANCELLED);
                process.setEndTime(LocalDateTime.now());
                processRepository.save(process);

                // 生成详细的错误信息
                String errorMessage = "所有治疗头启动失败，治疗进程已取消";
                if (!notTakenHeads.isEmpty()) {
                    errorMessage += String.format("。治疗头 %s 需要从治疗仓取出并贴到治疗部位后才能启动治疗",
                                                 notTakenHeads.stream()
                                                             .map(String::valueOf)
                                                             .collect(Collectors.joining(", ")));
                }

                return ApiResponse.error(500, errorMessage);
            }

            // 6. 构建响应
            var response = new StartTreatmentResponse();
            response.setProcessId(process.getId());
            response.setStatus("STARTED");
            response.setStartedHeads(startedHeads);
            response.setTotalHeads(request.getTreatmentDetails().size());
            response.setMessage(String.format("治疗进程已启动，成功启动 %d/%d 个治疗头",
                                            startedHeads.size(), request.getTreatmentDetails().size()));

            log.info("Treatment process {} started successfully with {} heads",
                    process.getId(), startedHeads.size());

            return ApiResponse.success("治疗进程启动成功", response);

        } catch (Exception e) {
            log.error("Error starting treatment process", e);
            return ApiResponse.error(500, "启动治疗进程失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗进程详情页面数据
     */
    @GetMapping("/{processId}")
    public ApiResponse<TreatmentProcessVO> getTreatmentProcess(@PathVariable Long processId) {
        var processOpt = processRepository.findById(processId);
        if (processOpt.isEmpty()) {
            return ApiResponse.notFound("治疗进程不存在");
        }

        var process = processOpt.get();
        var record = process.getRecord();
        var patient = record.getPatient();
        var details = treatmentDetailRepository.findByProcessId(processId);

        return ApiResponse.success(new TreatmentProcessVO(process, patient, details));
    }

    /**
     * 结束整个治疗进程
     */
    @PostMapping("/{processId}/terminate")
    public ApiResponse<Void> terminateProcess(@PathVariable Long processId) {
        var processOpt = processRepository.findById(processId);
        if (processOpt.isEmpty()) {
            return ApiResponse.notFound("治疗进程不存在");
        }

        var process = processOpt.get();
        if (process.getStatus() != ProcessStatus.IN_PROGRESS) {
            return ApiResponse.badRequest("只能终止进行中的治疗进程");
        }

        // 更新进程状态为取消
        process.setStatus(ProcessStatus.CANCELLED);
        process.setEndTime(LocalDateTime.now());

        // 更新所有治疗详情状态为终止
        var details = treatmentDetailRepository.findByProcessId(processId);
        for (var detail : details) {
            if (detail.getStatus() == TreatmentDetailStatus.TREATING) {
                // 🚨 硬件集成：发送TWZO指令关闭治疗头
                try {
                    boolean hardwareSuccess = hardwareService.stopTreatment(detail.getHeadNumberUsed());
                    if (hardwareSuccess) {
                        log.info("Successfully sent TWZO command to stop treatment head {}", detail.getHeadNumberUsed());
                    } else {
                        log.warn("Failed to send TWZO command to treatment head {}", detail.getHeadNumberUsed());
                    }
                } catch (Exception e) {
                    log.error("Error sending TWZO command to treatment head {}: {}",
                             detail.getHeadNumberUsed(), e.getMessage());
                    // 不阻止软件层面的终止操作
                }

                detail.setStatus(TreatmentDetailStatus.TERMINATED);
            }
        }

        processRepository.save(process);
        treatmentDetailRepository.saveAll(details);

        log.info("治疗进程 {} 已被终止", processId);
        return ApiResponse.success();
    }

    /**
     * 终止指定部位的治疗
     */
    @PostMapping("/detail/{detailId}/terminate")
    public ApiResponse<Void> terminateDetail(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }

        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.TREATING) {
            return ApiResponse.badRequest("只能终止正在治疗中的项目");
        }

        // 🚨 硬件集成：发送TWZO指令关闭治疗头
        try {
            boolean hardwareSuccess = hardwareService.stopTreatment(detail.getHeadNumberUsed());
            if (hardwareSuccess) {
                log.info("Successfully sent TWZO command to stop treatment head {}", detail.getHeadNumberUsed());
            } else {
                log.warn("Failed to send TWZO command to treatment head {}, but continuing with software termination",
                        detail.getHeadNumberUsed());
            }
        } catch (Exception e) {
            log.error("Error sending TWZO command to treatment head {}: {}",
                     detail.getHeadNumberUsed(), e.getMessage());
            // 不阻止软件层面的终止操作
        }

        // 更新治疗详情状态为终止
        detail.setStatus(TreatmentDetailStatus.TERMINATED);
        treatmentDetailRepository.save(detail);

        log.info("治疗详情 {} (部位: {}) 已被终止", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 完成指定部位的治疗
     */
    @PostMapping("/detail/{detailId}/complete")
    public ApiResponse<Void> completeDetail(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }

        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.TREATING) {
            return ApiResponse.badRequest("只能完成正在治疗中的项目");
        }

        // 更新治疗详情状态为完成
        detail.setStatus(TreatmentDetailStatus.COMPLETED);
        treatmentDetailRepository.save(detail);

        // 更新部位统计数据
        updateBodyPartStats(detail);

        // 检查是否所有治疗详情都已完成，如果是则完成整个进程
        checkAndCompleteProcess(detail.getProcess().getId());

        log.info("治疗详情 {} (部位: {}) 已完成", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 设置指定部位的治疗为待取回状态
     */
    @PostMapping("/detail/{detailId}/awaiting-return")
    public ApiResponse<Void> setAwaitingReturn(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }

        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.TREATING) {
            return ApiResponse.badRequest("只能设置正在治疗中的项目为待取回状态");
        }

        // 更新治疗详情状态为待取回
        detail.setStatus(TreatmentDetailStatus.AWAITING_RETURN);
        treatmentDetailRepository.save(detail);

        // 更新部位统计数据（远端治疗进入待取回状态时）
        updateBodyPartStats(detail);

        // 检查是否所有治疗详情都已达到待取回状态，如果是则更新整个进程状态
        checkAndUpdateProcessForAwaitingReturn(detail.getProcess().getId());

        log.info("治疗详情 {} (部位: {}) 已设置为待取回状态", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 确认治疗头已归还（硬件检测到归还后调用）
     */
    @PostMapping("/detail/{detailId}/returned")
    public ApiResponse<Void> confirmReturned(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }

        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.COMPLETED &&
            detail.getStatus() != TreatmentDetailStatus.AWAITING_RETURN) {
            return ApiResponse.badRequest("只能确认已完成或待取回状态的治疗头归还");
        }

        // 更新治疗详情状态为已归还
        detail.setStatus(TreatmentDetailStatus.RETURNED);
        treatmentDetailRepository.save(detail);

        // 检查是否所有治疗详情都已归还，如果是则完成整个进程
        checkAndCompleteProcessForReturned(detail.getProcess().getId());

        log.info("治疗详情 {} (部位: {}) 已确认归还", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 手动触发治疗头归还检测
     * POST /api/treatment-process/detect-returns
     *
     * 通过硬件主板查询治疗头状态，自动检测并更新已归还的治疗头
     */
    @PostMapping("/detect-returns")
    public ApiResponse<Void> detectTreatmentHeadReturns() {
        try {
            int returnedCount = returnDetectionService.manualDetectReturns();

            if (returnedCount > 0) {
                return ApiResponse.success("检测完成，发现 " + returnedCount + " 个治疗头已归还", null);
            } else {
                return ApiResponse.success("检测完成，没有发现新的归还治疗头", null);
            }
        } catch (Exception e) {
            log.error("手动检测治疗头归还失败", e);
            return ApiResponse.error(500, "检测治疗头归还失败: " + e.getMessage());
        }
    }

    /**
     * 获取硬件通信状态信息
     * GET /api/treatment-process/hardware-status
     */
    @GetMapping("/hardware-status")
    public ApiResponse<String> getHardwareStatus() {
        try {
            String hardwareInfo = hardwareService.getHardwareInfo();
            return ApiResponse.success("获取硬件状态成功", hardwareInfo);
        } catch (Exception e) {
            log.error("获取硬件状态失败", e);
            return ApiResponse.error(500, "获取硬件状态失败: " + e.getMessage());
        }
    }

    /**
     * 完成整个治疗进程
     */
    @PostMapping("/{processId}/complete")
    public ApiResponse<Void> completeProcess(@PathVariable Long processId) {
        var processOpt = processRepository.findById(processId);
        if (processOpt.isEmpty()) {
            return ApiResponse.notFound("治疗进程不存在");
        }

        var process = processOpt.get();
        if (process.getStatus() != ProcessStatus.IN_PROGRESS) {
            return ApiResponse.badRequest("只能完成进行中的治疗进程");
        }

        // 完成所有未完成的治疗详情
        var details = treatmentDetailRepository.findByProcessId(processId);
        for (var detail : details) {
            if (detail.getStatus() == TreatmentDetailStatus.TREATING) {
                detail.setStatus(TreatmentDetailStatus.COMPLETED);
                updateBodyPartStats(detail);
            }
        }

        // 更新进程状态为完成
        process.setStatus(ProcessStatus.COMPLETED);
        process.setEndTime(LocalDateTime.now());

        // 更新档案的完成次数
        var record = process.getRecord();
        record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);

        processRepository.save(process);
        treatmentDetailRepository.saveAll(details);
        recordRepository.save(record);

        log.info("治疗进程 {} 已完成，档案 {} 完成次数更新为 {}",
                processId, record.getRecordNumber(), record.getSessionsCompletedCount());
        return ApiResponse.success();
    }

    /**
     * 更新部位统计数据
     */
    public void updateBodyPartStats(TreatmentDetail detail) {
        var process = detail.getProcess();
        var record = process.getRecord();
        var patient = record.getPatient();

        // 查找或创建部位统计记录
        var statOpt = bodyPartStatRepository.findByRecordIdAndBodyPart(record.getId(), detail.getBodyPart());
        BodyPartStat stat;

        if (statOpt.isPresent()) {
            stat = statOpt.get();
        } else {
            stat = new BodyPartStat();
            stat.setRecord(record);
            stat.setBodyPart(detail.getBodyPart());
            stat.setTotalUsageCount(0);
            stat.setTotalDurationMinutes(0);
        }

        // 更新统计数据
        stat.setTotalUsageCount(stat.getTotalUsageCount() + 1);
        stat.setTotalDurationMinutes(stat.getTotalDurationMinutes() + detail.getDuration());

        bodyPartStatRepository.save(stat);
        log.info("更新部位统计 - 患者: {}, 部位: {}, 使用次数: {}, 总时长: {}分钟",
                patient.getName(), detail.getBodyPart(), stat.getTotalUsageCount(), stat.getTotalDurationMinutes());
    }

    /**
     * 检查并完成进程（根据治疗模式和状态组合判断进程状态）
     */
    public void checkAndCompleteProcess(Long processId) {
        var details = treatmentDetailRepository.findByProcessId(processId);
        var processOpt = processRepository.findById(processId);

        if (processOpt.isEmpty()) {
            return;
        }

        var process = processOpt.get();
        if (process.getStatus() != ProcessStatus.IN_PROGRESS) {
            return;
        }

        // 统计各种状态的治疗详情数量
        long completedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.COMPLETED).count();
        long terminatedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.TERMINATED).count();
        long returnedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.RETURNED).count();
        long totalCount = details.size();

        log.debug("进程 {} 状态统计: 已完成={}, 已终止={}, 已归还={}, 总数={}",
                 processId, completedCount, terminatedCount, returnedCount, totalCount);

        if (process.getTreatmentMode() == TreatmentMode.ON_SITE) {
            // 本地治疗逻辑：根据文档要求
            if (terminatedCount == totalCount) {
                // 只有此进程对应的treatment_details表的status均为TERMINATED状态，其processes表的status才变成CANCELLED
                process.setStatus(ProcessStatus.CANCELLED);
                process.setEndTime(LocalDateTime.now());
                processRepository.save(process);
                log.info("本地治疗进程 {} 已取消，所有治疗详情均为终止状态", processId);
            } else if ((completedCount + returnedCount + terminatedCount) == totalCount && (completedCount > 0 || returnedCount > 0)) {
                // 只有当此患者此次档案的全部进程对应的treatment_details表的status均为COMPLETED或者RETURNED或者TERMINATED的时候，进程才完成
                process.setStatus(ProcessStatus.COMPLETED);
                process.setEndTime(LocalDateTime.now());
                updateRecordCompletionCount(process.getRecord());
                processRepository.save(process);
                log.info("本地治疗进程 {} 已完成，所有治疗详情已完成或归还", processId);
            }
        }
        // 注意：远端治疗的进程状态更新在checkAndUpdateProcessForAwaitingReturn方法中处理
    }

    /**
     * 更新档案完成次数
     */
    private void updateRecordCompletionCount(com.Bone.BoneSys.entity.Record record) {
        record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);
        recordRepository.save(record);
    }

    /**
     * 检查并更新进程状态（根据文档要求的状态更新逻辑）
     */
    public void checkAndUpdateProcessForAwaitingReturn(Long processId) {
        var details = treatmentDetailRepository.findByProcessId(processId);
        var processOpt = processRepository.findById(processId);

        if (processOpt.isEmpty() || processOpt.get().getStatus() != ProcessStatus.IN_PROGRESS) {
            return;
        }

        var process = processOpt.get();

        // 统计各种状态的治疗详情数量
        long awaitingReturnCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.AWAITING_RETURN).count();
        long returnedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.RETURNED).count();
        long terminatedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.TERMINATED).count();
        long completedCount = details.stream().filter(d -> d.getStatus() == TreatmentDetailStatus.COMPLETED).count();
        long totalCount = details.size();

        log.debug("进程 {} 状态统计: 待取回={}, 已归还={}, 已终止={}, 已完成={}, 总数={}",
                 processId, awaitingReturnCount, returnedCount, terminatedCount, completedCount, totalCount);

        if (process.getTreatmentMode() == TreatmentMode.ON_SITE) {
            // 本地治疗逻辑
            if (terminatedCount == totalCount) {
                // 所有治疗详情都被终止 → 进程状态变成取消状态
                process.setStatus(ProcessStatus.CANCELLED);
                process.setEndTime(LocalDateTime.now());
                processRepository.save(process);
                log.info("本地治疗进程 {} 已取消，所有治疗详情均为终止状态", processId);
            } else if (returnedCount == totalCount || (completedCount + returnedCount + terminatedCount) == totalCount) {
                // 所有治疗详情都已归还，或者所有治疗详情都处于最终状态 → 进程状态变成已完成
                process.setStatus(ProcessStatus.COMPLETED);
                process.setEndTime(LocalDateTime.now());
                updateRecordCompletionCount(process.getRecord());
                processRepository.save(process);
                log.info("本地治疗进程 {} 已完成，所有治疗详情已归还或完成", processId);
            }
        } else if (process.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
            // 远端治疗逻辑

            // 检查是否全部详情都进入最终状态（AWAITING_RETURN 或 TERMINATED），且尚未计数
            long finalStateCount = awaitingReturnCount + terminatedCount;
            if (finalStateCount == totalCount && !process.getRemoteCounted()) {
                // 远端治疗临床完成：全部详情都不再治疗，更新档案完成次数
                updateRecordCompletionCount(process.getRecord());
                process.setRemoteCounted(true);
                processRepository.save(process);
                log.info("远端治疗进程 {} 临床完成，全部详情已进入最终状态（待取回/终止），档案完成次数已更新", processId);
            }

            if (terminatedCount == totalCount) {
                // 只有此进程对应的treatment_details表的status均为TERMINATED状态，其processes表的status才变成CANCELLED
                process.setStatus(ProcessStatus.CANCELLED);
                process.setEndTime(LocalDateTime.now());
                processRepository.save(process);
                log.info("远端治疗进程 {} 已取消，所有治疗详情均为终止状态", processId);
            } else if (returnedCount == totalCount) {
                // 当此次进程的treatment_details表的status为RETURNED时，其processes表的status变成COMPLETED
                process.setStatus(ProcessStatus.COMPLETED);
                process.setEndTime(LocalDateTime.now());
                // 注意：这里不再重复更新档案完成次数，因为在临床完成时已经更新过
                processRepository.save(process);
                log.info("远端治疗进程 {} 已完成，所有治疗详情已归还", processId);
            }
            // 注意：远端治疗进程在AWAITING_RETURN状态时保持IN_PROGRESS，不变更为其他状态
        }
    }

    /**
     * 检查并完成进程（当所有治疗详情都已归还时）
     */
    private void checkAndCompleteProcessForReturned(Long processId) {
        var details = treatmentDetailRepository.findByProcessId(processId);
        boolean allReturned = details.stream()
                .allMatch(detail -> detail.getStatus() == TreatmentDetailStatus.RETURNED);

        if (allReturned) {
            var processOpt = processRepository.findById(processId);
            if (processOpt.isPresent()) {
                var process = processOpt.get();
                if (process.getStatus() != ProcessStatus.COMPLETED) {
                    // 所有治疗头都已归还，完成进程并更新档案统计
                    process.setStatus(ProcessStatus.COMPLETED);
                    process.setEndTime(LocalDateTime.now());

                    // 更新档案完成次数
                    var record = process.getRecord();
                    record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);

                    processRepository.save(process);
                    recordRepository.save(record);

                    log.info("进程 {} 已完成，所有治疗头已归还", processId);
                }
            }
        }
    }

    /**
     * 治疗进程页面数据VO
     */
    @Data
    public static class TreatmentProcessVO {
        private Long processId;
        private String patientName;
        private String patientCardId;
        private String treatmentMode;
        private String status;
        private LocalDateTime startTime;
        private List<TreatmentDetailVO> details;

        public TreatmentProcessVO(com.Bone.BoneSys.entity.Process process, Patient patient, List<TreatmentDetail> details) {
            this.processId = process.getId();
            this.patientName = patient.getName();
            this.patientCardId = patient.getPatientCardId();
            this.treatmentMode = process.getTreatmentMode().name();
            this.status = process.getStatus().name();
            this.startTime = process.getStartTime();
            this.details = details.stream().map(TreatmentDetailVO::new).collect(Collectors.toList());
        }
    }

    /**
     * 治疗详情VO
     */
    @Data
    public static class TreatmentDetailVO {
        private Long detailId;
        private String bodyPart;
        private Integer headNumberUsed;
        private Integer duration;
        private BigDecimal intensity;
        private Integer frequency;
        private String patchType;
        private Integer patchQuantity;
        private String status;
        private String statusDescription;

        public TreatmentDetailVO(TreatmentDetail detail) {
            this.detailId = detail.getId();
            this.bodyPart = detail.getBodyPart();
            this.headNumberUsed = detail.getHeadNumberUsed();
            this.duration = detail.getDuration();
            this.intensity = detail.getIntensity();
            this.frequency = detail.getFrequency();
            this.patchType = detail.getPatchType().name();
            this.patchQuantity = detail.getPatchQuantity();
            this.status = detail.getStatus().name();
            this.statusDescription = getStatusDescription(detail.getStatus());
        }

        private String getStatusDescription(TreatmentDetailStatus status) {
            switch (status) {
                case TREATING: return "治疗中";
                case COMPLETED: return "已完成";
                case TERMINATED: return "已终止";
                case AWAITING_RETURN: return "等待归还";
                case RETURNED: return "已归还";
                default: return status.name();
            }
        }
    }

    /**
     * 开始治疗请求DTO
     */
    @Data
    public static class StartTreatmentRequest {
        private Long recordId;
        private TreatmentMode treatmentMode;
        private List<TreatmentDetailRequest> treatmentDetails;
    }

    /**
     * 治疗详情请求DTO
     */
    @Data
    public static class TreatmentDetailRequest {
        private String bodyPart;
        private Integer headNumber;
        private Integer duration;
        private Integer intensity;
        private Integer frequency;
        private PatchType patchType;
        private Integer patchQuantity;
    }

    /**
     * 开始治疗响应DTO
     */
    @Data
    public static class StartTreatmentResponse {
        private Long processId;
        private String status;
        private List<Integer> startedHeads;
        private Integer totalHeads;
        private String message;
    }

    /**
     * 推送本地治疗启动进度（进度条）
     */
    private void sendStartupProgress(int total, int completed, int failed, int current, String operation) {
        try {
            var data = new com.Bone.BoneSys.websocket.NotificationWebSocketHandler.StartupProgressData();
            data.setTotal(total);
            data.setCompleted(completed);
            data.setFailed(failed);
            data.setCurrent(current);
            data.setCurrentOperation(operation);
            if (total > 0) {
                data.setProgress(((double) (completed + failed) / (double) total) * 100.0);
            } else {
                data.setProgress(0.0);
            }
            notificationHandler.sendStartupProgressNotification(data);
        } catch (Exception ex) {
            log.warn("发送启动进度通知失败: {}", ex.getMessage());
        }
    }
}


