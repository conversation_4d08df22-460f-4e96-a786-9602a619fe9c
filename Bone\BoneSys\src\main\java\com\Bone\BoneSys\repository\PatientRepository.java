package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.Patient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 患者数据访问接口
 */
@Repository
public interface PatientRepository extends JpaRepository<Patient, Long> {
    
    /**
     * 根据就诊卡号查找患者
     */
    Optional<Patient> findByPatientCardId(String patientCardId);
    
    /**
     * 检查就诊卡号是否存在
     */
    boolean existsByPatientCardId(String patientCardId);
    
    /**
     * 根据姓名模糊查询患者
     */
    Page<Patient> findByNameContaining(String name, Pageable pageable);
    
    /**
     * 根据就诊卡号和姓名模糊查询患者
     */
    @Query("SELECT p FROM Patient p WHERE " +
           "(:patientCardId IS NULL OR p.patientCardId LIKE %:patientCardId%) AND " +
           "(:name IS NULL OR p.name LIKE %:name%)")
    Page<Patient> findByPatientCardIdContainingAndNameContaining(
            @Param("patientCardId") String patientCardId,
            @Param("name") String name,
            Pageable pageable);
    
    /**
     * 获取最近创建的患者（用于新建档案界面显示）
     */
    @Query("SELECT p FROM Patient p ORDER BY p.createdAt DESC")
    Page<Patient> findRecentPatients(Pageable pageable);
    
    /**
     * 根据性别统计患者数量
     */
    @Query("SELECT p.gender, COUNT(p) FROM Patient p GROUP BY p.gender")
    Object[] countByGender();

    /**
     * 获取最大患者ID（用于生成患者编号）
     */
    @Query("SELECT MAX(p.id) FROM Patient p")
    Long findMaxId();

    /**
     * 根据姓名或就诊卡号模糊查询患者
     */
    Page<Patient> findByNameContainingOrPatientCardIdContaining(String name, String cardId, Pageable pageable);
    
    /**
     * 查询患者及其关联的档案和部位统计（用于数据聚合）
     */
    @Query("SELECT DISTINCT p FROM Patient p " +
           "LEFT JOIN FETCH p.records r " +
           "LEFT JOIN FETCH r.bodyPartStats bps " +
           "WHERE (:search IS NULL OR p.name LIKE %:search% OR p.patientCardId LIKE %:search%) " +
           "ORDER BY p.createdAt DESC")
    Page<Patient> findPatientsWithRecordsAndStats(@Param("search") String search, Pageable pageable);
    
    /**
     * 优化的患者基本信息查询（不加载关联数据，提高性能）
     */
    @Query("SELECT p FROM Patient p " +
           "WHERE (:search IS NULL OR p.name LIKE %:search% OR p.patientCardId LIKE %:search%) " +
           "ORDER BY p.createdAt DESC")
    Page<Patient> findPatientsOptimized(@Param("search") String search, Pageable pageable);
    
    /**
     * 批量查询患者基本信息（用于数据聚合服务）
     */
    @Query("SELECT p FROM Patient p WHERE p.id IN :patientIds")
    List<Patient> findByIdIn(@Param("patientIds") List<Long> patientIds);
    
    /**
     * 统计搜索结果数量（用于性能优化）
     */
    @Query("SELECT COUNT(p) FROM Patient p " +
           "WHERE (:search IS NULL OR p.name LIKE %:search% OR p.patientCardId LIKE %:search%)")
    long countBySearchCriteria(@Param("search") String search);
}