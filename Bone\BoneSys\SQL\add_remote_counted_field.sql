-- 为远端治疗统计更新逻辑添加防重字段
-- 在 processes 表中新增 remote_counted 字段（如果不存在）

USE bonesys;

-- 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'bonesys'
    AND TABLE_NAME = 'processes'
    AND COLUMN_NAME = 'remote_counted'
);

-- 动态添加字段（仅当不存在时）
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE processes ADD COLUMN remote_counted TINYINT(1) NOT NULL DEFAULT 0 COMMENT ''远端治疗是否已计数档案完成次数，防止重复统计''',
    'SELECT ''remote_counted 字段已存在，跳过添加'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保所有现有进程的 remote_counted 字段都设为 false（如果为 NULL）
UPDATE processes SET remote_counted = 0 WHERE remote_counted IS NULL;

-- 验证字段状态
DESCRIBE processes;

-- 显示结果
SELECT
    CASE
        WHEN @column_exists = 0 THEN 'remote_counted 字段添加完成'
        ELSE 'remote_counted 字段已存在，已确保数据一致性'
    END AS result,
    NOW() AS timestamp;
