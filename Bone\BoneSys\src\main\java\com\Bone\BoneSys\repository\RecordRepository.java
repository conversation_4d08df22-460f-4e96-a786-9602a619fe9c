package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.Record;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 档案数据访问接口
 */
@Repository
public interface RecordRepository extends JpaRepository<Record, Long> {
    
    /**
     * 根据档案编号查找档案
     */
    Optional<Record> findByRecordNumber(String recordNumber);
    
    /**
     * 检查档案编号是否存在
     */
    boolean existsByRecordNumber(String recordNumber);
    
    /**
     * 根据患者ID查找档案
     */
    List<Record> findByPatientId(Long patientId);
    
    /**
     * 根据患者就诊卡号查找档案
     */
    @Query("SELECT r FROM Record r JOIN r.patient p WHERE p.patientCardId = :patientCardId")
    List<Record> findByPatientCardId(@Param("patientCardId") String patientCardId);
    
    /**
     * 根据患者姓名模糊查询档案
     */
    @Query("SELECT r FROM Record r JOIN r.patient p WHERE p.name LIKE %:patientName%")
    Page<Record> findByPatientNameContaining(@Param("patientName") String patientName, Pageable pageable);
    
    /**
     * 根据患者卡号和姓名查询档案
     */
    @Query("SELECT r FROM Record r JOIN r.patient p WHERE " +
           "(:patientCardId IS NULL OR p.patientCardId LIKE %:patientCardId%) AND " +
           "(:patientName IS NULL OR p.name LIKE %:patientName%)")
    Page<Record> findByPatientCardIdAndNameContaining(
            @Param("patientCardId") String patientCardId,
            @Param("patientName") String patientName,
            Pageable pageable);
    
    /**
     * 根据创建日期范围查询档案
     */
    Page<Record> findByCreatedAtBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * 统计总档案数量
     */
    @Query("SELECT COUNT(r) FROM Record r")
    Long countTotalRecords();
    
    /**
     * 根据日期统计档案数量
     */
    @Query("SELECT r.createdAt, COUNT(r) FROM Record r GROUP BY r.createdAt ORDER BY r.createdAt DESC")
    List<Object[]> countByCreatedDate();

    /**
     * 根据患者姓名或就诊卡号模糊查询档案
     */
    @Query("SELECT r FROM Record r JOIN r.patient p WHERE " +
           "p.name LIKE %:search% OR p.patientCardId LIKE %:search%")
    Page<Record> findByPatientNameContainingOrPatientCardIdContaining(
            @Param("search") String search1,
            @Param("search") String search2,
            Pageable pageable);

    /**
     * 根据患者就诊卡号查询档案（分页）
     */
    @Query("SELECT r FROM Record r JOIN r.patient p WHERE p.patientCardId = :patientCardId")
    Page<Record> findByPatientCardId(@Param("patientCardId") String patientCardId, Pageable pageable);
    
    /**
     * 根据患者ID按创建时间排序查询档案
     */
    @Query("SELECT r FROM Record r WHERE r.patient.id = :patientId ORDER BY r.createdAt DESC")
    List<Record> findByPatientIdOrderByCreatedAtDesc(@Param("patientId") Long patientId);
}