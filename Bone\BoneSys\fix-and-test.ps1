# Fix compartment_type and test treatment heads availability
Write-Host "========================================" -ForegroundColor Green
Write-Host "Fix and Test Treatment Heads" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Step 1: Fix database
Write-Host "[Step 1] Fixing compartment_type in database..." -ForegroundColor Yellow
try {
    mysql -u root -p bonesys -e "
    ALTER TABLE treatment_heads 
    ADD COLUMN IF NOT EXISTS compartment_type VARCHAR(10) NULL DEFAULT NULL 
    COMMENT 'Compartment type: SHALLOW or DEEP' 
    AFTER slot_number;
    
    UPDATE treatment_heads 
    SET compartment_type = CASE 
        WHEN head_number BETWEEN 1 AND 10 THEN 'SHALLOW'
        WHEN head_number BETWEEN 11 AND 20 THEN 'DEEP'
        ELSE NULL
    END
    WHERE compartment_type IS NULL OR compartment_type = '';
    "
    Write-Host "✅ Database fixed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Database fix failed: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Verify database
Write-Host ""
Write-Host "[Step 2] Verifying database..." -ForegroundColor Yellow
$dbResult = mysql -u root -p bonesys -e "
SELECT 
    compartment_type,
    COUNT(*) as count,
    COUNT(CASE WHEN battery_level >= 20 THEN 1 END) as available
FROM treatment_heads 
GROUP BY compartment_type;
"
Write-Host $dbResult

# Step 3: Test debug API
Write-Host ""
Write-Host "[Step 3] Testing debug API..." -ForegroundColor Yellow
try {
    $debugResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/treatment-parameters/debug-heads" -Method Get -TimeoutSec 10
    Write-Host "✅ Debug API working" -ForegroundColor Green
    Write-Host "Total heads: $($debugResponse.data.totalHeads)" -ForegroundColor White
    Write-Host "Shallow count: $($debugResponse.data.shallowCount)" -ForegroundColor White
    Write-Host "Deep count: $($debugResponse.data.deepCount)" -ForegroundColor White
    Write-Host "Null compartment count: $($debugResponse.data.nullCompartmentCount)" -ForegroundColor White
    Write-Host "Shallow available: $($debugResponse.data.shallowAvailable)" -ForegroundColor White
    Write-Host "Deep available: $($debugResponse.data.deepAvailable)" -ForegroundColor White
} catch {
    Write-Host "❌ Debug API failed: $_" -ForegroundColor Red
}

# Step 4: Test availability API
Write-Host ""
Write-Host "[Step 4] Testing availability API..." -ForegroundColor Yellow

$testBody = @{
    patientId = "TEST001"
    treatmentMode = "local"
    bodyParts = @(
        @{
            name = "shoulder"
            color = "#FF0000"
            parameters = @{
                time = "15分钟"
                intensity = "30"
                frequency = "1000"
                depth = "深部"
                count = 2
            }
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $availabilityResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/treatment-parameters/check-availability" -Method Post -Body $testBody -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ Availability API working" -ForegroundColor Green
    Write-Host "Sufficient: $($availabilityResponse.data.sufficient)" -ForegroundColor White
    Write-Host "Deep needed: $($availabilityResponse.data.deepNeeded)" -ForegroundColor White
    Write-Host "Deep available: $($availabilityResponse.data.deepAvailable)" -ForegroundColor White
    Write-Host "Deep sufficient: $($availabilityResponse.data.deepSufficient)" -ForegroundColor White
    
    if ($availabilityResponse.data.deepAvailable -gt 0) {
        Write-Host "🎉 Problem fixed! Deep heads are now available." -ForegroundColor Green
    } else {
        Write-Host "⚠️ Still showing 0 deep available heads. Check backend logs." -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Availability API failed: $_" -ForegroundColor Red
}

# Step 5: Show sample data
Write-Host ""
Write-Host "[Step 5] Sample treatment head data..." -ForegroundColor Yellow
$sampleData = mysql -u root -p bonesys -e "
SELECT 
    head_number,
    compartment_type,
    realtime_status,
    battery_level
FROM treatment_heads 
WHERE head_number BETWEEN 15 AND 20
ORDER BY head_number;
"
Write-Host $sampleData

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Fix and Test Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "If deep available is still 0, restart the backend application:" -ForegroundColor Cyan
Write-Host "cd BoneSys && ./gradlew bootRun" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
