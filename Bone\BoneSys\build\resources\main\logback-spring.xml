<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" packagingData="true">

    <!-- 日志目录，支持通过环境变量 LOG_DIR 覆盖，默认 logs -->
    <springProperty scope="context" name="LOG_DIR" source="LOG_DIR" defaultValue="logs"/>
    <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 应用总日志：按天+按大小滚动 -->
    <appender name="APP" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/application.log</file>
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/application.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 硬件通信专用日志：按天+按大小滚动 -->
    <appender name="HARDWARE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/hardware.log</file>
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/hardware.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 业务包默认级别在各环境的 application-*.properties 中控制，这里仅定义输出去向 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="APP"/>
    </root>

    <!-- 硬件通信相关类：额外写入硬件日志文件，同时也进入总日志 -->
    <logger name="com.Bone.BoneSys.service.HardwareService" level="DEBUG" additivity="true">
        <appender-ref ref="HARDWARE"/>
    </logger>
    <logger name="com.Bone.BoneSys.service.DualLayerSerialCommunicationService" level="DEBUG" additivity="true">
        <appender-ref ref="HARDWARE"/>
    </logger>
    <logger name="com.Bone.BoneSys.service.SerialHardwareCommunicationAdapter" level="DEBUG" additivity="true">
        <appender-ref ref="HARDWARE"/>
    </logger>
    <logger name="com.Bone.BoneSys.service.WebSocketHardwareService" level="DEBUG" additivity="true">
        <appender-ref ref="HARDWARE"/>
    </logger>
    <logger name="com.Bone.BoneSys.service.WebSocketHardwareCommunicationAdapter" level="DEBUG" additivity="true">
        <appender-ref ref="HARDWARE"/>
    </logger>
</configuration> 