# 开发环境配置

# 硬件通信模式配置
hardware.communication.mode=websocket

# 硬件模拟器配置（开发环境启用）
hardware.simulator.enabled=true

# WebSocket硬件通信配置
hardware.websocket.url=ws://122.51.229.122:6123
hardware.websocket.timeout=30000
hardware.websocket.reconnect.enabled=true
hardware.websocket.reconnect.interval=2000
hardware.websocket.reconnect.max-attempts=1

# 双层串口配置 (开发环境)
# 开发模式：禁用真实串口，启用模拟响应
hardware.serial.enabled=false
hardware.serial.dev-mode=true
serial.port.upper=COM3
serial.port.lower=COM4
serial.port.auto-detect=true
serial.port.baud-rate=115200
serial.port.data-bits=8
serial.port.stop-bits=1
serial.port.parity=0
serial.port.timeout=5000

# 治疗头自动同步配置
treatment-head.sync.enabled=true
treatment-head.sync.interval=10000
treatment-head.sync.startup-delay=5000
treatment-head.sync.timeout=5000
treatment-head.sync.retry-attempts=3
treatment-head.sync.retry-delay=2000

# 日志配置（开发环境详细日志）
logging.level.com.Bone.BoneSys=DEBUG
logging.level.com.Bone.BoneSys.service.WebSocketHardwareService=DEBUG
logging.level.com.Bone.BoneSys.service.WebSocketHardwareCommunicationAdapter=DEBUG
logging.level.com.Bone.BoneSys.service.HardwareService=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# 跨域配置（开发环境）
cors.allowed-origins=http://localhost:3000,http://localhost:8080,http://localhost:8081