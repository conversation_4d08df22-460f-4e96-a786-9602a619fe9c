# 🔌 硬件通信模式切换系统

## 📋 系统概述

硬件通信系统支持**远程硬件**和**串口通信**两种模式切换，实现灵活的硬件指令传输方式。

## 🔄 通信模式

### **1. 远程硬件模式（WebSocket）**
- **通信方式**: WebSocket连接
- **适用场景**: 远程硬件设备、网络化部署
- **服务类**: `WebSocketHardwareService`
- **特点**: 支持实时双向通信、网络传输

### **2. 串口通信模式（Serial）**
- **通信方式**: 串口直连
- **适用场景**: 本地硬件设备、直接连接
- **服务类**: `SerialCommunicationService`
- **特点**: 稳定可靠、低延迟

## 🏗️ 系统架构

### **核心服务层**

#### **HardwareService**
- **功能**: 硬件操作统一入口
- **职责**: 调用底层通信服务，处理业务逻辑
- **方法**: 
  - `syncAllTreatmentHeads()` - 同步治疗头数据
  - `setTreatmentHeadLights()` - 设置指示灯
  - `startTreatment()` - 开始治疗
  - `stopTreatment()` - 停止治疗

#### **WebSocketHardwareService**
- **功能**: WebSocket远程硬件通信
- **连接**: 自动重连机制
- **配置**: 支持连接参数配置

#### **SerialCommunicationService**
- **功能**: 串口直连硬件通信
- **端口**: 自动检测可用串口
- **配置**: 波特率、数据位等参数

#### **HardwareCommandParser**
- **功能**: 硬件指令构建和解析
- **支持指令**:
  - `TRZI` - 查询所有治疗头数据
  - `TWSC` - 点亮推荐治疗头指示灯
  - `TWSN` - 关闭推荐治疗头指示灯
  - `TWSDT` - 向治疗头发送治疗参数
  - `TWS` - 向治疗头发送治疗参数并工作
  - `TWZO` - 关闭治疗头指令

## ⚙️ 配置切换

### **应用配置文件**
```yaml
# application.yml
hardware:
  communication:
    mode: websocket  # websocket 或 serial
    websocket:
      url: ws://localhost:8081/hardware
      reconnect: true
      timeout: 5000
    serial:
      port: COM3
      baudRate: 9600
      dataBits: 8
      stopBits: 1
      parity: none
```

### **环境变量配置**
```bash
# WebSocket模式
HARDWARE_MODE=websocket
WEBSOCKET_URL=ws://*************:8081/hardware

# 串口模式
HARDWARE_MODE=serial
SERIAL_PORT=COM3
SERIAL_BAUDRATE=9600
```

## 🔧 使用方式

### **统一调用接口**
```java
@Autowired
private HardwareService hardwareService;

// 查询治疗头（自动选择通信模式）
List<TreatmentHeadInfo> heads = hardwareService.syncAllTreatmentHeads();

// 开始治疗（自动选择通信模式）
boolean success = hardwareService.startTreatment(headNumber, duration, intensity, frequency);
```

### **模式检测**
```java
// 检查连接状态
boolean connected = hardwareService.isHardwareConnected();

// 获取连接信息
String info = hardwareService.getHardwareInfo();

// 测试连接
boolean testResult = hardwareService.testHardwareConnection();
```

## 🚀 指令传输流程

### **WebSocket模式流程**
```mermaid
sequenceDiagram
    participant App as 应用
    participant WS as WebSocketService
    participant Hardware as 远程硬件
    
    App->>WS: 发送指令
    WS->>Hardware: WebSocket传输
    Hardware->>WS: 返回响应
    WS->>App: 解析结果
```

### **串口模式流程**
```mermaid
sequenceDiagram
    participant App as 应用
    participant Serial as SerialService
    participant Hardware as 本地硬件
    
    App->>Serial: 发送指令
    Serial->>Hardware: 串口传输
    Hardware->>Serial: 返回响应
    Serial->>App: 解析结果
```

## 📊 指令格式

### **通用指令格式**
所有指令都以`\r\n`结尾，响应格式统一：

```
发送: [指令][参数]\r\n
响应: [指令][数据]\r\n
```

### **示例指令**
```bash
# 查询治疗头
发送: TRZI\r\n
响应: TRZI05110951201095220209533030954404095550509566060957\r\n

# 点亮指示灯
发送: TWSC02111121\r\n
响应: TWSC02111121\r\n

# 开始治疗
发送: TWZS11150301\r\n
响应: TWZS11150301\r\n
```

## 🔍 故障排查

### **WebSocket模式问题**
```bash
# 检查网络连接
ping *************

# 检查WebSocket服务
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" \
  http://localhost:8081/hardware
```

### **串口模式问题**
```bash
# 检查串口设备
# Windows
mode

# Linux
ls /dev/ttyUSB* /dev/ttyACM*

# 检查串口占用
netstat -an | findstr :COM3
```

### **通用问题**
1. **连接失败**: 检查配置参数和设备状态
2. **指令超时**: 调整timeout参数
3. **解析错误**: 检查指令格式和响应数据

## 🛠️ 开发调试

### **启用调试日志**
```yaml
logging:
  level:
    com.Bone.BoneSys.service.WebSocketHardwareService: DEBUG
    com.Bone.BoneSys.service.SerialCommunicationService: DEBUG
    com.Bone.BoneSys.service.HardwareCommandParser: DEBUG
```

### **测试工具**
```java
// 测试WebSocket连接
@Test
void testWebSocketConnection() {
    assertTrue(webSocketHardwareService.isConnected());
}

// 测试串口连接
@Test
void testSerialConnection() {
    assertTrue(serialCommunicationService.testConnection());
}
```

## 📈 性能优化

### **连接池管理**
- WebSocket: 保持长连接，自动重连
- 串口: 连接复用，避免频繁开关

### **指令缓存**
- 缓存常用查询结果
- 批量处理指令请求
- 异步处理非关键指令

### **错误恢复**
- 自动重试机制
- 降级处理策略
- 连接状态监控

---

## ✅ 系统优势

- ✅ **灵活切换**: 支持两种通信模式
- ✅ **统一接口**: 业务代码无需关心底层实现
- ✅ **自动重连**: 网络中断自动恢复
- ✅ **错误处理**: 完善的异常处理机制
- ✅ **易于配置**: 简单的配置文件切换
- ✅ **调试友好**: 详细的日志和测试工具

现在系统支持灵活的硬件通信模式切换，满足不同部署场景的需求！🎯
