package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗头信息DTO
 * 用于封装从硬件查询到的治疗头数据
 */
public class TreatmentHeadInfo {
    
    private int headNumber;      // 治疗头编号
    private int batteryLevel;    // 电量百分比
    private int usageCount;      // 使用次数
    private int slotNumber;      // 槽编号 (1-10)
    private String status;       // 状态
    private String compartmentType; // 仓位类型 (SHALLOW/DEEP)
    private int failureCount;    // 失败次数（仅用于异常状态的治疗头）
    
    public TreatmentHeadInfo() {}
    
    public TreatmentHeadInfo(int headNumber, int batteryLevel, String status, String compartmentType) {
        this.headNumber = headNumber;
        this.batteryLevel = batteryLevel;
        this.status = status;
        this.compartmentType = compartmentType;
        // 根据治疗头编号确定仓位类型和槽号
        if (headNumber >= 1 && headNumber <= 10) {
            this.compartmentType = "SHALLOW";
            this.slotNumber = headNumber; // 浅部：槽号1-10
        } else if (headNumber >= 11 && headNumber <= 20) {
            this.compartmentType = "DEEP";
            this.slotNumber = headNumber - 10; // 深部：槽号1-10 (11->1, 12->2, ..., 20->10)
        } else {
            this.slotNumber = 0; // 无效编号
        }
        this.usageCount = 0; // 默认使用次数为0
    }
    
    public TreatmentHeadInfo(int headNumber, int batteryLevel, int usageCount, int slotNumber) {
        this.headNumber = headNumber;
        this.batteryLevel = batteryLevel;
        this.usageCount = usageCount;
        this.slotNumber = slotNumber;
        // 根据电量设置状态
        if (batteryLevel == 100) {
            this.status = "CHARGED";
        } else if (batteryLevel >= 60) {
            this.status = "CHARGING";
        } else {
            this.status = "LOW_BATTERY";
        }
        this.compartmentType = headNumber <= 10 ? "SHALLOW" : "DEEP";
    }
    
    // Getters and Setters
    public int getHeadNumber() {
        return headNumber;
    }
    
    public void setHeadNumber(int headNumber) {
        this.headNumber = headNumber;
    }
    
    public int getBatteryLevel() {
        return batteryLevel;
    }
    
    public void setBatteryLevel(int batteryLevel) {
        this.batteryLevel = batteryLevel;
    }
    
    public int getUsageCount() {
        return usageCount;
    }
    
    public void setUsageCount(int usageCount) {
        this.usageCount = usageCount;
    }
    
    public int getSlotNumber() {
        return slotNumber;
    }
    
    public void setSlotNumber(int slotNumber) {
        this.slotNumber = slotNumber;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getCompartmentType() {
        return compartmentType;
    }
    
    public void setCompartmentType(String compartmentType) {
        this.compartmentType = compartmentType;
    }

    public int getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }
    
    /**
     * 判断是否为浅部治疗头
     */
    public boolean isShallowCompartment() {
        return "SHALLOW".equals(compartmentType);
    }

    /**
     * 判断是否为深部治疗头
     */
    public boolean isDeepCompartment() {
        return "DEEP".equals(compartmentType);
    }

    /**
     * 获取仓位描述
     */
    public String getCompartmentDescription() {
        if (isShallowCompartment()) {
            return "上层浅部";
        } else if (isDeepCompartment()) {
            return "下层深部";
        } else {
            return "未知仓位";
        }
    }

    /**
     * 获取完整的槽位描述
     */
    public String getFullSlotDescription() {
        return getCompartmentDescription() + " - 槽位" + slotNumber;
    }

    @Override
    public String toString() {
        if ("ABNORMAL".equals(status)) {
            return String.format("TreatmentHeadInfo{headNumber=%d, batteryLevel=%d%%, usageCount=%d, slotNumber=%d, status='%s', compartmentType='%s', failureCount=%d, description='%s'}",
                               headNumber, batteryLevel, usageCount, slotNumber, status, compartmentType, failureCount, getFullSlotDescription());
        } else {
            return String.format("TreatmentHeadInfo{headNumber=%d, batteryLevel=%d%%, usageCount=%d, slotNumber=%d, status='%s', compartmentType='%s', description='%s'}",
                               headNumber, batteryLevel, usageCount, slotNumber, status, compartmentType, getFullSlotDescription());
        }
    }
}