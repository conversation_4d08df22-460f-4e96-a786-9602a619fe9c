package com.Bone.BoneSys.integration;

import com.Bone.BoneSys.service.*;
import com.Bone.BoneSys.websocket.NotificationWebSocketHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 系统集成测试
 * 验证所有新增功能的正常工作
 */
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SystemIntegrationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemIntegrationTest.class);
    
    @Autowired
    private TreatmentHeadQuerySchedulerService querySchedulerService;
    
    @Autowired
    private TreatmentHeadAbnormalDetectionService abnormalDetectionService;
    
    @Autowired
    private LocalTreatmentService localTreatmentService;
    
    @Autowired
    private RemoteTreatmentService remoteTreatmentService;
    
    @Autowired
    private TreatmentCompletionMonitorService completionMonitorService;
    
    @Autowired
    private NotificationWebSocketHandler notificationHandler;
    
    @Test
    @Order(1)
    public void testTreatmentHeadQueryScheduler() {
        logger.info("测试治疗头查询调度服务...");
        
        try {
            // 测试获取查询状态
            String statusInfo = querySchedulerService.getQueryStatusInfo();
            assertNotNull(statusInfo);
            logger.info("查询状态信息: {}", statusInfo);
            
            // 测试手动触发查询
            querySchedulerService.triggerStartupQuery();
            querySchedulerService.triggerEnterRecordQuery();
            querySchedulerService.triggerCreatePatientQuery();
            
            // 测试立即执行查询
            try {
                querySchedulerService.executeImmediateQuery("集成测试");
                logger.info("立即查询执行成功");
            } catch (Exception e) {
                logger.warn("立即查询执行失败（可能是硬件未连接）: {}", e.getMessage());
            }
            
            // 测试状态重置
            querySchedulerService.resetQueryStatus();
            
            logger.info("✅ 治疗头查询调度服务测试通过");
            
        } catch (Exception e) {
            logger.error("❌ 治疗头查询调度服务测试失败", e);
            fail("治疗头查询调度服务测试失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(2)
    public void testAbnormalDetectionService() {
        logger.info("测试异常检测服务...");
        
        try {
            // 测试获取异常状态统计
            Map<String, Object> statistics = abnormalDetectionService.getAbnormalStatusStatistics();
            assertNotNull(statistics);
            logger.info("异常状态统计: {}", statistics);
            
            // 测试重置异常状态
            abnormalDetectionService.resetAbnormalStatus(1);
            abnormalDetectionService.clearAllAbnormalStatus();
            
            logger.info("✅ 异常检测服务测试通过");
            
        } catch (Exception e) {
            logger.error("❌ 异常检测服务测试失败", e);
            fail("异常检测服务测试失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(3)
    public void testLocalTreatmentService() {
        logger.info("测试本地治疗服务...");
        
        try {
            List<Integer> testHeadNumbers = Arrays.asList(1, 2, 3);
            
            // 测试治疗头充足性检查
            boolean available = localTreatmentService.checkTreatmentHeadAvailability(testHeadNumbers);
            logger.info("治疗头充足性检查结果: {}", available);
            
            // 测试点亮治疗头指示灯
            try {
                boolean lightUpSuccess = localTreatmentService.lightUpRecommendedHeads(testHeadNumbers);
                logger.info("点亮治疗头指示灯结果: {}", lightUpSuccess);
            } catch (Exception e) {
                logger.warn("点亮治疗头指示灯失败（可能是硬件未连接）: {}", e.getMessage());
            }
            
            // 测试本地治疗执行
            LocalTreatmentService.LocalTreatmentRequest request = new LocalTreatmentService.LocalTreatmentRequest();
            request.setRecommendedHeadNumbers(testHeadNumbers);
            request.setDuration(10);
            request.setIntensity(50);
            request.setFrequency(0); // 100Hz
            
            try {
                LocalTreatmentService.LocalTreatmentResponse response = localTreatmentService.executeLocalTreatment(request);
                assertNotNull(response);
                logger.info("本地治疗执行结果: 成功={}, 失败={}", response.getSuccessfulHeads(), response.getFailedHeads());
            } catch (Exception e) {
                logger.warn("本地治疗执行失败（可能是硬件未连接）: {}", e.getMessage());
            }
            
            logger.info("✅ 本地治疗服务测试通过");
            
        } catch (Exception e) {
            logger.error("❌ 本地治疗服务测试失败", e);
            fail("本地治疗服务测试失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(4)
    public void testRemoteTreatmentService() {
        logger.info("测试远端治疗服务...");
        
        try {
            List<Integer> testHeadNumbers = Arrays.asList(11, 12, 13);
            
            // 测试治疗头充足性检查
            boolean available = remoteTreatmentService.checkTreatmentHeadAvailability(testHeadNumbers);
            logger.info("治疗头充足性检查结果: {}", available);
            
            // 测试点亮治疗头指示灯
            try {
                boolean lightUpSuccess = remoteTreatmentService.lightUpRecommendedHeads(testHeadNumbers);
                logger.info("点亮治疗头指示灯结果: {}", lightUpSuccess);
            } catch (Exception e) {
                logger.warn("点亮治疗头指示灯失败（可能是硬件未连接）: {}", e.getMessage());
            }
            
            // 测试远端治疗执行
            RemoteTreatmentService.RemoteTreatmentRequest request = new RemoteTreatmentService.RemoteTreatmentRequest();
            request.setRecommendedHeadNumbers(testHeadNumbers);
            request.setDuration(15);
            request.setIntensity(60);
            request.setFrequency(1000); // 1000Hz
            
            try {
                RemoteTreatmentService.RemoteTreatmentResponse response = remoteTreatmentService.executeRemoteTreatment(request);
                assertNotNull(response);
                logger.info("远端治疗执行结果: 成功={}, 失败={}", response.getSuccessfulHeads(), response.getFailedHeads());
            } catch (Exception e) {
                logger.warn("远端治疗执行失败（可能是硬件未连接）: {}", e.getMessage());
            }
            
            logger.info("✅ 远端治疗服务测试通过");
            
        } catch (Exception e) {
            logger.error("❌ 远端治疗服务测试失败", e);
            fail("远端治疗服务测试失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(5)
    public void testTreatmentCompletionMonitor() {
        logger.info("测试治疗完成监控服务...");
        
        try {
            // 测试手动触发检查
            completionMonitorService.triggerManualCheck();
            
            // 测试获取监控统计
            Map<String, Object> statistics = completionMonitorService.getMonitoringStatistics();
            assertNotNull(statistics);
            logger.info("监控统计信息: {}", statistics);
            
            // 测试重置状态
            completionMonitorService.resetPatientCompletionStatus(1L);
            completionMonitorService.resetPickupStatus("上仓(浅部)", Arrays.asList(1, 2, 3));
            completionMonitorService.clearAllCompletionStatus();
            
            logger.info("✅ 治疗完成监控服务测试通过");
            
        } catch (Exception e) {
            logger.error("❌ 治疗完成监控服务测试失败", e);
            fail("治疗完成监控服务测试失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(6)
    public void testWebSocketNotifications() {
        logger.info("测试WebSocket通知系统...");
        
        try {
            // 测试各种通知类型
            notificationHandler.sendTreatmentCompletedNotification("测试患者");
            notificationHandler.sendPickupReminderNotification(Arrays.asList(1, 2, 3));
            notificationHandler.sendReinsertHeadNotification(1, "上仓(浅部)", 1);
            notificationHandler.sendResetHeadNotification(1, "上仓(浅部)", 1, 6);
            
            logger.info("✅ WebSocket通知系统测试通过");
            
        } catch (Exception e) {
            logger.error("❌ WebSocket通知系统测试失败", e);
            fail("WebSocket通知系统测试失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(7)
    public void testSystemIntegration() {
        logger.info("测试系统整体集成...");
        
        try {
            // 模拟完整的治疗流程
            logger.info("1. 触发开机查询...");
            querySchedulerService.triggerStartupQuery();
            
            logger.info("2. 检查治疗头状态...");
            Map<String, Object> abnormalStats = abnormalDetectionService.getAbnormalStatusStatistics();
            logger.info("异常治疗头数量: {}", abnormalStats.get("abnormalHeadCount"));
            
            logger.info("3. 执行治疗完成检查...");
            completionMonitorService.triggerManualCheck();
            
            logger.info("4. 获取查询状态...");
            String queryStatus = querySchedulerService.getQueryStatusInfo();
            logger.info("查询状态: {}", queryStatus);
            
            logger.info("✅ 系统整体集成测试通过");
            
        } catch (Exception e) {
            logger.error("❌ 系统整体集成测试失败", e);
            fail("系统整体集成测试失败: " + e.getMessage());
        }
    }
}
