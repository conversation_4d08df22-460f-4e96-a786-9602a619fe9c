@echo off
chcp 65001 > nul
echo ========================================
echo FREEBONE医疗系统 - API测试脚本
echo ========================================
echo.

echo [1/3] 测试主界面API...
echo 请求: GET /api/dashboard/main
curl -s http://localhost:8080/api/dashboard/main | jq .
echo.

echo [2/3] 测试治疗头管理API...
echo 请求: GET /api/hardware/heads?page=1^&size=20
curl -s "http://localhost:8080/api/hardware/heads?page=1&size=20" | jq .
echo.

echo [3/3] 测试治疗头同步API...
echo 请求: POST /api/hardware/sync-treatment-heads
curl -s -X POST http://localhost:8080/api/hardware/sync-treatment-heads | jq .
echo.

echo ========================================
echo API测试完成！
echo ========================================
echo.
echo 检查要点：
echo 1. 主界面API应返回 availableHeads: 9 (在治疗仓中的数量)
echo 2. 治疗头管理API应返回 totalRecords: 20 (所有治疗头)
echo 3. 同步API应成功返回硬件数据
echo.
pause
