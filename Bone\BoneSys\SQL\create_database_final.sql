-- FREEBONE 医疗系统 - 最终版数据库创建脚本（与当前实体模型一致）
-- 兼容 MySQL 5.7+（不使用不可移植的 CHECK/VISIBLE 等语法）

DROP DATABASE IF EXISTS bonesys;
CREATE DATABASE bonesys CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE bonesys;

-- 表：users（与实体 User 一致）
CREATE TABLE users (
  id INT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  factory_password_hash VARCHAR(255) NOT NULL,
  user_password_hash VARCHAR(255) NOT NULL,
  last_updated_at DATETIME NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 表：patients（与实体 Patient 一致）
CREATE TABLE patients (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  patient_card_id VARCHAR(50) NOT NULL UNIQUE,
  name VARCHA<PERSON>(100) NOT NULL,
  gender VARCHAR(10) NULL,
  age VARCHAR(10) NULL,
  contact_info VARCHAR(255) NULL,
  created_at DATETIME NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 表：records（与实体 Record 一致）
CREATE TABLE records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  record_number VARCHAR(50) NOT NULL UNIQUE,
  patient_id BIGINT NOT NULL,
  diagnosis_description TEXT NULL,
  sessions_completed_count INT NOT NULL DEFAULT 0,
  created_at DATE NOT NULL,
  KEY idx_records_patient_id (patient_id),
  CONSTRAINT fk_records_patient FOREIGN KEY (patient_id) REFERENCES patients(id)
    ON UPDATE RESTRICT ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 表：processes（与实体 Process 一致）
CREATE TABLE processes (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  record_id BIGINT NOT NULL,
  treatment_mode VARCHAR(20) NOT NULL, -- EnumType.STRING: ON_SITE/TAKE_AWAY
  status VARCHAR(20) NOT NULL,         -- EnumType.STRING: IN_PROGRESS/COMPLETED/CANCELLED
  start_time DATETIME NOT NULL,
  end_time DATETIME NULL,
  remote_counted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '远端治疗是否已计数档案完成次数，防止重复统计',
  KEY idx_processes_record_id (record_id),
  CONSTRAINT fk_processes_record FOREIGN KEY (record_id) REFERENCES records(id)
    ON UPDATE RESTRICT ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 表：treatment_heads（与实体 TreatmentHead 一致）
CREATE TABLE treatment_heads (
  head_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  head_number INT NOT NULL UNIQUE,
  slot_number INT NULL,
  compartment_type VARCHAR(10) NULL,   -- "上仓(浅部)" / "下仓(深部)"
  light_color INT NOT NULL DEFAULT 0,  -- 0=关 1=橙 2=蓝 3=绿
  realtime_status VARCHAR(20) NOT NULL, -- CHARGING/CHARGED/TREATING/ABNORMAL
  battery_level INT NULL,
  total_usage_count INT NOT NULL DEFAULT 0,
  total_usage_minutes INT NOT NULL DEFAULT 0,
  max_usage_count INT NOT NULL DEFAULT 500
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 表：treatment_parameter_presets（与实体 TreatmentParameterPreset 一致）
CREATE TABLE treatment_parameter_presets (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  preset_name VARCHAR(100) NOT NULL,
  body_part VARCHAR(50) NOT NULL,
  default_duration INT NOT NULL,
  default_intensity INT NOT NULL,
  default_frequency INT NOT NULL,
  patch_type VARCHAR(20) NOT NULL,     -- SHALLOW/DEEP
  recommended_count INT NOT NULL,
  min_patch_count INT NOT NULL,
  max_patch_count INT NOT NULL,
  is_default TINYINT(1) NOT NULL DEFAULT 0,
  is_active TINYINT(1) NOT NULL DEFAULT 1,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  KEY idx_presets_body_part (body_part)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 表：treatment_details（与实体 TreatmentDetail 一致）
CREATE TABLE treatment_details (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  process_id BIGINT NOT NULL,
  head_number_used INT NOT NULL,
  body_part VARCHAR(50) NOT NULL,
  duration INT NOT NULL,
  intensity DECIMAL(5,2) NOT NULL,
  frequency INT NOT NULL,
  patch_type VARCHAR(20) NOT NULL,     -- SHALLOW/DEEP
  patch_quantity INT NOT NULL,         -- 实体限制1-4（由应用层校验）
  status VARCHAR(20) NOT NULL,         -- EnumType.STRING: TREATING/COMPLETED/AWAITING_RETURN/RETURNED/TERMINATED
  KEY idx_details_process_id (process_id),
  KEY idx_details_head_number_used (head_number_used),
  CONSTRAINT fk_details_process FOREIGN KEY (process_id) REFERENCES processes(id)
    ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_details_head_number FOREIGN KEY (head_number_used) REFERENCES treatment_heads(head_number)
    ON UPDATE RESTRICT ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 表：body_part_stats（与实体 BodyPartStat 一致）
CREATE TABLE body_part_stats (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  record_id BIGINT NOT NULL,
  body_part VARCHAR(50) NOT NULL,
  total_usage_count INT NOT NULL,
  total_duration_minutes INT NOT NULL,
  UNIQUE KEY uk_record_part (record_id, body_part),
  KEY idx_stats_record_id (record_id),
  CONSTRAINT fk_stats_record FOREIGN KEY (record_id) REFERENCES records(id)
    ON UPDATE RESTRICT ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 完成

