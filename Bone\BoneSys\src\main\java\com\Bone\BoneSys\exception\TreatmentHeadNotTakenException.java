package com.Bone.BoneSys.exception;

/**
 * 治疗头未取出异常
 * 当治疗头还在治疗仓中（未取出贴到身上）时抛出此异常
 */
public class TreatmentHeadNotTakenException extends SerialCommunicationException {
    
    private final int headNumber;
    
    public TreatmentHeadNotTakenException(int headNumber) {
        super(String.format("治疗头%d还在治疗仓中，请取出并贴到身上后重试", headNumber));
        this.headNumber = headNumber;
    }
    
    public TreatmentHeadNotTakenException(int headNumber, String message) {
        super(message);
        this.headNumber = headNumber;
    }
    
    public int getHeadNumber() {
        return headNumber;
    }
    
    /**
     * 获取用户友好的错误信息
     */
    public String getUserFriendlyMessage() {
        return String.format("治疗头%d需要从治疗仓取出并贴到治疗部位后才能启动治疗", headNumber);
    }
    
    /**
     * 获取错误代码，用于前端识别这种特定的错误类型
     */
    public String getErrorCode() {
        return "TREATMENT_HEAD_NOT_TAKEN";
    }
} 