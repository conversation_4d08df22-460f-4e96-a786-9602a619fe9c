# 🧪 完整治疗流程测试指南

## 📋 测试目标

在没有真实治疗头硬件的情况下，测试完整的治疗业务逻辑，包括：
- 患者建档
- 治疗参数设置
- 治疗头启动和控制
- 治疗进程管理
- 治疗完成和归还
- 弹窗通知系统

## 🔧 环境配置

### 1. 启用Mock模式

确保 `application-dev.properties` 中的配置：
```properties
# 硬件通信模式配置 (websocket/serial/mock)
hardware.communication.mode=mock

# 硬件模拟器配置（开发环境启用）
hardware.simulator.enabled=true
```

### 2. 启动服务

```bash
cd Bone/BoneSys
./gradlew bootRun --args='--spring.profiles.active=dev'
```

## 🧪 测试步骤

### 步骤1：验证Mock硬件连接

```bash
# 检查硬件连接状态
curl "http://localhost:8080/api/test/hardware/connection-status"

# 预期响应：
# {
#   "code": 200,
#   "data": {
#     "connected": true,
#     "communicationType": "mock"
#   }
# }
```

### 步骤2：测试治疗头同步

```bash
# 同步治疗头数据
curl -X POST "http://localhost:8080/api/test/hardware/sync-treatment-heads"

# 预期响应：同步20个治疗头的数据
```

### 步骤3：测试硬件基础功能

```bash
# 测试指示灯控制
curl -X POST "http://localhost:8080/api/test/hardware/test-lights?headNumbers=1,2,3&turnOn=true"

# 测试治疗启动
curl -X POST "http://localhost:8080/api/test/hardware/start-treatment?headNumber=1&duration=20&intensity=45&frequency=1000"

# 测试治疗停止
curl -X POST "http://localhost:8080/api/test/hardware/stop-treatment?headNumber=1"

# 批量测试治疗流程
curl -X POST "http://localhost:8080/api/test/hardware/test-treatment-flow?headNumbers=1,2,3"
```

### 步骤4：完整业务流程测试

#### 4.1 创建患者档案

```bash
# 创建患者
curl -X POST "http://localhost:8080/api/patients" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试患者",
    "patientCardId": "TEST001",
    "gender": "男",
    "age": 35,
    "phone": "13800138000"
  }'

# 创建档案
curl -X POST "http://localhost:8080/api/records" \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": 1,
    "diagnosis": "肩颈疼痛",
    "symptoms": "肩膀酸痛，颈部僵硬"
  }'
```

#### 4.2 开始治疗进程

```bash
# 开始本地治疗
curl -X POST "http://localhost:8080/api/treatment-process/start" \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": 1,
    "treatmentMode": "ON_SITE",
    "bodyParts": [
      {
        "name": "肩颈部",
        "parameters": {
          "duration": 20,
          "intensity": 45,
          "frequency": 1000,
          "patchType": "SHALLOW",
          "count": 2
        }
      }
    ]
  }'
```

#### 4.3 监控治疗进程

```bash
# 查看进程状态
curl "http://localhost:8080/api/processes/1/realtime"

# 查看进程管理页面
curl "http://localhost:8080/api/processes"
```

#### 4.4 完成治疗

```bash
# 完成指定部位的治疗
curl -X POST "http://localhost:8080/api/treatment-process/detail/1/complete"

# 或者完成整个进程
curl -X POST "http://localhost:8080/api/treatment-process/1/complete"
```

#### 4.5 测试治疗完成通知

```bash
# 手动触发治疗完成检查
curl -X POST "http://localhost:8080/api/debug/test/trigger-completion-check"

# 或者直接测试通知
curl -X POST "http://localhost:8080/api/test/notifications/treatment-completed?patientName=测试患者"
```

### 步骤5：测试取走治疗流程

```bash
# 开始取走治疗
curl -X POST "http://localhost:8080/api/treatment-process/start" \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": 1,
    "treatmentMode": "TAKE_AWAY",
    "bodyParts": [
      {
        "name": "腰背部",
        "parameters": {
          "duration": 30,
          "intensity": 60,
          "frequency": 100,
          "patchType": "DEEP",
          "count": 2
        }
      }
    ]
  }'

# 确认治疗头归还
curl -X POST "http://localhost:8080/api/treatment-process/detail/2/return"
```

## 🔍 验证要点

### 1. 硬件通信验证
- [ ] Mock适配器正常工作
- [ ] 治疗头同步成功
- [ ] 指示灯控制正常
- [ ] 治疗启动/停止正常

### 2. 业务逻辑验证
- [ ] 患者档案创建成功
- [ ] 治疗进程启动成功
- [ ] 治疗参数正确传递
- [ ] 状态变更正确记录

### 3. 数据库验证
```sql
-- 检查治疗详情状态
SELECT td.id, td.body_part, td.status, p.treatment_mode, pt.name 
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
JOIN records r ON p.record_id = r.id
JOIN patients pt ON r.patient_id = pt.id;

-- 检查部位统计
SELECT * FROM body_part_stats;
```

### 4. 通知系统验证
- [ ] WebSocket连接正常
- [ ] 治疗完成通知触发
- [ ] 待取回通知触发
- [ ] 前端弹窗正常显示

## 🚨 故障排除

### 常见问题

1. **Mock适配器未启用**
   - 检查配置：`hardware.communication.mode=mock`
   - 重启应用

2. **治疗头同步失败**
   - 检查Mock适配器日志
   - 验证TRZI指令响应

3. **治疗启动失败**
   - 检查治疗头状态
   - 验证参数格式

4. **通知不显示**
   - 检查WebSocket连接
   - 验证前端路由

### 调试命令

```bash
# 查看硬件状态
curl "http://localhost:8080/api/test/hardware/database-heads"

# 重置所有治疗头
curl -X POST "http://localhost:8080/api/test/hardware/reset-all-heads"

# 获取模拟状态
curl "http://localhost:8080/api/test/hardware/mock/head-state/1"

# 设置连接状态
curl -X POST "http://localhost:8080/api/test/hardware/mock/set-connection?connected=false"
```

## 📊 测试报告模板

### 测试结果记录

| 测试项目 | 状态 | 备注 |
|---------|------|------|
| Mock硬件连接 | ✅/❌ | |
| 治疗头同步 | ✅/❌ | |
| 指示灯控制 | ✅/❌ | |
| 治疗启动 | ✅/❌ | |
| 治疗停止 | ✅/❌ | |
| 患者建档 | ✅/❌ | |
| 治疗进程 | ✅/❌ | |
| 状态更新 | ✅/❌ | |
| 完成通知 | ✅/❌ | |
| 数据统计 | ✅/❌ | |

### 性能指标

- 硬件响应时间：< 200ms
- 治疗启动成功率：> 95%
- 数据同步准确率：100%
- 通知触发及时性：< 60s

通过这套测试方案，您可以在没有真实硬件的情况下完整验证整个治疗系统的业务逻辑。
