<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="downloading-modal">
        <!-- 关闭按钮 -->
        <img
          class="close-btn"
          :src="closeIcon"
          alt="关闭"
          @click="closeModal"
        />
        <!-- 加载动画 -->
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 导入图片
import downloadingBackground from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/弹窗参数下载中.png'
import closeIcon from '@/assets/images/交互界面/参数设置/×.png'

// Props
const props = defineProps<{
  visible: boolean
}>()

// Emits
const emit = defineEmits<{
  close: []
  cancel: [] // 用户手动关闭
}>()

// 方法
const handleOverlayClick = () => {
  handleManualClose()
}

const closeModal = () => {
  handleManualClose()
}

const handleManualClose = () => {
  emit('cancel') // 用户手动关闭，发送取消信号
  emit('close')
}
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background: transparent;
  overflow: hidden;
  position: absolute;
  top: 147px;
  left: 658px;
}

/* 参数下载中弹窗样式 */
.downloading-modal {
  height: 752px;
  background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/弹窗参数下载中.png') 0px 0px no-repeat;
  background-size: 631px 774px;
  width: 612px;
  position: relative;
}

/* 关闭按钮样式 */
.close-btn {
  width: 60px;
  height: 60px;
  margin: 46px 0 10px 510px;
  cursor: pointer;
  position: absolute;
  transition: transform 0.2s ease;
}

.close-btn:hover {
  transform: scale(1.1);
}

/* 加载动画容器 */
.loading-spinner {
  position: absolute;
  top: calc(50% + 170px);
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

/* 转圈动画 */
.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #5cd4c8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 转圈动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 