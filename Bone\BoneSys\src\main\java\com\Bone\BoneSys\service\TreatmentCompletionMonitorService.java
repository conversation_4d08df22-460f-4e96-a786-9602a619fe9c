package com.Bone.BoneSys.service;

import com.Bone.BoneSys.entity.Patient;
import com.Bone.BoneSys.entity.Record;
import com.Bone.BoneSys.entity.TreatmentDetail;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.repository.PatientRepository;
import com.Bone.BoneSys.repository.ProcessRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import com.Bone.BoneSys.repository.TreatmentDetailRepository;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import com.Bone.BoneSys.websocket.NotificationWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 治疗完成监控服务
 * 负责监控患者档案的治疗进程状态，检测治疗完成情况并发送相应通知
 */
@Service
public class TreatmentCompletionMonitorService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentCompletionMonitorService.class);
    
    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private RecordRepository recordRepository;

    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;

    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private NotificationWebSocketHandler notificationHandler;
    
    // 已完成治疗的患者记录（避免重复通知）
    private final Map<Long, LocalDateTime> completedPatients = new ConcurrentHashMap<>();
    
    // 待取回治疗头的记录（避免重复通知）
    private final Map<String, LocalDateTime> pendingPickupHeads = new ConcurrentHashMap<>();
    
    /**
     * 定时检查治疗完成状态 - 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 60秒执行一次
    public void checkTreatmentCompletion() {
        try {
            logger.debug("开始检查治疗完成状态...");
            
            // 检查本地治疗完成
            checkLocalTreatmentCompletion();
            
            // 检查远端治疗头待取回
            checkRemoteTreatmentPickup();
            
        } catch (Exception e) {
            logger.error("检查治疗完成状态失败", e);
        }
    }
    
    /**
     * 检查本地治疗完成情况
     * 根据文档要求：只有当此患者此次档案的全部进程对应的treatment_details表的status均为COMPLETED或者RETURNED或者TERMINATED的时候，才会给前端弹出"xxx全部治疗已完成"
     */
    private void checkLocalTreatmentCompletion() {
        try {
            // 查找所有有治疗记录的患者
            List<Patient> activePatients = patientRepository.findAll();

            for (Patient patient : activePatients) {
                // 检查该患者是否已经通知过
                String patientKey = "patient_" + patient.getId();
                if (completedPatients.containsKey(patient.getId())) {
                    continue;
                }

                // 获取该患者的最新档案
                List<Record> records = recordRepository.findByPatientIdOrderByCreatedAtDesc(patient.getId());
                if (records.isEmpty()) {
                    continue;
                }

                Record latestRecord = records.get(0);

                // 获取该档案对应的所有本地治疗进程
                List<Process> processes = processRepository.findByRecordId(latestRecord.getId());
                List<Process> localProcesses = processes.stream()
                    .filter(p -> p.getTreatmentMode() == TreatmentMode.ON_SITE)
                    .collect(Collectors.toList());

                if (localProcesses.isEmpty()) {
                    continue; // 没有本地治疗进程
                }

                // 检查该档案的所有本地治疗进程对应的treatment_details是否都处于最终状态
                boolean allLocalDetailsFinished = true;

                for (Process process : localProcesses) {
                    List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(process.getId());

                    for (TreatmentDetail detail : details) {
                        if (detail.getStatus() != TreatmentDetailStatus.COMPLETED &&
                            detail.getStatus() != TreatmentDetailStatus.RETURNED &&
                            detail.getStatus() != TreatmentDetailStatus.TERMINATED) {
                            allLocalDetailsFinished = false;
                            break;
                        }
                    }

                    if (!allLocalDetailsFinished) {
                        break;
                    }
                }

                if (allLocalDetailsFinished) {
                    // 发送治疗完成通知
                    sendTreatmentCompletedNotification(patient);

                    // 记录已通知的患者
                    completedPatients.put(patient.getId(), LocalDateTime.now());

                    logger.info("患者 {} 的本地治疗档案已完成，已发送通知", patient.getName());
                }
            }

        } catch (Exception e) {
            logger.error("检查本地治疗完成情况失败", e);
        }
    }
    
    /**
     * 检查远端治疗头待取回情况
     * 根据文档要求：只有当此患者此次档案的全部进程对应的treatment_details表的status均为AWAITING_RETURN或者RETURNED或者TERMINATED，才会给前端弹出"xx号治疗头待取回"
     */
    private void checkRemoteTreatmentPickup() {
        try {
            // 查找所有有远端治疗记录的患者
            List<Patient> activePatients = patientRepository.findAll();

            for (Patient patient : activePatients) {
                // 检查该患者是否已经通知过
                String patientKey = "patient_" + patient.getId();
                if (pendingPickupHeads.containsKey(patientKey)) {
                    continue;
                }

                // 获取该患者的最新档案
                List<Record> records = recordRepository.findByPatientIdOrderByCreatedAtDesc(patient.getId());
                if (records.isEmpty()) {
                    continue;
                }

                Record latestRecord = records.get(0);

                // 获取该档案对应的所有远端治疗进程
                List<Process> processes = processRepository.findByRecordId(latestRecord.getId());
                List<Process> remoteProcesses = processes.stream()
                    .filter(p -> p.getTreatmentMode() == TreatmentMode.TAKE_AWAY)
                    .collect(Collectors.toList());

                if (remoteProcesses.isEmpty()) {
                    continue; // 没有远端治疗进程
                }

                // 检查该档案的所有远端治疗进程对应的treatment_details是否都处于最终状态
                boolean allRemoteDetailsFinished = true;
                List<Integer> awaitingReturnHeadNumbers = new ArrayList<>();

                for (Process process : remoteProcesses) {
                    List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(process.getId());

                    for (TreatmentDetail detail : details) {
                        if (detail.getStatus() != TreatmentDetailStatus.AWAITING_RETURN &&
                            detail.getStatus() != TreatmentDetailStatus.RETURNED &&
                            detail.getStatus() != TreatmentDetailStatus.TERMINATED) {
                            allRemoteDetailsFinished = false;
                            break;
                        }

                        // 收集待取回的治疗头编号
                        if (detail.getStatus() == TreatmentDetailStatus.AWAITING_RETURN &&
                            detail.getHeadNumberUsed() != null) {
                            awaitingReturnHeadNumbers.add(detail.getHeadNumberUsed());
                        }
                    }

                    if (!allRemoteDetailsFinished) {
                        break;
                    }
                }

                if (allRemoteDetailsFinished && !awaitingReturnHeadNumbers.isEmpty()) {
                    // 去重并排序
                    List<Integer> uniqueHeadNumbers = awaitingReturnHeadNumbers.stream()
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());

                    // 发送待取回通知
                    sendPickupReminderNotification(uniqueHeadNumbers, "");

                    // 记录已通知的患者
                    pendingPickupHeads.put(patientKey, LocalDateTime.now());

                    logger.info("患者 {} 的远端治疗档案已完成，治疗头 {} 待取回，已发送通知",
                               patient.getName(), uniqueHeadNumbers);
                }
            }

        } catch (Exception e) {
            logger.error("检查远端治疗头待取回情况失败", e);
        }
    }

    /**
     * 检查患者的远端治疗是否完成
     * 当该档案最近对应的远端治疗进程全部变成已完成状态时，返回true
     */
    private boolean isPatientRemoteTreatmentCompleted(Patient patient) {
        try {
            // 获取该患者的最新档案
            List<Record> records = recordRepository.findByPatientIdOrderByCreatedAtDesc(patient.getId());

            if (records.isEmpty()) {
                return false;
            }

            Record latestRecord = records.get(0);

            // 获取该档案对应的所有远端治疗进程
            List<Process> processes = processRepository.findByRecordId(latestRecord.getId());
            List<Process> remoteProcesses = processes.stream()
                .filter(p -> p.getTreatmentMode() == com.Bone.BoneSys.entity.enums.TreatmentMode.TAKE_AWAY)
                .collect(java.util.stream.Collectors.toList());

            if (remoteProcesses.isEmpty()) {
                return false; // 没有远端治疗进程
            }

            // 检查所有远端治疗进程是否都已完成
            for (Process process : remoteProcesses) {
                if (process.getStatus() != ProcessStatus.COMPLETED) {
                    return false; // 有未完成的远端治疗进程
                }
            }

            return true; // 所有远端治疗进程都已完成

        } catch (Exception e) {
            logger.error("检查患者 {} 远端治疗完成状态失败", patient.getId(), e);
            return false;
        }
    }

    /**
     * 获取患者最新档案的远端治疗头信息
     */
    private List<Integer> getPatientRemoteTreatmentHeads(Patient patient) {
        // 这里应该根据实际的业务逻辑获取治疗头编号
        // 暂时返回空列表，需要根据实际的数据结构实现
        return new java.util.ArrayList<>();
    }
    
    /**
     * 检查患者的所有治疗进程是否完成
     * 当该档案最近对应的进程全部变成已完成状态时，返回true
     */
    private boolean isPatientTreatmentCompleted(Patient patient) {
        try {
            // 获取该患者的最新档案
            List<Record> records = recordRepository.findByPatientIdOrderByCreatedAtDesc(patient.getId());

            if (records.isEmpty()) {
                return false; // 没有档案记录，不算完成
            }

            // 获取最新的档案
            Record latestRecord = records.get(0);

            // 获取该档案对应的所有进程
            List<Process> processes = processRepository.findByRecordId(latestRecord.getId());

            if (processes.isEmpty()) {
                return false; // 没有进程记录，不算完成
            }

            // 检查该档案的所有进程是否都已完成
            for (Process process : processes) {
                if (process.getStatus() != ProcessStatus.COMPLETED) {
                    return false; // 有未完成的进程
                }
            }

            return true; // 该档案的所有进程都已完成

        } catch (Exception e) {
            logger.error("检查患者 {} 治疗完成状态失败", patient.getId(), e);
            return false;
        }
    }
    
    /**
     * 按仓位分组治疗头
     */
    private Map<String, List<Integer>> groupHeadsByCompartment(List<TreatmentHead> heads) {
        Map<String, List<Integer>> grouped = new HashMap<>();
        
        for (TreatmentHead head : heads) {
            String compartmentType = determineCompartmentType(head.getHeadNumber());
            
            grouped.computeIfAbsent(compartmentType, k -> new ArrayList<>())
                   .add(head.getHeadNumber());
        }
        
        return grouped;
    }
    
    /**
     * 根据治疗头编号确定仓位类型
     */
    private String determineCompartmentType(int headNumber) {
        if (headNumber >= 1 && headNumber <= 10) {
            return "上仓(浅部)";
        } else if (headNumber >= 11 && headNumber <= 20) {
            return "下仓(深部)";
        } else {
            return "UNKNOWN";
        }
    }
    
    /**
     * 发送治疗完成通知
     */
    private void sendTreatmentCompletedNotification(Patient patient) {
        try {
            notificationHandler.sendTreatmentCompletedNotification(patient.getName());
            logger.info("已发送治疗完成通知: 患者={}", patient.getName());
        } catch (Exception e) {
            logger.error("发送治疗完成通知失败", e);
        }
    }
    
    /**
     * 发送待取回提醒通知
     */
    private void sendPickupReminderNotification(List<Integer> headNumbers, String compartmentType) {
        try {
            notificationHandler.sendPickupReminderNotification(headNumbers);
            logger.info("已发送待取回提醒通知: 治疗头={}, 仓位={}", headNumbers, compartmentType);
        } catch (Exception e) {
            logger.error("发送待取回提醒通知失败", e);
        }
    }
    
    /**
     * 手动触发治疗完成检查
     */
    public void triggerManualCheck() {
        logger.info("手动触发治疗完成检查");
        checkTreatmentCompletion();
    }
    
    /**
     * 重置患者完成状态（用于测试或重新检查）
     */
    public void resetPatientCompletionStatus(Long patientId) {
        completedPatients.remove(patientId);
        logger.info("患者 {} 的完成状态已重置", patientId);
    }
    
    /**
     * 重置待取回状态（用于测试或重新检查）
     */
    public void resetPickupStatus(String compartmentType, List<Integer> headNumbers) {
        String notificationKey = compartmentType + "_" + headNumbers.toString();
        pendingPickupHeads.remove(notificationKey);
        logger.info("治疗头 {} 的待取回状态已重置", headNumbers);
    }
    
    /**
     * 清除所有完成状态记录
     */
    public void clearAllCompletionStatus() {
        completedPatients.clear();
        pendingPickupHeads.clear();
        logger.info("所有完成状态记录已清除");
    }
    
    /**
     * 获取监控状态统计
     */
    public Map<String, Object> getMonitoringStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("completedPatientsCount", completedPatients.size());
        stats.put("pendingPickupGroupsCount", pendingPickupHeads.size());
        stats.put("completedPatientIds", new ArrayList<>(completedPatients.keySet()));
        stats.put("pendingPickupKeys", new ArrayList<>(pendingPickupHeads.keySet()));
        stats.put("lastCheckTime", LocalDateTime.now());
        
        return stats;
    }
}
