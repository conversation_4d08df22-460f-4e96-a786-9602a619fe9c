package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.TreatmentDetail;
import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.repository.TreatmentDetailRepository;
import com.Bone.BoneSys.service.TreatmentDetailAutoUpdateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 治疗详情自动更新控制器
 * 提供手动触发和调试接口
 */
@RestController
@RequestMapping("/api/treatment-detail-auto-update")
@CrossOrigin(origins = "*")
public class TreatmentDetailAutoUpdateController {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentDetailAutoUpdateController.class);
    
    @Autowired
    private TreatmentDetailAutoUpdateService autoUpdateService;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    /**
     * 手动触发状态更新检查
     * POST /api/treatment-detail-auto-update/trigger
     */
    @PostMapping("/trigger")
    public ApiResponse<String> triggerManualUpdate() {
        try {
            logger.info("手动触发治疗详情状态更新检查");
            autoUpdateService.triggerManualUpdate();
            return ApiResponse.success("状态更新检查已触发", "检查任务已执行");
        } catch (Exception e) {
            logger.error("手动触发状态更新检查失败", e);
            return ApiResponse.serverError("触发状态更新检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前正在治疗中的详情状态
     * GET /api/treatment-detail-auto-update/status
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getTreatmentDetailStatus() {
        try {
            List<TreatmentDetail> treatingDetails = treatmentDetailRepository
                .findByStatus(TreatmentDetailStatus.TREATING);
            
            Map<String, Object> statusInfo = new HashMap<>();
            statusInfo.put("total_treating_details", treatingDetails.size());
            
            LocalDateTime now = LocalDateTime.now();
            int expiredCount = 0;
            int nearExpiryCount = 0;
            
            for (TreatmentDetail detail : treatingDetails) {
                Process process = detail.getProcess();
                if (process != null && process.getStartTime() != null) {
                    long elapsedMinutes = ChronoUnit.MINUTES.between(process.getStartTime(), now);
                    int treatmentDurationMinutes = detail.getDuration();
                    
                    if (elapsedMinutes >= treatmentDurationMinutes) {
                        expiredCount++;
                    } else if (elapsedMinutes >= treatmentDurationMinutes - 5) { // 5分钟内到期
                        nearExpiryCount++;
                    }
                }
            }
            
            statusInfo.put("expired_details", expiredCount);
            statusInfo.put("near_expiry_details", nearExpiryCount);
            statusInfo.put("check_time", now.toString());
            
            return ApiResponse.success("状态信息获取成功", statusInfo);
            
        } catch (Exception e) {
            logger.error("获取治疗详情状态失败", e);
            return ApiResponse.serverError("获取状态信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取详细的治疗详情时间信息
     * GET /api/treatment-detail-auto-update/details
     */
    @GetMapping("/details")
    public ApiResponse<List<Map<String, Object>>> getTreatmentDetailTimeInfo() {
        try {
            List<TreatmentDetail> treatingDetails = treatmentDetailRepository
                .findByStatus(TreatmentDetailStatus.TREATING);
            
            LocalDateTime now = LocalDateTime.now();
            
            List<Map<String, Object>> detailsInfo = treatingDetails.stream()
                .map(detail -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("detail_id", detail.getId());
                    info.put("body_part", detail.getBodyPart());
                    info.put("duration_minutes", detail.getDuration());
                    info.put("status", detail.getStatus().name());
                    
                    Process process = detail.getProcess();
                    if (process != null) {
                        info.put("process_id", process.getId());
                        info.put("treatment_mode", process.getTreatmentMode().name());
                        
                        if (process.getStartTime() != null) {
                            long elapsedMinutes = ChronoUnit.MINUTES.between(process.getStartTime(), now);
                            info.put("elapsed_minutes", elapsedMinutes);
                            info.put("remaining_minutes", detail.getDuration() - elapsedMinutes);
                            info.put("start_time", process.getStartTime().toString());
                            
                            // 判断是否应该更新状态
                            boolean shouldUpdate = false;
                            if (process.getTreatmentMode().name().equals("ON_SITE")) {
                                shouldUpdate = elapsedMinutes >= detail.getDuration();
                            } else if (process.getTreatmentMode().name().equals("TAKE_AWAY")) {
                                shouldUpdate = elapsedMinutes >= (detail.getDuration() + 15); // 15分钟提醒时间
                            }
                            info.put("should_update", shouldUpdate);
                        }
                    }
                    
                    return info;
                })
                .toList();
            
            return ApiResponse.success("详细信息获取成功", detailsInfo);
            
        } catch (Exception e) {
            logger.error("获取治疗详情详细信息失败", e);
            return ApiResponse.serverError("获取详细信息失败: " + e.getMessage());
        }
    }
}
