package com.Bone.BoneSys.entity.enums;

/**
 * 治疗头指示灯颜色枚举
 */
public enum LightColor {
    /**
     * 关闭
     */
    OFF(0, "关闭"),
    
    /**
     * 橙色
     */
    ORANGE(1, "橙色"),
    
    /**
     * 蓝色
     */
    BLUE(2, "蓝色"),
    
    /**
     * 绿色
     */
    GREEN(3, "绿色");
    
    private final int code;
    private final String description;
    
    LightColor(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static LightColor fromCode(int code) {
        for (LightColor color : values()) {
            if (color.code == code) {
                return color;
            }
        }
        throw new IllegalArgumentException("未知的指示灯颜色代码: " + code);
    }
}