-- 修复槽号映射逻辑错误
-- 硬件协议明确：上下仓槽号都是1-10，没有11-20的槽号
-- 问题：数据库中下仓治疗头的槽号被错误地存储为11-20

USE bonesys;

-- 1. 查看当前槽号分布情况
SELECT 
    head_number,
    slot_number,
    compartment_type,
    CASE 
        WHEN head_number BETWEEN 1 AND 10 THEN '上仓(浅部)'
        WHEN head_number BETWEEN 11 AND 20 THEN '下仓(深部)'
        ELSE '未知'
    END as expected_compartment
FROM treatment_heads 
ORDER BY head_number;

-- 2. 重置所有槽号为NULL，让硬件同步时重新填充正确的槽号
UPDATE treatment_heads 
SET slot_number = NULL
WHERE slot_number IS NOT NULL;

-- 3. 确保compartment_type字段正确设置
UPDATE treatment_heads 
SET compartment_type = '上仓(浅部)' 
WHERE head_number BETWEEN 1 AND 10;

UPDATE treatment_heads 
SET compartment_type = '下仓(深部)' 
WHERE head_number BETWEEN 11 AND 20;

-- 4. 验证修复结果
SELECT 
    head_number,
    slot_number,
    compartment_type,
    realtime_status,
    battery_level
FROM treatment_heads 
ORDER BY head_number;

-- 5. 添加约束确保槽号在正确范围内（1-10）
-- 注意：如果已存在同名约束，需要先删除
-- ALTER TABLE treatment_heads DROP CONSTRAINT IF EXISTS chk_slot_number_range;

ALTER TABLE treatment_heads 
ADD CONSTRAINT chk_slot_number_range 
CHECK (slot_number IS NULL OR (slot_number >= 1 AND slot_number <= 10));

COMMIT;
