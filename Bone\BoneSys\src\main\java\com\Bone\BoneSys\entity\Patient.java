package com.Bone.BoneSys.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 患者实体类
 * 对应数据库表：patients
 */
@Entity
@Table(name = "patients")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = {"records"})
@ToString(exclude = {"records"})
public class Patient {
    
    /**
     * 患者ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    /**
     * 就诊卡号
     */
    @Column(name = "patient_card_id", unique = true, nullable = false, length = 50)
    private String patientCardId;
    
    /**
     * 患者姓名
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    /**
     * 性别
     */
    @Column(name = "gender", length = 10)
    private String gender;
    
    /**
     * 年龄
     */
    @Column(name = "age", length = 10)
    private String age;
    
    /**
     * 联系方式
     */
    @Column(name = "contact_info")
    private String contactInfo;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 患者的档案记录
     */
    @OneToMany(mappedBy = "patient", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Record> records;
    
    /**
     * 设置创建时间
     */
    @PrePersist
    public void prePersist() {
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
    }
}