-- 修改治疗仓槽号逻辑
-- 上下层都是1-10，分别对应浅部和深部

-- 1. 修改表结构注释
ALTER TABLE `bonesys`.`treatment_heads` 
MODIFY COLUMN `slot_number` INT NULL DEFAULT NULL COMMENT '槽位号 (上层1-10浅部, 下层1-10深部)';

-- 2. 删除旧的约束（如果存在）
-- MySQL语法：先检查约束是否存在，然后删除
SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.table_constraints
                         WHERE constraint_schema = 'bonesys'
                         AND table_name = 'treatment_heads'
                         AND constraint_name = 'chk_head_slot_mapping');

SET @sql = IF(@constraint_exists > 0,
              'ALTER TABLE `bonesys`.`treatment_heads` DROP CHECK `chk_head_slot_mapping`',
              'SELECT "Constraint does not exist" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 添加新的约束
ALTER TABLE `bonesys`.`treatment_heads`
ADD CONSTRAINT `chk_head_slot_mapping` CHECK (
  slot_number IS NULL OR slot_number BETWEEN 1 AND 10
);

-- 4. 添加仓位类型字段
ALTER TABLE `bonesys`.`treatment_heads` 
ADD COLUMN `compartment_type` VARCHAR(10) NULL DEFAULT NULL COMMENT '仓位类型: SHALLOW(浅部上层), DEEP(深部下层)' 
AFTER `slot_number`;

-- 5. 更新现有数据的仓位类型
-- 治疗头1-10为浅部，11-20为深部
UPDATE `bonesys`.`treatment_heads` 
SET `compartment_type` = 'SHALLOW' 
WHERE `head_number` BETWEEN 1 AND 10;

UPDATE `bonesys`.`treatment_heads` 
SET `compartment_type` = 'DEEP' 
WHERE `head_number` BETWEEN 11 AND 20;

-- 6. 重置槽号为NULL（由硬件查询时动态设置）
UPDATE `bonesys`.`treatment_heads` 
SET `slot_number` = NULL;

-- 7. 创建索引
CREATE INDEX `idx_compartment_type` ON `bonesys`.`treatment_heads` (`compartment_type`);
CREATE INDEX `idx_compartment_slot` ON `bonesys`.`treatment_heads` (`compartment_type`, `slot_number`);

-- 8. 验证数据
SELECT 
    head_number,
    slot_number,
    compartment_type,
    CASE 
        WHEN head_number BETWEEN 1 AND 10 THEN '浅部治疗头'
        WHEN head_number BETWEEN 11 AND 20 THEN '深部治疗头'
        ELSE '未知类型'
    END as head_type
FROM `bonesys`.`treatment_heads` 
ORDER BY head_number;

-- 9. 显示修改后的表结构
DESCRIBE `bonesys`.`treatment_heads`;
