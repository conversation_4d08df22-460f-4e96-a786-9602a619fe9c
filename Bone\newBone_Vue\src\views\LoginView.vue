<!-- src/views/LoginView.vue -->
<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';
import http from '@/utils/axios';
import NumericKeypad from '@/components/NumericKeypad.vue'

// 静态导入图片资源
import logoImage from '@/assets/images/login/94ba9fc357ac1731d8758d94f0b98d05.png';
import passwordIcon from '@/assets/images/login/aaa28ee80252446131ad309e4ea8bc2a.png';
import inputLine from '@/assets/images/login/418bad088dc9845f99672504680f4fd6.png';
import eyeIcon from '@/assets/images/login/ffed879f51e05f73d5f48d3fc90c28e0.png';
import loginIcon from '@/assets/images/login/07727ce0ec230bc9e6d8ed1247ad7418.png';
import bottomLogo from '@/assets/images/login/1d9c10ed0305d3151e41acb4998f2f1c.png';
import bgImage from '@/assets/images/login/6ce9f9861ced27679296af9c9ac6c482.png';
import boxBg from '@/assets/images/login/********************************.png';
import loginBtnBg from '@/assets/images/login/8b5f27b4d61eee989b13cfe693bdf49d.png';
import inputBg from '@/assets/images/login/680c683b8cfaea5137d473c73bd994eb.png';
import loginBtnImage from '@/assets/images/login/登录.png';

const router = useRouter();
const password = ref('');
const pressTimer = ref<number | null>(null);
const isPressed = ref(false);
const errorMessage = ref('');
const isInputFocused = ref(false);

// 数字键盘显隐
const showKeypad = ref(false)
const keypadTop = ref(0)
const keypadLeft = ref(0)
const inputWrapRef = ref<HTMLElement | null>(null)

// 小键盘位置微调（像素）。负数向左/向上，正数向右/向下
const keypadOffsetX = ref(-110)
const keypadOffsetY = ref(15)

// 登录功能
const onSubmit = async () => {
  errorMessage.value = '';
  if (!password.value) {
    errorMessage.value = '请输入密码';
    return;
  }
  try {
    const response = await http.post('/auth/login', { password: password.value });
    localStorage.setItem('jwtToken', response.data.token);
    localStorage.setItem('isAuthenticated', 'true');
    router.push('/home');
  } catch (error: any) {
    console.error('登录失败:', error);
    errorMessage.value = '密 码 错 误 ！';
  }
};

// 处理输入框聚焦和失焦
const handleInputFocus = () => {
  isInputFocused.value = true;
  errorMessage.value = '';
  // 触摸屏体验：聚焦即弹出数字小键盘，并定位在输入框下方
  nextTick(() => {
    const el = inputWrapRef.value
    if (el) {
      const rect = el.getBoundingClientRect()
      keypadTop.value = Math.round(rect.bottom + keypadOffsetY.value)
      keypadLeft.value = Math.round(rect.left + keypadOffsetX.value)
    }
    showKeypad.value = true
  })
};
const handleInputBlur = () => {
  isInputFocused.value = false;
};

// 键盘事件
const onKeypadInput = (v: string) => {
  // 限制密码长度，防止超出视觉布局
  if (password.value.length >= 12) return
  password.value += v
}
const onKeypadDelete = () => {
  password.value = password.value.slice(0, -1)
}
const onKeypadClear = () => {
  password.value = ''
}
const onKeypadConfirm = () => {
  showKeypad.value = false
  nextTick(() => onSubmit())
}
const onKeypadClose = () => {
  showKeypad.value = false
}

// 长按功能实现 - 最终优化版本
const startPress = (event: Event) => {
  event.preventDefault();
  event.stopPropagation();
  isPressed.value = true;
  if (pressTimer.value) clearTimeout(pressTimer.value);
  pressTimer.value = setTimeout(() => {
    if (isPressed.value) {
      MessagePlugin.success('长按触发跳转到设置页面');
      localStorage.setItem('isAuthenticated', 'true');
      pressTimer.value = null;
      isPressed.value = false;
      setTimeout(() => {
        router.push('/settings').catch(err => {
          console.error('路由跳转失败:', err);
          MessagePlugin.error('跳转失败');
        });
      }, 100);
    }
  }, 3000);
};

const endPress = (event: Event) => {
  event.preventDefault();
  event.stopPropagation();
  isPressed.value = false;
  if (pressTimer.value) { clearTimeout(pressTimer.value); pressTimer.value = null; }
};

// 全局Enter键监听
const handleGlobalKeyup = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    const activeElement = document.activeElement as HTMLElement | null
    const isInput = activeElement?.tagName === 'INPUT'
    if (!isInput || (activeElement as HTMLInputElement)?.type === 'password') {
      onSubmit();
    }
  }
};

onMounted(() => { document.addEventListener('keyup', handleGlobalKeyup); });
onUnmounted(() => {
  document.removeEventListener('keyup', handleGlobalKeyup);
  if (pressTimer.value) { clearTimeout(pressTimer.value); }
});
</script>

<template>
  <div class="page flex-col">
    <div class="section_1 flex-col">
      <div class="section_2 flex-row">
        <div class="box_1 flex-col">
          <img class="image_1" referrerpolicy="no-referrer" :src="logoImage" />
          <div class="text-group_1 flex-col justify-between">
            <div class="text-wrapper_1">
              <span class="text_1">Hello</span>
              <span class="text_2">，</span>
            </div>
            <span class="text_3">Welcome.</span>
          </div>
          <div class="section_3 flex-row justify-between">
            <div class="block_1 flex-row">
              <img class="label_1" referrerpolicy="no-referrer" :src="passwordIcon" />
              <div class="box_2 flex-col justify-between" ref="inputWrapRef">
                <t-input
                  v-model="password"
                  type="password"
                  size="large"
                  :placeholder="isInputFocused ? '' : '请输入密码'"
                  letter-spacing="4px"
                  @keyup.enter="onSubmit"
                  @keydown.enter.prevent
                  @focus="handleInputFocus"
                  @blur="handleInputBlur"
                  class="password-input"
                />
                <img class="image_2" referrerpolicy="no-referrer" :src="inputLine" />
                <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
              </div>
            </div>
            <img class="login-button" :src="loginBtnImage" @click="onSubmit" @keyup.enter="onSubmit" tabindex="0" role="button" aria-label="登录按钮" referrerpolicy="no-referrer" />
          </div>
        </div>
      </div>
      <div 
        class="image-wrapper_2 flex-row"
        @touchstart.prevent="startPress"
        @touchend.prevent="endPress"
        @touchcancel.prevent="endPress"
        @mousedown.prevent="startPress"
        @mouseup.prevent="endPress"
        @mouseleave.prevent="endPress"
        @click.prevent 
      >
        <img class="image_4" referrerpolicy="no-referrer" :src="bottomLogo" />
      </div>
    </div>

    <!-- 数字小键盘：定位到输入框下方，屏幕不变暗 -->
    <NumericKeypad
      :visible="showKeypad"
      :top="keypadTop"
      :left="keypadLeft"
      @close="onKeypadClose"
      @input="onKeypadInput"
      @delete="onKeypadDelete"
      @clear="onKeypadClear"
      @confirm="onKeypadConfirm"
    />
  </div>
</template>

<style scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.section_1 {
  height: 1080px;
  background: url('@/assets/images/login/6ce9f9861ced27679296af9c9ac6c482.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.section_2 {
  width: 1319px;
  height: 779px;
  margin: 116px 0 0 301px;
}

.box_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  width: 1319px;
  height: 779px;
  border: 1px solid rgba(96, 96, 96, 1);
}

.image_1 {
  width: 677px;
  height: 109px;
  margin: 91px 0 0 135px;
}

.text-group_1 {
  width: 254px;
  height: 101px;
  margin: 61px 0 0 144px;
}

.text-wrapper_1 {
  width: 157px;
  height: 43px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: ArialMT;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 31px;
  margin-left: 3px;
}

.text_1 {
  width: 157px;
  height: 43px;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 50px;
  font-family: ArialMT;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 31px;
}

.text_2 {
  width: 157px;
  height: 43px;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 50px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 31px;
}

.text_3 {
  width: 254px;
  height: 38px;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 50px;
  font-family: ArialMT;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 29px;
  margin-top: 20px;
}

.section_3 {
  width: 617px;
  height: 69px;
  margin: 189px 0 159px 350px;
}

.block_1 {
  width: 333px;
  height: 56px;
  background: url('@/assets/images/login/********************************.png') -9px 0px no-repeat;
  background-size: 352px 78px;
  margin-top: 7px;
}

.label_1 {
  width: 32px;
  height: 36px;
  margin: 9px 0 0 33px;
}

.box_2 {
  width: 177px;
  height: 35px;
  margin: 10px 0 0 54px;
  position: relative;
  overflow: visible !important;
}

.password-input {
  /* t-input组件高度小一点 */
  width: 170px;
  height: 32px;
  border: none !important;
  background: transparent !important;
  outline: none !important;
  font-size: 20px;
  text-align: center;
  letter-spacing: 4px;
  font-family: MicrosoftYaHei;
  margin-top: -1px;
  box-shadow: none !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0 !important;          /* 移除内边距 */
  margin-left: -12px !important;           /* 移除左边距 */
}

/* 深度选择器，覆盖组件内部样式 */
:deep(.t-input) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  height: 32px !important;
  min-height: 32px !important;
  overflow: visible !important;
  left: -10px;
  background-color: rgba(255, 255, 255, 0.7) !important;
}

:deep(.t-input__inner) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  height: 32px !important;
  min-height: 34px !important;
  line-height: 32px !important;
  padding: 0 8px !important;
  text-align: center !important;
  opacity: 0.7 !important;
}

/* placeholder文字居中显示 */
:deep(.t-input__inner::placeholder) {
  opacity: 1 !important;
  text-align: center !important;
  letter-spacing: 4px !important;
  font-family: MicrosoftYaHei !important;
}

:deep(.t-input__wrap) {
  height: 32px !important;
  min-height: 32px !important;
}

/* 调整密码可见性切换图标位置 - 移动到输入框外右侧 */
:deep(.t-input__suffix) {
  position: absolute !important;
  right: -45px !important;
  top: 5px !important;
  transform: none !important;
  margin-right: 0 !important;
  padding-right: 0 !important;
  background: rgba(255, 255, 255, 0) !important;
  border-radius: 4px !important;
  padding: 2px !important;
  z-index: 1000 !important;
}

:deep(.t-input__suffix-inner) {
  margin-right: 0 !important;
}

:deep(.t-icon-browse) {
  margin-right: 0 !important;
  color: rgba(89, 89, 89, 1) !important;
  font-size: 25px !important;
  cursor: pointer !important;
}

:deep(.t-icon-browse-off) {
  margin-right: 0 !important;
  color: rgba(89, 89, 89, 1) !important;
  font-size: 25px !important;
  cursor: pointer !important;
}
.custom-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 190px;
  height: 20px;
  color: rgba(116, 116, 116, 1);
  font-size: 20px;
  font-family: MicrosoftYaHei;
  letter-spacing: 4px;
  text-align: center;
  line-height: 20px;
  z-index: 1;
  letter-spacing: 4px;
  pointer-events: none; /* 让点击事件穿透到输入框 */
}
.image_2 {
  position: absolute;
  width: 190px;
  height: 1px;
  margin-top: 35px;
  margin-left: -29px;
}

/* 错误消息样式 */
.error-message {
  position: absolute;
  top: 2px;
  left: -20px;

  width: 200px;
  color: #ff0000;
  font-size: 20px;
  font-family: MicrosoftYaHei;
  text-align: center;
  margin-top: 50px;
  margin-left: -19px;
}

.label_2 {
  width: 24px;
  height: 21px;
  margin: 17px 23px 0 20px;
}

/* 新的登录按钮样式 - 使用单个图片 */
.login-button {
  width: 246px;
  height: 88px;
  cursor: pointer;
  transition: transform 0.2s ease;
  margin-top: 0px;
}

.login-button:hover {
  transform: scale(1.05);
}

.login-button:active {
  transform: scale(0.98);
}

.login-button:focus {
  outline: 2px solid rgba(248, 128, 36, 0.5);
  outline-offset: 2px;
  transform: scale(1.02);
}

.image-wrapper_2 {
  width: 138px;
  height: 50px;
  margin: 91px 0 44px 1708px;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
}

.image_4 {
  width: 138px;
  height: 50px;
  transition: transform 0.2s ease;
}

.image-wrapper_2:active .image_4 {
  transform: scale(0.95);
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style> 