package com.Bone.BoneSys.exception;

import com.Bone.BoneSys.dto.hardware.AvailabilityDetail;

/**
 * 治疗头数量不足异常
 * 当可用治疗头数量无法满足需求时抛出
 */
public class InsufficientTreatmentHeadsException extends TreatmentHeadRecommendationException {
    
    private AvailabilityDetail availabilityDetail;
    
    public InsufficientTreatmentHeadsException(String message, AvailabilityDetail availabilityDetail) {
        super("INSUFFICIENT_HEADS", 
              generateUserFriendlyMessage(availabilityDetail), 
              message);
        this.availabilityDetail = availabilityDetail;
    }
    
    public InsufficientTreatmentHeadsException(String message, AvailabilityDetail availabilityDetail, Throwable cause) {
        super("INSUFFICIENT_HEADS", 
              generateUserFriendlyMessage(availabilityDetail), 
              message, 
              cause);
        this.availabilityDetail = availabilityDetail;
    }
    
    public AvailabilityDetail getAvailabilityDetail() {
        return availabilityDetail;
    }
    
    /**
     * 生成用户友好的错误消息
     */
    private static String generateUserFriendlyMessage(AvailabilityDetail detail) {
        if (detail == null) {
            return "治疗头数量不足，请等待充电完成或更换治疗头";
        }
        
        StringBuilder message = new StringBuilder("治疗头数量不足：");
        
        if (!detail.isShallowSufficient()) {
            int shortage = detail.getShallowRequired() - detail.getShallowAvailable();
            message.append(String.format("浅部治疗头缺少%d个", shortage));
        }
        
        if (!detail.isDeepSufficient()) {
            if (!detail.isShallowSufficient()) {
                message.append("，");
            }
            int shortage = detail.getDeepRequired() - detail.getDeepAvailable();
            message.append(String.format("深部治疗头缺少%d个", shortage));
        }
        
        message.append("。建议等待充电完成或更换治疗头。");
        
        return message.toString();
    }
    
    /**
     * 获取详细的不足信息
     */
    public String getDetailedShortageInfo() {
        if (availabilityDetail == null) {
            return "详细信息不可用";
        }
        
        StringBuilder info = new StringBuilder();
        
        if (!availabilityDetail.isShallowSufficient()) {
            info.append(String.format("浅部治疗头：需要%d个，可用%d个，缺少%d个\n", 
                       availabilityDetail.getShallowRequired(),
                       availabilityDetail.getShallowAvailable(),
                       availabilityDetail.getShallowRequired() - availabilityDetail.getShallowAvailable()));
        }
        
        if (!availabilityDetail.isDeepSufficient()) {
            info.append(String.format("深部治疗头：需要%d个，可用%d个，缺少%d个\n", 
                       availabilityDetail.getDeepRequired(),
                       availabilityDetail.getDeepAvailable(),
                       availabilityDetail.getDeepRequired() - availabilityDetail.getDeepAvailable()));
        }
        
        return info.toString().trim();
    }
}