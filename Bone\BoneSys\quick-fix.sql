-- Quick fix for compartment_type issue
USE bonesys;

-- Show current problem
SELECT 'BEFORE FIX - Problem diagnosis' as info;
SELECT 
    head_number,
    compartment_type,
    realtime_status,
    battery_level,
    CASE 
        WHEN compartment_type IS NULL THEN 'NULL - THIS IS THE PROBLEM'
        WHEN compartment_type = 'DEEP' AND battery_level >= 20 THEN 'DEEP_AVAILABLE'
        WHEN compartment_type = 'SHALLOW' AND battery_level >= 20 THEN 'SHALLOW_AVAILABLE'
        ELSE 'NOT_AVAILABLE'
    END as diagnosis
FROM treatment_heads 
WHERE head_number BETWEEN 11 AND 20
ORDER BY head_number;

-- Fix the problem
UPDATE treatment_heads 
SET compartment_type = 'SHALLOW' 
WHERE head_number BETWEEN 1 AND 10 AND (compartment_type IS NULL OR compartment_type = '');

UPDATE treatment_heads 
SET compartment_type = 'DEEP' 
WHERE head_number BETWEEN 11 AND 20 AND (compartment_type IS NULL OR compartment_type = '');

-- Verify the fix
SELECT 'AFTER FIX - Should show DEEP_AVAILABLE' as info;
SELECT 
    head_number,
    compartment_type,
    realtime_status,
    battery_level,
    CASE 
        WHEN compartment_type = 'DEEP' AND battery_level >= 20 THEN 'DEEP_AVAILABLE'
        WHEN compartment_type = 'SHALLOW' AND battery_level >= 20 THEN 'SHALLOW_AVAILABLE'
        WHEN compartment_type IS NULL THEN 'STILL_NULL'
        ELSE 'NOT_AVAILABLE'
    END as status
FROM treatment_heads 
WHERE head_number BETWEEN 11 AND 20
ORDER BY head_number;

-- Count available heads
SELECT 'SUMMARY - Available heads count' as info;
SELECT 
    'DEEP available heads' as type,
    COUNT(*) as count
FROM treatment_heads 
WHERE compartment_type = 'DEEP' AND battery_level >= 20

UNION ALL

SELECT 
    'SHALLOW available heads' as type,
    COUNT(*) as count
FROM treatment_heads 
WHERE compartment_type = 'SHALLOW' AND battery_level >= 20;
