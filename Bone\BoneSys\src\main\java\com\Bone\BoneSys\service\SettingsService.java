package com.Bone.BoneSys.service;

import com.Bone.BoneSys.config.AppProperties;
import com.Bone.BoneSys.entity.User;
import com.Bone.BoneSys.repository.UserRepository;
import com.Bone.BoneSys.dto.SystemSettingsRequest;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统设置服务
 * 负责管理系统参数配置、设备配置等
 */
@Service
public class SettingsService {

    private static final Logger logger = LoggerFactory.getLogger(SettingsService.class);

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private Environment environment;

    @Autowired
    private UserRepository userRepository;

    // 串口配置参数
    @Value("${serial.port.name:COM1}")
    private String serialPortName;

    @Value("${serial.port.baud-rate:115200}")
    private Integer serialBaudRate;

    @Value("${serial.port.timeout:5000}")
    private Integer serialTimeout;

    @Value("${serial.connection.max-retry-attempts:5}")
    private Integer maxRetryAttempts;

    @Value("${serial.connection.retry-delay:2000}")
    private Long retryDelay;

    // 硬件配置参数
    @Value("${hardware.simulator.enabled:true}")
    private Boolean hardwareSimulatorEnabled;

    @Value("${hardware.performance.metrics-report.interval:60000}")
    private Long metricsReportInterval;

    // 治疗参数默认值
    @Value("${treatment.default.duration:20}")
    private Integer defaultTreatmentDuration;

    @Value("${treatment.default.intensity:45}")
    private Integer defaultTreatmentIntensity;

    @Value("${treatment.default.frequency:1000}")
    private Integer defaultTreatmentFrequency;

    @Value("${treatment.max-usage-count:500}")
    private Integer maxUsageCount;

    @Value("${treatment.low-battery-threshold:20}")
    private Integer lowBatteryThreshold;

    /**
     * 获取系统参数配置
     */
    public SystemParameters getSystemParameters() {
        logger.info("Fetching system parameters");

        SystemParameters params = new SystemParameters();
        
        // 基础系统信息
        params.setSystemName("FREEBONE医疗系统");
        params.setSystemVersion("1.0.0");
        params.setEnvironment(getActiveProfile());
        
        // 治疗参数配置
        TreatmentParameters treatmentParams = new TreatmentParameters();
        treatmentParams.setDefaultDuration(defaultTreatmentDuration);
        treatmentParams.setDefaultIntensity(defaultTreatmentIntensity);
        treatmentParams.setDefaultFrequency(defaultTreatmentFrequency);
        treatmentParams.setMaxUsageCount(maxUsageCount);
        treatmentParams.setLowBatteryThreshold(lowBatteryThreshold);
        treatmentParams.setIntensityOptions(new int[]{30, 45, 60});
        treatmentParams.setFrequencyOptions(new int[]{100, 1000});
        treatmentParams.setDurationRange(new DurationRange(5, 60));
        params.setTreatmentParameters(treatmentParams);
        
        // 设备配置
        DeviceConfiguration deviceConfig = new DeviceConfiguration();
        deviceConfig.setTotalTreatmentHeads(20);
        deviceConfig.setShallowCompartmentRange(new CompartmentRange(1, 10));
        deviceConfig.setDeepCompartmentRange(new CompartmentRange(11, 20));
        deviceConfig.setLightColors(Map.of(
            1, "橙色",
            2, "蓝色", 
            3, "绿色"
        ));
        params.setDeviceConfiguration(deviceConfig);
        
        // 串口配置
        SerialConfiguration serialConfig = new SerialConfiguration();
        serialConfig.setPortName(serialPortName);
        serialConfig.setBaudRate(serialBaudRate);
        serialConfig.setTimeout(serialTimeout);
        serialConfig.setMaxRetryAttempts(maxRetryAttempts);
        serialConfig.setRetryDelay(retryDelay);
        params.setSerialConfiguration(serialConfig);
        
        // 硬件配置
        HardwareConfiguration hardwareConfig = new HardwareConfiguration();
        hardwareConfig.setSimulatorEnabled(hardwareSimulatorEnabled);
        hardwareConfig.setMetricsReportInterval(metricsReportInterval);
        params.setHardwareConfiguration(hardwareConfig);

        logger.info("Successfully fetched system parameters");
        return params;
    }

    /**
     * 更新系统参数配置
     */
    public void updateSystemParameters(SystemParametersUpdateRequest request) {
        logger.info("Updating system parameters: {}", request);

        // 验证参数有效性
        validateSystemParameters(request);

        // 更新治疗参数
        if (request.getTreatmentParameters() != null) {
            updateTreatmentParameters(request.getTreatmentParameters());
        }

        // 更新设备配置
        if (request.getDeviceConfiguration() != null) {
            updateDeviceConfiguration(request.getDeviceConfiguration());
        }

        // 更新串口配置
        if (request.getSerialConfiguration() != null) {
            updateSerialConfiguration(request.getSerialConfiguration());
        }

        // 更新硬件配置
        if (request.getHardwareConfiguration() != null) {
            updateHardwareConfiguration(request.getHardwareConfiguration());
        }

        logger.info("Successfully updated system parameters");
    }

    /**
     * 获取设备配置信息
     */
    public DeviceConfigurationInfo getDeviceConfiguration() {
        logger.info("Fetching device configuration");

        DeviceConfigurationInfo config = new DeviceConfigurationInfo();
        config.setTotalTreatmentHeads(20);
        config.setCompartmentConfiguration(Map.of(
            "shallow", Map.of("start", 1, "end", 10, "description", "浅部治疗头"),
            "deep", Map.of("start", 11, "end", 20, "description", "深部治疗头")
        ));
        config.setLightColorConfiguration(Map.of(
            1, Map.of("name", "橙色", "description", "第一个治疗部位"),
            2, Map.of("name", "蓝色", "description", "第二个治疗部位"),
            3, Map.of("name", "绿色", "description", "第三个治疗部位")
        ));
        config.setTreatmentModes(Map.of(
            "ON_SITE", "现场治疗模式",
            "TAKE_AWAY", "取走治疗模式"
        ));

        logger.info("Successfully fetched device configuration");
        return config;
    }

    /**
     * 重置系统参数为默认值
     */
    public void resetToDefaults() {
        logger.info("Resetting system parameters to defaults");
        
        // 这里可以实现重置逻辑
        // 由于当前使用配置文件管理，主要是记录重置操作
        
        logger.info("System parameters reset to defaults completed");
    }

    /**
     * 获取当前活动的配置文件
     */
    private String getActiveProfile() {
        String[] activeProfiles = environment.getActiveProfiles();
        return activeProfiles.length > 0 ? activeProfiles[0] : "default";
    }

    /**
     * 验证系统参数有效性
     */
    private void validateSystemParameters(SystemParametersUpdateRequest request) {
        if (request.getTreatmentParameters() != null) {
            TreatmentParameters tp = request.getTreatmentParameters();
            if (tp.getDefaultDuration() != null && (tp.getDefaultDuration() < 5 || tp.getDefaultDuration() > 60)) {
                throw new IllegalArgumentException("治疗时长必须在5-60分钟之间");
            }
            if (tp.getDefaultIntensity() != null && tp.getDefaultIntensity() != 30 && tp.getDefaultIntensity() != 45 && tp.getDefaultIntensity() != 60) {
                throw new IllegalArgumentException("治疗强度只能是30、45或60档位");
            }
            if (tp.getDefaultFrequency() != null && tp.getDefaultFrequency() != 100 && tp.getDefaultFrequency() != 1000) {
                throw new IllegalArgumentException("治疗频率只能是100Hz或1000Hz");
            }
        }
    }

    /**
     * 更新治疗参数（实际实现中可能需要更新配置文件或数据库）
     */
    private void updateTreatmentParameters(TreatmentParameters params) {
        logger.info("Updating treatment parameters: {}", params);
        // 实际实现中可能需要更新配置文件或数据库
    }

    /**
     * 更新设备配置
     */
    private void updateDeviceConfiguration(DeviceConfiguration config) {
        logger.info("Updating device configuration: {}", config);
        // 实际实现中可能需要更新配置文件或数据库
    }

    /**
     * 更新串口配置
     */
    private void updateSerialConfiguration(SerialConfiguration config) {
        logger.info("Updating serial configuration: {}", config);
        // 实际实现中可能需要重新初始化串口连接
    }

    /**
     * 更新硬件配置
     */
    private void updateHardwareConfiguration(HardwareConfiguration config) {
        logger.info("Updating hardware configuration: {}", config);
        // 实际实现中可能需要重新初始化硬件服务
    }

    // DTO类定义
    @Data
    public static class SystemParameters {
        private String systemName;
        private String systemVersion;
        private String environment;
        private TreatmentParameters treatmentParameters;
        private DeviceConfiguration deviceConfiguration;
        private SerialConfiguration serialConfiguration;
        private HardwareConfiguration hardwareConfiguration;
    }

    @Data
    public static class TreatmentParameters {
        private Integer defaultDuration;
        private Integer defaultIntensity;
        private Integer defaultFrequency;
        private Integer maxUsageCount;
        private Integer lowBatteryThreshold;
        private int[] intensityOptions;
        private int[] frequencyOptions;
        private DurationRange durationRange;
    }

    @Data
    public static class DeviceConfiguration {
        private Integer totalTreatmentHeads;
        private CompartmentRange shallowCompartmentRange;
        private CompartmentRange deepCompartmentRange;
        private Map<Integer, String> lightColors;
    }

    @Data
    public static class SerialConfiguration {
        private String portName;
        private Integer baudRate;
        private Integer timeout;
        private Integer maxRetryAttempts;
        private Long retryDelay;
    }

    @Data
    public static class HardwareConfiguration {
        private Boolean simulatorEnabled;
        private Long metricsReportInterval;
    }



    @Data
    public static class DurationRange {
        private Integer min;
        private Integer max;
        
        public DurationRange(Integer min, Integer max) {
            this.min = min;
            this.max = max;
        }
    }

    @Data
    public static class CompartmentRange {
        private Integer start;
        private Integer end;
        
        public CompartmentRange(Integer start, Integer end) {
            this.start = start;
            this.end = end;
        }
    }

    @Data
    public static class DeviceConfigurationInfo {
        private Integer totalTreatmentHeads;
        private Map<String, Map<String, Object>> compartmentConfiguration;
        private Map<Integer, Map<String, String>> lightColorConfiguration;
        private Map<String, String> treatmentModes;
    }

    @Data
    public static class SystemParametersUpdateRequest {
        private TreatmentParameters treatmentParameters;
        private DeviceConfiguration deviceConfiguration;
        private SerialConfiguration serialConfiguration;
        private HardwareConfiguration hardwareConfiguration;
    }

    // 新增的设置功能方法

    /**
     * 密码重置
     */
    public boolean resetPassword(String factoryPassword) {
        logger.info("Attempting password reset with factory password");
        
        // 验证厂家密码
        if (!"000000".equals(factoryPassword)) {
            logger.warn("Invalid factory password provided");
            return false;
        }
        
        try {
            User user = userRepository.findTheOnlyUser().orElse(null);
            if (user != null) {
                user.setPassword("123456");
                userRepository.save(user);
                logger.info("Password reset successfully");
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("Error resetting password", e);
            return false;
        }
    }

    /**
     * 修改密码
     */
    public boolean changePassword(String oldPassword, String newPassword) {
        logger.info("Attempting password change");
        
        try {
            User user = userRepository.findTheOnlyUser().orElse(null);
            if (user != null) {
                // 验证原密码
                if (!user.getPassword().equals(oldPassword)) {
                    logger.warn("Old password incorrect");
                    return false;
                }
                
                // 设置新密码
                user.setPassword(newPassword);
                userRepository.save(user);
                logger.info("Password changed successfully");
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("Error changing password", e);
            return false;
        }
    }

    /**
     * 获取系统设置
     */
    public Object getSystemSettings() {
        logger.info("Getting system settings");
        
        Map<String, Object> settings = new HashMap<>();
        settings.put("volume", 75);
        settings.put("screenTimeout", "20min");
        settings.put("language", "中文");
        settings.put("reminderTimes", new String[]{"10min", "15min", "20min"});
        
        return settings;
    }

    /**
     * 更新系统设置
     */
    public void updateSystemSettings(SystemSettingsRequest request) {
        logger.info("Updating system settings: {}", request);
        
        // 这里可以实现具体的系统设置保存逻辑
        // 例如保存到配置文件、数据库或系统配置中
        
        logger.info("System settings updated successfully");
    }
}
