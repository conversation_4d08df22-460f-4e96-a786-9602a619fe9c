package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.websocket.NotificationWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 通知测试控制器
 * 用于测试各种弹窗通知功能
 */
@RestController
@RequestMapping("/api/test/notifications")
@CrossOrigin(origins = "*")
public class NotificationTestController {

    @Autowired
    private NotificationWebSocketHandler notificationHandler;

    /**
     * 测试治疗完成通知
     */
    @PostMapping("/treatment-completed")
    public ApiResponse<Object> testTreatmentCompletedNotification(@RequestParam String patientName) {
        try {
            notificationHandler.sendTreatmentCompletedNotification(patientName);
            return ApiResponse.success("治疗完成通知已发送", "已发送治疗完成通知: " + patientName);
        } catch (Exception e) {
            return ApiResponse.error(500, "发送通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试待取回提醒通知
     */
    @PostMapping("/pickup-reminder")
    public ApiResponse<Object> testPickupReminderNotification(@RequestParam String headNumbers) {
        try {
            // 解析治疗头编号
            String[] numbers = headNumbers.split(",");
            java.util.List<Integer> headNumberList = new java.util.ArrayList<>();
            for (String number : numbers) {
                headNumberList.add(Integer.parseInt(number.trim()));
            }
            
            notificationHandler.sendPickupReminderNotification(headNumberList);
            return ApiResponse.success("待取回提醒通知已发送", "已发送待取回提醒: " + headNumbers);
        } catch (Exception e) {
            return ApiResponse.error(500, "发送通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试重新插入治疗头通知
     */
    @PostMapping("/reinsert-head")
    public ApiResponse<Object> testReinsertHeadNotification(
            @RequestParam int headNumber,
            @RequestParam(defaultValue = "上仓(浅部)") String compartmentType,
            @RequestParam(defaultValue = "1") int slotNumber) {
        try {
            notificationHandler.sendReinsertHeadNotification(headNumber, compartmentType, slotNumber);
            return ApiResponse.success("重新插入治疗头通知已发送", 
                String.format("已发送重新插入通知: %d号治疗头", headNumber));
        } catch (Exception e) {
            return ApiResponse.error(500, "发送通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试治疗头复位通知
     */
    @PostMapping("/reset-head")
    public ApiResponse<Object> testResetHeadNotification(
            @RequestParam int headNumber,
            @RequestParam(defaultValue = "上仓(浅部)") String compartmentType,
            @RequestParam(defaultValue = "1") int slotNumber,
            @RequestParam(defaultValue = "6") int failureCount) {
        try {
            notificationHandler.sendResetHeadNotification(headNumber, compartmentType, slotNumber, failureCount);
            return ApiResponse.success("治疗头复位通知已发送", 
                String.format("已发送复位通知: %d号治疗头，失败%d次", headNumber, failureCount));
        } catch (Exception e) {
            return ApiResponse.error(500, "发送通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试硬件错误通知
     */
    @PostMapping("/hardware-error")
    public ApiResponse<Object> testHardwareErrorNotification(
            @RequestParam String errorCode,
            @RequestParam String message,
            @RequestParam String headNumbers) {
        try {
            // 解析治疗头编号
            String[] numbers = headNumbers.split(",");
            java.util.List<Integer> headNumberList = new java.util.ArrayList<>();
            for (String number : numbers) {
                headNumberList.add(Integer.parseInt(number.trim()));
            }
            
            notificationHandler.sendHardwareErrorNotification(errorCode, message, headNumberList);
            return ApiResponse.success("硬件错误通知已发送", 
                String.format("已发送硬件错误通知: %s - %s", errorCode, message));
        } catch (Exception e) {
            return ApiResponse.error(500, "发送通知失败: " + e.getMessage());
        }
    }

    /**
     * 批量测试所有通知类型
     */
    @PostMapping("/test-all")
    public ApiResponse<Object> testAllNotifications() {
        try {
            // 1. 治疗完成通知
            notificationHandler.sendTreatmentCompletedNotification("测试患者");
            Thread.sleep(1000);

            // 2. 待取回提醒
            java.util.List<Integer> headNumbers = java.util.Arrays.asList(1, 2, 3);
            notificationHandler.sendPickupReminderNotification(headNumbers);
            Thread.sleep(1000);

            // 3. 重新插入治疗头
            notificationHandler.sendReinsertHeadNotification(5, "上仓(浅部)", 5);
            Thread.sleep(1000);

            // 4. 治疗头复位
            notificationHandler.sendResetHeadNotification(10, "下仓(深部)", 10, 8);
            Thread.sleep(1000);

            // 5. 硬件错误
            notificationHandler.sendHardwareErrorNotification("ERR001", "通信超时", 
                java.util.Arrays.asList(7, 8, 9));

            return ApiResponse.success("所有测试通知已发送", "已发送5种类型的测试通知");
        } catch (Exception e) {
            return ApiResponse.error(500, "批量测试失败: " + e.getMessage());
        }
    }
}
