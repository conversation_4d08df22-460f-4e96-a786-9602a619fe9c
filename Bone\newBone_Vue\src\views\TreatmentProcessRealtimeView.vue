<template>
  <div class="treatment-process-realtime-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-text">{{ error }}</div>
      <button @click="fetchRealtimeData" class="retry-button">重试</button>
    </div>

    <!-- 实时数据显示 -->
    <div v-else-if="realtimeData" class="realtime-data-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <button @click="goBack" class="back-button">← 返回</button>
        <h2 class="page-title">治疗进程实时监控</h2>
      </div>

      <!-- 患者信息 -->
      <div class="patient-info-section">
        <div class="patient-name">患者：{{ realtimeData.patientName }}</div>
        <div class="treatment-mode">
          治疗模式：{{ formatTreatmentMode(realtimeData.treatmentMode) }}
        </div>
      </div>

      <!-- 治疗部位列表 -->
      <div class="body-parts-section">
        <h3 class="section-title">治疗部位进度</h3>
        <div class="body-parts-list">
          <div 
            v-for="(bodyPart, index) in realtimeData.bodyParts" 
            :key="index"
            class="body-part-card"
          >
            <div class="body-part-header">
              <div class="body-part-name">{{ bodyPart.bodyPart }}</div>
              <div class="body-part-status" :class="getStatusClass(bodyPart.remainingTime)">
                {{ getStatusText(bodyPart.remainingTime) }}
              </div>
            </div>
            <div class="body-part-details">
              <div class="detail-item">
                <span class="detail-label">剩余时间：</span>
                <span class="detail-value time-value">{{ bodyPart.remainingTime }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">治疗强度：</span>
                <span class="detail-value intensity-value">{{ bodyPart.intensity }}</span>
              </div>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: calculateProgress(bodyPart.remainingTime) + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <button @click="refreshData" class="refresh-button">刷新数据</button>
        <button @click="stopTreatment" class="stop-button">停止治疗</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getProcessRealtime, type ProcessRealtimeResponse } from '@/api/treatmentProcess';
import { MessagePlugin } from 'tdesign-vue-next';

const route = useRoute();
const router = useRouter();

// 数据状态
const loading = ref(true);
const error = ref<string | null>(null);
const realtimeData = ref<ProcessRealtimeResponse | null>(null);

// 定时器
let refreshTimer: NodeJS.Timeout | null = null;

// 获取实时数据
const fetchRealtimeData = async () => {
  try {
    const processId = Number(route.params.processId);
    if (!processId) {
      error.value = '无效的进程ID';
      return;
    }

    loading.value = true;
    error.value = null;
    
    const response = await getProcessRealtime(processId);
    if (response.data) {
      realtimeData.value = response.data;
    } else {
      error.value = '无法获取实时数据';
    }
  } catch (err: any) {
    console.error('获取实时数据失败:', err);
    error.value = err.message || '获取实时数据失败';
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  fetchRealtimeData();
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 停止治疗
const stopTreatment = () => {
  MessagePlugin.warning('停止治疗功能待实现');
};

// 格式化治疗模式
const formatTreatmentMode = (mode: string) => {
  return mode === 'ON_SITE' ? '本地治疗' : '取走治疗';
};

// 获取状态文本
const getStatusText = (remainingTime: string) => {
  if (remainingTime === '已完成' || remainingTime === '0分0秒') {
    return '已完成';
  }
  return '治疗中';
};

// 获取状态样式类
const getStatusClass = (remainingTime: string) => {
  if (remainingTime === '已完成' || remainingTime === '0分0秒') {
    return 'status-completed';
  }
  return 'status-treating';
};

// 计算进度百分比
const calculateProgress = (remainingTime: string) => {
  if (remainingTime === '已完成' || remainingTime === '0分0秒') {
    return 100;
  }
  
  // 简单的进度计算逻辑，可以根据实际需求调整
  const match = remainingTime.match(/(\d+)分/);
  if (match) {
    const minutes = parseInt(match[1]);
    // 假设总时间是20分钟，根据剩余时间计算进度
    const totalMinutes = 20;
    const elapsed = totalMinutes - minutes;
    return Math.max(0, Math.min(100, (elapsed / totalMinutes) * 100));
  }
  
  return 0;
};

// 页面挂载时获取数据并设置定时刷新
onMounted(() => {
  fetchRealtimeData();
  
  // 每5秒自动刷新一次数据
  refreshTimer = setInterval(() => {
    fetchRealtimeData();
  }, 5000);
});

// 页面卸载时清除定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});
</script>

<style scoped>
.treatment-process-realtime-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.loading-text {
  font-size: 18px;
  color: #666;
}

.error-text {
  font-size: 16px;
  color: #e74c3c;
  margin-bottom: 16px;
}

.retry-button {
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-button:hover {
  background-color: #2980b9;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.back-button {
  padding: 8px 16px;
  background-color: #95a5a6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 16px;
}

.back-button:hover {
  background-color: #7f8c8d;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.patient-info-section {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.patient-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.treatment-mode {
  font-size: 16px;
  color: #7f8c8d;
}

.body-parts-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.body-parts-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 16px;
}

.body-part-card {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.body-part-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.body-part-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.body-part-status {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-treating {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-completed {
  background-color: #e8f5e8;
  color: #388e3c;
}

.body-part-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-label {
  color: #7f8c8d;
  font-size: 14px;
}

.detail-value {
  font-weight: 500;
  font-size: 14px;
}

.time-value {
  color: #e74c3c;
}

.intensity-value {
  color: #f39c12;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #3498db;
  transition: width 0.3s ease;
}

.actions-section {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.refresh-button,
.stop-button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.refresh-button {
  background-color: #3498db;
  color: white;
}

.refresh-button:hover {
  background-color: #2980b9;
}

.stop-button {
  background-color: #e74c3c;
  color: white;
}

.stop-button:hover {
  background-color: #c0392b;
}
</style> 