package com.Bone.BoneSys.dto.hardware;

import java.util.List;

/**
 * 治疗确认请求DTO
 * 用于确认治疗头选择并发送参数
 */
public class TreatmentConfirmationRequest {
    
    private String patientId;
    private String treatmentMode; // "local" 或 "takeaway"
    private List<TreatmentHeadRecommendation> recommendedHeads;
    private TreatmentParamsRequest treatmentParams;
    
    public TreatmentConfirmationRequest() {}
    
    public TreatmentConfirmationRequest(String patientId, String treatmentMode, 
                                      List<TreatmentHeadRecommendation> recommendedHeads,
                                      TreatmentParamsRequest treatmentParams) {
        this.patientId = patientId;
        this.treatmentMode = treatmentMode;
        this.recommendedHeads = recommendedHeads;
        this.treatmentParams = treatmentParams;
    }
    
    // Getters and Setters
    public String getPatientId() {
        return patientId;
    }
    
    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }
    
    public String getTreatmentMode() {
        return treatmentMode;
    }
    
    public void setTreatmentMode(String treatmentMode) {
        this.treatmentMode = treatmentMode;
    }
    
    public List<TreatmentHeadRecommendation> getRecommendedHeads() {
        return recommendedHeads;
    }
    
    public void setRecommendedHeads(List<TreatmentHeadRecommendation> recommendedHeads) {
        this.recommendedHeads = recommendedHeads;
    }
    
    public TreatmentParamsRequest getTreatmentParams() {
        return treatmentParams;
    }
    
    public void setTreatmentParams(TreatmentParamsRequest treatmentParams) {
        this.treatmentParams = treatmentParams;
    }
    
    @Override
    public String toString() {
        return "TreatmentConfirmationRequest{" +
                "patientId='" + patientId + '\'' +
                ", treatmentMode='" + treatmentMode + '\'' +
                ", recommendedHeads=" + recommendedHeads +
                ", treatmentParams=" + treatmentParams +
                '}';
    }
} 