# FREEBONE医疗系统需求文档

## 介绍

本系统是一款基于SpringBoot + Vue 3架构的FREEBONE医疗设备管理系统，用于管理治疗设备的患者档案、治疗进程、设备状态等功能。系统通过串口通信控制治疗头硬件设备，支持现场治疗(ON_SITE)和带走治疗(TAKE_AWAY)两种模式。UI界面固定且少于20页，后端按照每张UI图片需要的数据来组织和传输数据。

技术栈：Java JDK 17 + MySQL 8.0 + SpringBoot 3.4.7 + Vue 3
硬件通信：串口通信，波特率115200，停止位1，数据位8，校验位None

## 需求

### 需求 1 - 用户认证登录界面（图版1）

**用户故事：** 作为系统操作员，我希望通过密码验证登录系统，确保设备安全使用。

#### 验收标准

1. 当系统启动时，系统应显示密码登录界面
2. 首次登录需要密码重置，使用厂家密码，后续使用用户自己设置的密码
3. 当用户输入用户名和密码时，系统应根据isFactoryPassword标志验证厂家密码或用户自定义密码
4. 当密码验证成功后，系统应生成JWT token并跳转到主界面
5. 当密码验证失败时，系统应显示错误提示并保持在登录界面
6. 当登录成功后，系统应更新last_updated_at字段记录登录时间

### 需求 2 - 主界面（图版3）

**用户故事：** 作为医护人员，我希望在主界面能够快速访问主要功能模块和查看系统状态。

#### 验收标准

1. 当用户登录成功后，系统应显示主界面
2. 当在主界面时，系统应显示当前系统时间和系统状态
3. 当在主界面时，系统应提供快捷操作按钮（新建档案、档案管理等）
4. 当在主界面时，系统应显示各功能模块的入口图标和名称
5. 当点击功能入口时，系统应正确跳转到对应的功能界面

### 需求 3 - 患者档案管理

**用户故事：** 作为医护人员，我希望能够创建和管理患者档案，包括新建档案、信息录入和档案查询。

#### 验收标准

1. 当进入新建档案界面（图版5）时，系统应显示最近治疗患者信息列表，包括就诊卡号、姓名、年龄、性别、最后治疗日期、治疗部位、会话次数
2. 当在患者信息录入界面（图版6）时，系统应提供就诊卡号、姓名、性别、年龄、联系方式等字段的输入
3. 当创建患者信息时，系统应验证就诊卡号的唯一性，重复时显示红色文字提示
4. 当在档案管理界面（图版20）时，系统应支持按就诊卡号和姓名查询患者档案
5. 当在个人信息界面（图版21）时，系统应显示患者基本信息、诊断描述、治疗历史记录和各部位统计数据，诊断描述可以更改，需要同步数据库
6. 当修改患者信息时，系统应支持编辑基本信息字段并保存到数据库

### 需求 4 - 治疗参数设置界面（图版7）

**用户故事：** 作为医护人员，我希望能够为患者设置详细的治疗参数，确保治疗的精准性和安全性。

#### 验收标准

1. 当进入参数设置界面时，系统应显示人体部位图，支持点击选择治疗部位
2. 当选择治疗部位时，系统应高亮显示选中的部位并显示部位名称，每个部位颜色不一样，按照橙蓝绿先后显示
3. 当设置治疗参数时，系统应提供治疗时长（分钟）、声强（30/45/60 mW/cm²）、频率（100Hz/1000Hz）的输入
4. 当设置治疗参数时，系统应提供贴片类型选择（深部DEEP/浅层SHALLOW）和贴片数量设置（1-4个），其中治疗头1-10为上层，是浅部，11-20是下层，深部
5. 当参数设置完成后，系统应验证所有参数的有效性和安全范围
6. 当参数验证通过后，系统应保存治疗参数配置并进入下一步流程

### 需求 5 - 治疗头可用性检查（图版8、9）

**用户故事：** 作为医护人员，我希望系统能够检查治疗头的可用性，确保有足够的设备进行治疗。

#### 验收标准

1. 当开始治疗前，系统应通过串口查询所有治疗头的状态和数量，按上下层区分，因为推荐时需要按照上下层推荐
2. 当治疗头数量不足时，系统应显示"可用治疗头数量不足，请等待其他..."的提示信息
3. 当显示治疗头不足提示时，系统应显示当前可用治疗头数量和所需数量
4. 当治疗头数量充足时，系统应允许继续进行治疗流程
5. 当等待治疗头可用时，系统应提供刷新检查功能

### 需求 6 - 参数下载界面

**用户故事：** 作为医护人员，我希望能够将治疗参数下载到治疗头设备，支持带走治疗模式。

#### 验收标准

1. 当选择参数下载模式时，系统应显示当前设置的治疗参数
2. 当执行参数下载时，系统应通过TWSDT指令将参数发送到指定治疗头
3. 当参数下载完成后，系统应显示下载成功确认信息
4. 当参数下载失败时，系统应显示错误信息并允许重试
5. 当参数下载成功后，系统应记录treatment_mode为TAKE_AWAY

### 需求 7 - 贴片治疗头推荐和指导界面（图版10、11）

**用户故事：** 作为医护人员，我希望系统能够指导患者正确贴片，确保治疗效果。

#### 验收标准

1. 当进入治疗头推荐界面时，系统应通过TRZI指令查询所有治疗头状态
2. 当显示治疗头列表时，系统应显示治疗头编号、电量、使用次数、槽位号
3. 治疗头推荐，需要根据电量、使用次数、使用时间、深浅部对应等进行合理推荐
4. 当选择推荐治疗头时，系统应通过TWSC指令点亮对应治疗头指示灯，需要和部位显示颜色一致
5. 当取消选择时，系统应通过TWSN指令关闭治疗头指示灯
6. 当确认选择后，系统应记录选中的治疗头编号用于后续治疗
7. 当显示治疗头推荐，系统会根据现场治疗和取走治疗两种模式进行区分
8. 当贴片类型为深部时，系统应显示对应的深部贴片指导
9. 当贴片类型为浅层时，系统应显示对应的浅层贴片指导
10. 当贴片指导完成后，系统应提供确认按钮进入治疗阶段

### 需求 8 - 治疗进程管理界面（图版12、13、14）

**用户故事：** 作为医护人员，我希望能够监控和管理治疗进程，实时查看治疗状态和进度。

#### 验收标准

1. 当开始治疗时，系统应通过串口发送治疗参数到选定的治疗头并启动治疗
2. 当治疗进行中时，系统应实时显示治疗进度、剩余时间、治疗强度等信息
3. 当治疗进行中时，系统应支持终止当前治疗
4. 当治疗完成时，系统应显示"本阶段治疗已治疗完成！"的提示信息
5. 当治疗异常终止时，系统应记录终止原因并更新治疗状态
6. 当治疗结束后，系统应更新患者档案的统计数据和治疗历史

### 需求 9 - 进程管理界面（图版22）

**用户故事：** 作为医护人员，我希望能够查看和管理所有治疗进程的状态。

#### 验收标准

1. 当进入进程管理界面时，系统应显示所有治疗进程的列表
2. 当显示进程列表时，系统应显示患者就诊卡号、姓名、治疗部位、状态等信息
3. 当显示进程状态时，系统应用不同颜色区分状态（正在治疗-绿色、待取回-橙色等）
4. 当点击查看按钮时，系统应显示该进程的详细信息
5. 当进程状态发生变化时，系统应实时更新显示状态
6. 当查询进程时，系统应支持按患者卡号、姓名、状态等条件筛选


### 需求 10 - 治疗头管理界面（图版19）

**用户故事：** 作为设备管理员，我希望能够监控和管理所有治疗头设备的状态。

#### 验收标准

1. 当进入治疗头管理界面时，系统应显示所有治疗头的状态列表
2. 当显示治疗头信息时，系统应显示槽位号、状态、使用次数、使用时长、电量等信息
3. 当治疗头状态为充电中时，系统应显示绿色状态标识
4. 当治疗头使用次数接近最大限制时，系统应显示警告提示
5. 当查看治疗头详情时，系统应显示使用百分比和剩余可用次数
6. 当更新治疗头状态时，系统应实时同步treatment_heads表数据
7. 当治疗头状态异常时，系统应提供相应的处理建议

### 需求 11 - 串口硬件通信

**用户故事：** 作为系统，我需要通过串口与治疗头硬件进行可靠通信，确保指令准确传输。

#### 验收标准

1. 当系统启动时，系统应建立串口连接（波特率115200，停止位1，数据位8，校验位None）
2. 当发送TRZI指令时，系统应正确解析返回的治疗头数据（编号、电量、使用次数、槽位号）
3. 当发送TWSC指令时，系统应正确控制治疗头指示灯的颜色和状态
4. 当发送TWSN指令时，系统应正确关闭治疗头指示灯
5. 当发送TWSDT指令时，系统应正确传输治疗参数到指定治疗头
6. 当发送TWS指令时，系统应正确启动治疗头工作并监控状态
7. 当发送TWZO指令时，系统应正确关闭治疗头并确认停止
8. 当串口通信异常时，系统应提供错误处理和重连机制

