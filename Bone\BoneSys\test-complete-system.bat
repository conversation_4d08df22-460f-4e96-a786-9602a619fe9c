@echo off
echo ========================================
echo FREEBONE医疗系统 - 完整系统测试
echo ========================================
echo.

echo 1. 清理构建缓存...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo ❌ 清理失败
    pause
    exit /b 1
)

echo.
echo 2. 编译Java代码...
call gradlew compileJava
if %ERRORLEVEL% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo 3. 运行硬件通信测试...
call gradlew test --tests WebSocketHardwareTest
if %ERRORLEVEL% neq 0 (
    echo ❌ 硬件通信测试失败
    pause
    exit /b 1
)

echo.
echo 4. 运行指示灯控制测试...
call gradlew test --tests IndicatorLightControlTest
if %ERRORLEVEL% neq 0 (
    echo ❌ 指示灯控制测试失败
    pause
    exit /b 1
)

echo.
echo 5. 运行基础系统测试...
call gradlew test --tests BasicSystemTest
if %ERRORLEVEL% neq 0 (
    echo ❌ 基础系统测试失败
    pause
    exit /b 1
)

echo.
echo ✅ 所有测试通过！
echo.
echo 6. 启动后端服务...
echo 注意：请在另一个终端窗口中启动前端服务
echo 前端启动命令：cd ../Bone_Vue && npm run dev
echo.
echo 按任意键启动后端服务...
pause > nul

call gradlew bootRun
