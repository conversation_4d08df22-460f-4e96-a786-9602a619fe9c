package com.Bone.BoneSys.entity;

import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 治疗进程实体类
 * 对应数据库表：processes
 */
@Entity
@Table(name = "processes")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = {"record", "treatmentDetails"})
@ToString(exclude = {"record", "treatmentDetails"})
public class Process {
    
    /**
     * 进程ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    /**
     * 关联的档案
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "record_id", nullable = false)
    private Record record;
    
    /**
     * 治疗模式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "treatment_mode", nullable = false, length = 20)
    private TreatmentMode treatmentMode;
    
    /**
     * 进程状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ProcessStatus status;
    
    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 远端治疗是否已计数档案完成次数
     * 用于防止重复统计 sessions_completed_count
     */
    @Column(name = "remote_counted", nullable = false)
    private Boolean remoteCounted = false;
    
    /**
     * 进程下的治疗详情
     */
    @OneToMany(mappedBy = "process", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TreatmentDetail> treatmentDetails;
    
    /**
     * 设置开始时间
     */
    @PrePersist
    public void prePersist() {
        if (this.startTime == null) {
            this.startTime = LocalDateTime.now();
        }
        if (this.status == null) {
            this.status = ProcessStatus.IN_PROGRESS;
        }
        if (this.remoteCounted == null) {
            this.remoteCounted = false;
        }
    }
    
    /**
     * 计算总治疗时长（分钟）
     */
    @Transient
    public Integer getTotalDuration() {
        if (treatmentDetails == null || treatmentDetails.isEmpty()) {
            return 0;
        }
        return treatmentDetails.stream()
                .mapToInt(TreatmentDetail::getDuration)
                .sum();
    }
    
    /**
     * 判断是否可以终止
     */
    @Transient
    public Boolean canTerminate() {
        return status == ProcessStatus.IN_PROGRESS;
    }
}