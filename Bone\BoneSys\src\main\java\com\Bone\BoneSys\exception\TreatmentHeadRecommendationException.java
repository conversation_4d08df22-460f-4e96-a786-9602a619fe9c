package com.Bone.BoneSys.exception;

/**
 * 治疗头推荐异常基类
 * 用于处理治疗头推荐过程中的各种异常情况
 */
public class TreatmentHeadRecommendationException extends Exception {
    
    private String errorCode;
    private String userMessage;
    private String technicalMessage;
    
    public TreatmentHeadRecommendationException(String message) {
        super(message);
        this.userMessage = message;
        this.technicalMessage = message;
    }
    
    public TreatmentHeadRecommendationException(String message, Throwable cause) {
        super(message, cause);
        this.userMessage = message;
        this.technicalMessage = message;
    }
    
    public TreatmentHeadRecommendationException(String errorCode, String userMessage, String technicalMessage) {
        super(technicalMessage);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
        this.technicalMessage = technicalMessage;
    }
    
    public TreatmentHeadRecommendationException(String errorCode, String userMessage, String technicalMessage, Throwable cause) {
        super(technicalMessage, cause);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
        this.technicalMessage = technicalMessage;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getUserMessage() {
        return userMessage != null ? userMessage : getMessage();
    }
    
    public String getTechnicalMessage() {
        return technicalMessage != null ? technicalMessage : getMessage();
    }
    
    @Override
    public String toString() {
        return String.format("TreatmentHeadRecommendationException{errorCode='%s', userMessage='%s', technicalMessage='%s'}", 
                           errorCode, userMessage, technicalMessage);
    }
}