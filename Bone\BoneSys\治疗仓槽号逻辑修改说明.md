# 🔄 治疗仓槽号逻辑修改说明

## 📋 修改概述

将治疗仓槽号逻辑从**上下连续编号**改为**上下层独立编号**，上下层都是1-10，分别对应浅部和深部治疗头。

## 🔧 修改前后对比

### **修改前（旧逻辑）**
```
上仓（浅部）：槽号1-10  → 治疗头1-10
下仓（深部）：槽号11-20 → 治疗头11-20
```

### **修改后（新逻辑）**
```
上层（浅部）：槽号1-10 → 治疗头1-10
下层（深部）：槽号1-10 → 治疗头11-20
```

## 🗄️ 数据库结构修改

### **1. 新增字段**
```sql
ALTER TABLE treatment_heads 
ADD COLUMN compartment_type VARCHAR(10) NULL DEFAULT NULL 
COMMENT '仓位类型: SHALLOW(浅部上层), DEEP(深部下层)';
```

### **2. 修改约束**
```sql
-- 槽号约束改为1-10
ALTER TABLE treatment_heads 
ADD CONSTRAINT chk_head_slot_mapping CHECK (
  slot_number IS NULL OR slot_number BETWEEN 1 AND 10
);
```

### **3. 数据初始化**
```sql
-- 设置仓位类型
UPDATE treatment_heads SET compartment_type = 'SHALLOW' WHERE head_number BETWEEN 1 AND 10;
UPDATE treatment_heads SET compartment_type = 'DEEP' WHERE head_number BETWEEN 11 AND 20;
```

## 💻 代码修改

### **1. TreatmentHead实体类**
```java
@Entity
public class TreatmentHead {
    // 新增字段
    @Column(name = "compartment_type", length = 10)
    private String compartmentType; // SHALLOW/DEEP
    
    // 新增便利方法
    public Boolean isShallowCompartment() {
        return "SHALLOW".equals(compartmentType);
    }
    
    public Boolean isDeepCompartment() {
        return "DEEP".equals(compartmentType);
    }
}
```

### **2. TreatmentHeadRepository**
```java
// 新增查询方法
@Query("SELECT t FROM TreatmentHead t WHERE t.compartmentType = :compartmentType")
List<TreatmentHead> findByCompartmentType(@Param("compartmentType") String compartmentType);

@Query("SELECT t FROM TreatmentHead t WHERE t.compartmentType = 'SHALLOW'")
List<TreatmentHead> findShallowCompartmentHeads();

@Query("SELECT t FROM TreatmentHead t WHERE t.compartmentType = 'DEEP'")
List<TreatmentHead> findDeepCompartmentHeads();
```

### **3. TreatmentHeadInfo DTO**
```java
public class TreatmentHeadInfo {
    private String compartmentType; // SHALLOW/DEEP
    
    // 构造函数中的槽号映射逻辑
    if (headNumber >= 1 && headNumber <= 10) {
        this.compartmentType = "SHALLOW";
        this.slotNumber = headNumber; // 浅部：槽号1-10
    } else if (headNumber >= 11 && headNumber <= 20) {
        this.compartmentType = "DEEP";
        this.slotNumber = headNumber - 10; // 深部：槽号1-10 (11->1, 12->2, ..., 20->10)
    }
}
```

### **4. HardwareCommandParser**
```java
// 槽号转换逻辑
int originalSlotNumber = Integer.parseInt(headsData.substring(currentIndex, currentIndex + 2));

int slotNumber;
String compartmentType;
if (headNumber >= 1 && headNumber <= 10) {
    // 浅部治疗头：槽号保持1-10
    compartmentType = "SHALLOW";
    slotNumber = originalSlotNumber;
} else if (headNumber >= 11 && headNumber <= 20) {
    // 深部治疗头：槽号转换为1-10
    compartmentType = "DEEP";
    slotNumber = originalSlotNumber > 10 ? originalSlotNumber - 10 : originalSlotNumber;
}
```

### **5. TreatmentHeadRecommendationService**
```java
// 修改判断方法
private boolean isShallowHead(TreatmentHeadInfo head) {
    return head.isShallowCompartment(); // 基于compartmentType而非编号
}

private boolean isDeepHead(TreatmentHeadInfo head) {
    return head.isDeepCompartment(); // 基于compartmentType而非编号
}
```

## 🔄 槽号映射逻辑

### **硬件到系统的映射**
```java
// 硬件返回的TRZI响应中的槽号转换
原始槽号 → 系统槽号 → 仓位类型
1-10    → 1-10     → SHALLOW (上层浅部)
11-20   → 1-10     → DEEP    (下层深部)
```

### **系统内部的表示**
```java
治疗头编号 → 仓位类型 → 槽号范围
1-10     → SHALLOW → 1-10
11-20    → DEEP    → 1-10
```

## 📊 数据示例

### **修改后的数据结构**
| 治疗头编号 | 槽号 | 仓位类型 | 仓位描述 |
|-----------|------|----------|----------|
| 1         | 1    | SHALLOW  | 上层浅部 |
| 2         | 2    | SHALLOW  | 上层浅部 |
| ...       | ...  | ...      | ...      |
| 10        | 10   | SHALLOW  | 上层浅部 |
| 11        | 1    | DEEP     | 下层深部 |
| 12        | 2    | DEEP     | 下层深部 |
| ...       | ...  | ...      | ...      |
| 20        | 10   | DEEP     | 下层深部 |

### **API响应示例**
```json
{
  "headNumber": 15,
  "slotNumber": 5,
  "compartmentType": "DEEP",
  "compartmentDescription": "下层深部",
  "fullSlotDescription": "下层深部 - 槽位5"
}
```

## 🎯 推荐逻辑影响

### **贴片类型映射**
```java
// 浅部贴片 → 上层治疗头
"SHALLOW" → compartmentType = "SHALLOW" → 治疗头1-10 → 槽号1-10

// 深部贴片 → 下层治疗头  
"DEEP" → compartmentType = "DEEP" → 治疗头11-20 → 槽号1-10
```

### **推荐显示**
```java
// 推荐结果中的仓位描述
isShallowCompartment() → "上层浅部"
isDeepCompartment()    → "下层深部"
```

## 🚀 部署步骤

### **1. 数据库升级**
```bash
# 执行SQL脚本
mysql -u root -p bonesys < SQL/修改槽号逻辑.sql
```

### **2. 应用重启**
```bash
# 重启Spring Boot应用
./gradlew bootRun
```

### **3. 验证测试**
```bash
# 运行测试脚本
测试槽号逻辑修改.bat
```

## ✅ 验证要点

1. **数据库结构**：新增compartment_type字段，槽号约束1-10
2. **数据完整性**：治疗头1-10为SHALLOW，11-20为DEEP
3. **API响应**：包含正确的compartmentType和槽号
4. **推荐逻辑**：浅部/深部贴片推荐正确的治疗头
5. **硬件通信**：槽号转换逻辑正确处理硬件响应

## 🔍 故障排查

### **数据库问题**
- 检查字段是否正确添加
- 验证约束是否生效
- 确认数据是否正确更新

### **API问题**
- 检查实体类字段映射
- 验证Repository查询方法
- 确认DTO转换逻辑

### **推荐问题**
- 检查isShallowHead/isDeepHead方法
- 验证compartmentType设置
- 确认槽号转换逻辑

---

## 🎉 修改完成

现在治疗仓槽号逻辑已经成功修改为上下层独立编号（1-10），分别对应浅部和深部治疗头，提供了更清晰和直观的槽位管理方式！
