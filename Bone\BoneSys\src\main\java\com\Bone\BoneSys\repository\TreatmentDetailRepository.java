package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.TreatmentDetail;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 治疗详情数据访问接口
 */
@Repository
public interface TreatmentDetailRepository extends JpaRepository<TreatmentDetail, Long> {
    
    /**
     * 根据进程ID查找治疗详情
     */
    List<TreatmentDetail> findByProcessId(Long processId);
    
    /**
     * 根据治疗头编号查找治疗详情
     */
    List<TreatmentDetail> findByHeadNumberUsed(Integer headNumber);
    
    /**
     * 根据状态查找治疗详情
     */
    List<TreatmentDetail> findByStatus(TreatmentDetailStatus status);
    
    /**
     * 根据治疗部位查找治疗详情
     */
    List<TreatmentDetail> findByBodyPart(String bodyPart);
    
    /**
     * 查找正在治疗的详情
     */
    List<TreatmentDetail> findByStatusOrderByIdDesc(TreatmentDetailStatus status);
    
    /**
     * 根据患者ID查找治疗详情
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r WHERE r.patient.id = :patientId")
    List<TreatmentDetail> findByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 根据患者ID查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r WHERE r.patient.id = :patientId")
    Page<TreatmentDetail> findByPatientId(@Param("patientId") Long patientId, Pageable pageable);

    /**
     * 根据患者ID和状态列表查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r " +
           "WHERE r.patient.id = :patientId AND td.status IN :statuses")
    Page<TreatmentDetail> findByPatientIdAndStatusIn(@Param("patientId") Long patientId,
                                                     @Param("statuses") List<TreatmentDetailStatus> statuses,
                                                     Pageable pageable);
    
    /**
     * 根据患者ID和治疗部位查找治疗详情
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r " +
           "WHERE r.patient.id = :patientId AND td.bodyPart = :bodyPart")
    List<TreatmentDetail> findByPatientIdAndBodyPart(@Param("patientId") Long patientId, @Param("bodyPart") String bodyPart);
    
    /**
     * 统计治疗头使用情况
     */
    @Query("SELECT td.headNumberUsed, COUNT(td), SUM(td.duration) FROM TreatmentDetail td " +
           "GROUP BY td.headNumberUsed ORDER BY COUNT(td) DESC")
    List<Object[]> getTreatmentHeadUsageStats();
    
    /**
     * 统计治疗部位使用情况
     */
    @Query("SELECT td.bodyPart, COUNT(td), SUM(td.duration) FROM TreatmentDetail td " +
           "GROUP BY td.bodyPart ORDER BY COUNT(td) DESC")
    List<Object[]> getBodyPartUsageStats();
    
    /**
     * 查找等待取回的治疗详情
     */
    @Query("SELECT td FROM TreatmentDetail td WHERE td.status = 'AWAITING_RETURN' ORDER BY td.id DESC")
    Page<TreatmentDetail> findAwaitingReturnDetails(Pageable pageable);
    
    /**
     * 根据档案ID统计治疗详情
     */
    @Query("SELECT COUNT(td), SUM(td.duration) FROM TreatmentDetail td JOIN td.process p WHERE p.record.id = :recordId")
    Object[] getStatsByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据状态列表查找治疗详情（分页）
     */
    Page<TreatmentDetail> findByStatusIn(List<TreatmentDetailStatus> statuses, Pageable pageable);

    /**
     * 根据患者卡号和状态列表查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r " +
           "WHERE r.patient.patientCardId LIKE %:cardId% AND td.status IN :statuses")
    Page<TreatmentDetail> findByPatientCardIdContainingAndStatusIn(
        @Param("cardId") String cardId, @Param("statuses") List<TreatmentDetailStatus> statuses, Pageable pageable);

    /**
     * 根据患者姓名和状态列表查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r " +
           "WHERE r.patient.name LIKE %:patientName% AND td.status IN :statuses")
    Page<TreatmentDetail> findByPatientNameContainingAndStatusIn(
        @Param("patientName") String patientName, @Param("statuses") List<TreatmentDetailStatus> statuses, Pageable pageable);

    /**
     * 根据状态查找治疗详情（分页）
     */
    Page<TreatmentDetail> findByStatus(TreatmentDetailStatus status, Pageable pageable);

    /**
     * 根据治疗详情状态和进程状态查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p " +
           "WHERE td.status IN :detailStatuses AND p.status IN :processStatuses")
    Page<TreatmentDetail> findByStatusInAndProcessStatusIn(
        @Param("detailStatuses") List<TreatmentDetailStatus> detailStatuses,
        @Param("processStatuses") List<ProcessStatus> processStatuses,
        Pageable pageable);

    /**
     * 根据患者卡号、治疗详情状态和进程状态查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r " +
           "WHERE r.patient.patientCardId LIKE %:cardId% AND td.status IN :detailStatuses AND p.status IN :processStatuses")
    Page<TreatmentDetail> findByPatientCardIdContainingAndStatusInAndProcessStatusIn(
        @Param("cardId") String cardId,
        @Param("detailStatuses") List<TreatmentDetailStatus> detailStatuses,
        @Param("processStatuses") List<ProcessStatus> processStatuses,
        Pageable pageable);

    /**
     * 根据患者姓名、治疗详情状态和进程状态查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p JOIN p.record r " +
           "WHERE r.patient.name LIKE %:patientName% AND td.status IN :detailStatuses AND p.status IN :processStatuses")
    Page<TreatmentDetail> findByPatientNameContainingAndStatusInAndProcessStatusIn(
        @Param("patientName") String patientName,
        @Param("detailStatuses") List<TreatmentDetailStatus> detailStatuses,
        @Param("processStatuses") List<ProcessStatus> processStatuses,
        Pageable pageable);

    /**
     * 根据治疗详情状态和进程状态查找治疗详情（分页）
     */
    @Query("SELECT td FROM TreatmentDetail td JOIN td.process p " +
           "WHERE td.status = :detailStatus AND p.status IN :processStatuses")
    Page<TreatmentDetail> findByStatusAndProcessStatusIn(
        @Param("detailStatus") TreatmentDetailStatus detailStatus,
        @Param("processStatuses") List<ProcessStatus> processStatuses,
        Pageable pageable);
}