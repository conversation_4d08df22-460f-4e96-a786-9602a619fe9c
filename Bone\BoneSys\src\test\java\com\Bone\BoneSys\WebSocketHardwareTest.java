package com.Bone.BoneSys;

import com.Bone.BoneSys.service.WebSocketHardwareService;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.service.HardwareCommandParser;
import com.Bone.BoneSys.dto.hardware.TreatmentParamsRequest;
import com.Bone.BoneSys.dto.hardware.TreatmentHeadLightRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * WebSocket硬件连接测试
 * 用于测试与真实硬件的WebSocket通信
 * 
 * 注意：这些测试需要真实的硬件设备连接，默认禁用
 * 要运行测试，请移除@Disabled注解并确保硬件设备已连接
 */
@SpringBootTest
@ActiveProfiles("test")
@Disabled("需要真实硬件设备，默认禁用")
public class WebSocketHardwareTest {

    @Autowired
    private WebSocketHardwareService webSocketHardwareService;
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private HardwareCommandParser commandParser;

    @BeforeEach
    void setUp() {
        System.out.println("=== WebSocket硬件连接测试开始 ===");
        System.out.println("测试前请确保：");
        System.out.println("1. 硬件设备已连接并启动");
        System.out.println("2. WebSocket服务器运行在 ws://122.51.229.122:6123");
        System.out.println("3. 治疗头已正确放置在治疗仓中");
        System.out.println("=====================================");
    }

    /**
     * 测试1：WebSocket连接测试
     */
    @Test
    void testWebSocketConnection() {
        try {
            System.out.println("\n【测试1】WebSocket连接测试");
            
            // 检查连接状态
            boolean isConnected = webSocketHardwareService.isConnected();
            System.out.println("WebSocket连接状态: " + (isConnected ? "已连接" : "未连接"));
            
            if (!isConnected) {
                System.out.println("尝试连接到WebSocket服务器...");
                webSocketHardwareService.connect();
                isConnected = webSocketHardwareService.isConnected();
                System.out.println("连接结果: " + (isConnected ? "连接成功" : "连接失败"));
            }
            
            assert isConnected : "WebSocket连接失败";
            System.out.println("✅ WebSocket连接测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ WebSocket连接测试失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 测试2：TRZI指令测试 - 查询治疗头信息
     */
    @Test
    void testTRZICommand() {
        try {
            System.out.println("\n【测试2】TRZI指令测试 - 查询治疗头信息");
            
            // 构建TRZI指令
            String command = commandParser.buildQueryAllTreatmentHeadsCommand();
            System.out.println("发送指令: " + command.replace("\r\n", "\\r\\n"));
            
            // 发送指令
            String response = webSocketHardwareService.sendCommand(command);
            System.out.println("收到响应: " + response.replace("\r\n", "\\r\\n"));
            
            // 解析响应
            var treatmentHeads = commandParser.parseQueryAllTreatmentHeadsResponse(response);
            System.out.println("解析结果: 找到 " + treatmentHeads.size() + " 个治疗头");
            
            for (var head : treatmentHeads) {
                System.out.println("  - 治疗头" + head.getHeadNumber() + 
                                 ": 电量=" + head.getBatteryLevel() + 
                                 "%, 槽位=" + head.getSlotNumber());
            }
            
            assert !treatmentHeads.isEmpty() : "未找到任何治疗头";
            System.out.println("✅ TRZI指令测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ TRZI指令测试失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 测试3：TWSC指令测试 - 点亮治疗头指示灯
     */
    @Test
    void testTWSCCommand() throws Exception {
        try {
            System.out.println("\n【测试3】TWSC指令测试 - 点亮治疗头指示灯");
            
            // 准备指示灯请求（点亮治疗头1和2，颜色代码1=红色）
            List<TreatmentHeadLightRequest> lightRequests = Arrays.asList(
                new TreatmentHeadLightRequest(1, 1), // 治疗头1，红色
                new TreatmentHeadLightRequest(2, 2)  // 治疗头2，绿色
            );
            
            // 构建TWSC指令
            String command = commandParser.buildLightUpCommand(lightRequests);
            System.out.println("发送指令: " + command.replace("\r\n", "\\r\\n"));
            
            // 发送指令
            String response = webSocketHardwareService.sendCommand(command);
            System.out.println("收到响应: " + response.replace("\r\n", "\\r\\n"));
            
            // 解析响应
            var lightResponses = commandParser.parseLightUpResponse(response);
            System.out.println("解析结果: " + lightResponses.size() + " 个治疗头指示灯已点亮");
            
            for (var lightResp : lightResponses) {
                System.out.println("  - 治疗头" + lightResp.getHeadNumber() + 
                                 ": 颜色=" + lightResp.getColorCode() + 
                                 ", 槽位=" + lightResp.getSlotNumber());
            }
            
            System.out.println("请观察治疗头指示灯是否已点亮（等待5秒）...");
            Thread.sleep(5000);
            
            assert lightResponses.size() == lightRequests.size() : "指示灯响应数量不匹配";
            System.out.println("✅ TWSC指令测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ TWSC指令测试失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 测试4：TWSN指令测试 - 关闭治疗头指示灯
     */
    @Test
    void testTWSNCommand() throws Exception {
        try {
            System.out.println("\n【测试4】TWSN指令测试 - 关闭治疗头指示灯");
            
            // 准备要关闭的治疗头编号
            List<Integer> headNumbers = Arrays.asList(1, 2);
            
            // 构建TWSN指令
            String command = commandParser.buildTurnOffLightCommand(headNumbers);
            System.out.println("发送指令: " + command.replace("\r\n", "\\r\\n"));
            
            // 发送指令
            String response = webSocketHardwareService.sendCommand(command);
            System.out.println("收到响应: " + response.replace("\r\n", "\\r\\n"));
            
            // 解析响应
            var turnedOffHeads = commandParser.parseTurnOffLightResponse(response);
            System.out.println("解析结果: " + turnedOffHeads.size() + " 个治疗头指示灯已关闭");
            
            for (var headNumber : turnedOffHeads) {
                System.out.println("  - 治疗头" + headNumber + " 指示灯已关闭");
            }
            
            System.out.println("请观察治疗头指示灯是否已关闭（等待3秒）...");
            Thread.sleep(3000);
            
            assert turnedOffHeads.size() == headNumbers.size() : "关闭指示灯响应数量不匹配";
            System.out.println("✅ TWSN指令测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ TWSN指令测试失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 测试5：TWSDT指令测试 - 发送治疗参数（不启动治疗）
     */
    @Test
    void testTWSDTCommand() throws Exception {
        try {
            System.out.println("\n【测试5】TWSDT指令测试 - 发送治疗参数");
            
            // 准备治疗参数（20分钟，500mW/cm²，1000Hz，治疗头1和2）
            TreatmentParamsRequest paramsRequest = new TreatmentParamsRequest(
                20, 500, 1000, Arrays.asList(1, 2));
            
            // 构建TWSDT指令
            String command = commandParser.buildSendTreatmentParamsCommand(paramsRequest);
            System.out.println("发送指令: " + command.replace("\r\n", "\\r\\n"));
            
            // 发送指令
            String response = webSocketHardwareService.sendCommand(command);
            System.out.println("收到响应: " + response.replace("\r\n", "\\r\\n"));
            
            // 验证响应
            boolean success = commandParser.validateSendTreatmentParamsResponse(response, paramsRequest);
            System.out.println("参数发送结果: " + (success ? "成功" : "失败"));
            
            assert success : "治疗参数发送失败";
            System.out.println("✅ TWSDT指令测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ TWSDT指令测试失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 测试6：TWZS指令测试 - 发送治疗参数并启动治疗
     */
    @Test
    void testTWZSCommand() throws Exception {
        try {
            System.out.println("\n【测试6】TWZS指令测试 - 发送治疗参数并启动治疗");
            System.out.println("⚠️  警告：此测试将启动真实的治疗过程，请确保安全！");
            
            // 构建TWZS指令（治疗头1，5分钟，300mW/cm²，100Hz）
            String command = commandParser.buildStartTreatmentCommand(1, 5, 300, 100);
            System.out.println("发送指令: " + command.replace("\r\n", "\\r\\n"));
            
            // 发送指令
            String response = webSocketHardwareService.sendCommand(command);
            System.out.println("收到响应: " + response.replace("\r\n", "\\r\\n"));
            
            // 验证响应
            boolean success = commandParser.validateStartTreatmentResponse(response, 1, 5, 300, 100);
            System.out.println("治疗启动结果: " + (success ? "成功" : "失败"));
            
            if (success) {
                System.out.println("治疗已启动，等待10秒后停止...");
                Thread.sleep(10000);
                
                // 发送TWZO指令停止治疗
                String stopCommand = commandParser.buildStopTreatmentCommand(1);
                System.out.println("发送停止指令: " + stopCommand.replace("\r\n", "\\r\\n"));
                
                String stopResponse = webSocketHardwareService.sendCommand(stopCommand);
                System.out.println("停止响应: " + stopResponse.replace("\r\n", "\\r\\n"));
                
                int stoppedHead = commandParser.parseStopTreatmentResponse(stopResponse);
                System.out.println("已停止治疗头: " + stoppedHead);
            }
            
            assert success : "治疗启动失败";
            System.out.println("✅ TWZS指令测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ TWZS指令测试失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 测试7：完整流程测试
     */
    @Test
    void testCompleteWorkflow() throws Exception {
        try {
            System.out.println("\n【测试7】完整流程测试");
            System.out.println("模拟完整的治疗流程：查询→推荐→参数发送→启动→停止");
            
            // 1. 查询治疗头
            System.out.println("\n步骤1: 查询治疗头信息");
            var treatmentHeads = hardwareService.syncAllTreatmentHeads();
            System.out.println("找到 " + treatmentHeads.size() + " 个治疗头");
            
            // 2. 点亮推荐治疗头
            System.out.println("\n步骤2: 点亮推荐治疗头指示灯");
            List<TreatmentHeadLightRequest> lightRequests = Arrays.asList(
                new TreatmentHeadLightRequest(1, 1),
                new TreatmentHeadLightRequest(2, 2)
            );
            var lightResponses = hardwareService.lightUpTreatmentHeads(lightRequests);
            boolean lightSuccess = !lightResponses.isEmpty();
            System.out.println("指示灯点亮: " + (lightSuccess ? "成功" : "失败"));
            
            Thread.sleep(3000); // 等待3秒观察指示灯
            
            // 3. 发送治疗参数
            System.out.println("\n步骤3: 发送治疗参数");
            TreatmentParamsRequest paramsRequest = new TreatmentParamsRequest(
                10, 400, 1000, Arrays.asList(1, 2));
            boolean paramsSuccess = hardwareService.sendTreatmentParams(paramsRequest);
            System.out.println("参数发送: " + (paramsSuccess ? "成功" : "失败"));
            
            // 4. 关闭指示灯
            System.out.println("\n步骤4: 关闭推荐治疗头指示灯");
            List<Integer> turnOffSuccess = hardwareService.turnOffTreatmentHeadLights(Arrays.asList(1, 2));
            System.out.println("指示灯关闭: " + turnOffSuccess.size() + " 个治疗头");
            
            assert lightSuccess && paramsSuccess && !turnOffSuccess.isEmpty() : "完整流程测试失败";
            System.out.println("✅ 完整流程测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ 完整流程测试失败: " + e.getMessage());
            throw e;
        }
    }
}
