package com.Bone.BoneSys.service;

import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.repository.*;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计数据计算服务
 * 负责各种治疗数据的统计计算和分析
 */
@Service
public class StatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsService.class);

    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;

    /**
     * 获取患者治疗统计数据
     */
    public PatientTreatmentStatistics getPatientTreatmentStatistics(Long patientId) {
        logger.info("Calculating treatment statistics for patient {}", patientId);
        
        PatientTreatmentStatistics stats = new PatientTreatmentStatistics();
        stats.setPatientId(patientId);
        
        // 获取患者基本信息
        Optional<Patient> patientOpt = patientRepository.findById(patientId);
        if (patientOpt.isEmpty()) {
            logger.warn("Patient {} not found", patientId);
            return stats;
        }
        Patient patient = patientOpt.get();
        stats.setPatientName(patient.getName());
        stats.setPatientCardId(patient.getPatientCardId());
        
        // 获取患者档案
        List<com.Bone.BoneSys.entity.Record> records = recordRepository.findByPatientId(patientId);
        stats.setTotalRecords(records.size());

        // 计算治疗进程统计
        List<com.Bone.BoneSys.entity.Process> processes = new ArrayList<>();
        for (com.Bone.BoneSys.entity.Record record : records) {
            processes.addAll(processRepository.findByRecordId(record.getId()));
        }
        
        stats.setTotalProcesses(processes.size());
        stats.setCompletedProcesses((int) processes.stream()
            .filter(p -> p.getStatus().name().equals("COMPLETED")).count());
        stats.setInProgressProcesses((int) processes.stream()
            .filter(p -> p.getStatus().name().equals("IN_PROGRESS")).count());
        stats.setCancelledProcesses((int) processes.stream()
            .filter(p -> p.getStatus().name().equals("CANCELLED")).count());
        
        // 计算治疗详情统计
        List<TreatmentDetail> details = treatmentDetailRepository.findByPatientId(patientId);
        stats.setTotalTreatmentDetails(details.size());
        stats.setTotalTreatmentDuration(details.stream()
            .mapToInt(TreatmentDetail::getDuration).sum());
        
        // 计算部位统计
        List<BodyPartStat> bodyPartStats = bodyPartStatRepository.findByPatientId(patientId);
        stats.setBodyPartStatistics(bodyPartStats.stream()
            .map(this::convertToBodyPartStatistics)
            .collect(Collectors.toList()));
        
        // 计算最常用部位
        stats.setMostUsedBodyPart(bodyPartStats.stream()
            .max(Comparator.comparing(BodyPartStat::getTotalUsageCount))
            .map(BodyPartStat::getBodyPart)
            .orElse("无"));
        
        // 计算治疗频率统计
        Map<Integer, Long> frequencyStats = details.stream()
            .collect(Collectors.groupingBy(TreatmentDetail::getFrequency, Collectors.counting()));
        stats.setFrequencyStatistics(frequencyStats);
        
        // 计算强度统计
        Map<String, Long> intensityStats = details.stream()
            .collect(Collectors.groupingBy(
                d -> d.getIntensity().toString() + "档位", 
                Collectors.counting()));
        stats.setIntensityStatistics(intensityStats);
        
        logger.info("Completed statistics calculation for patient {}", patientId);
        return stats;
    }

    /**
     * 获取全局治疗统计数据
     */
    public GlobalTreatmentStatistics getGlobalTreatmentStatistics() {
        logger.info("Calculating global treatment statistics");
        
        GlobalTreatmentStatistics stats = new GlobalTreatmentStatistics();
        
        // 基础数据统计
        stats.setTotalPatients(patientRepository.count());
        stats.setTotalRecords(recordRepository.count());
        stats.setTotalProcesses(processRepository.count());
        stats.setTotalTreatmentDetails(treatmentDetailRepository.count());
        
        // 治疗头使用统计
        List<Object[]> headUsageStats = treatmentDetailRepository.getTreatmentHeadUsageStats();
        List<TreatmentHeadUsageStatistics> headStats = headUsageStats.stream()
            .map(row -> new TreatmentHeadUsageStatistics(
                (Integer) row[0], // headNumber
                ((Number) row[1]).longValue(), // usageCount
                ((Number) row[2]).longValue() // totalDuration
            ))
            .collect(Collectors.toList());
        stats.setTreatmentHeadUsageStatistics(headStats);
        
        // 部位使用统计
        List<Object[]> bodyPartUsageStats = treatmentDetailRepository.getBodyPartUsageStats();
        List<BodyPartUsageStatistics> bodyPartStats = bodyPartUsageStats.stream()
            .map(row -> new BodyPartUsageStatistics(
                (String) row[0], // bodyPart
                ((Number) row[1]).longValue(), // usageCount
                ((Number) row[2]).longValue() // totalDuration
            ))
            .collect(Collectors.toList());
        stats.setBodyPartUsageStatistics(bodyPartStats);
        
        // 进程状态统计
        List<Object[]> processStatusStats = processRepository.countByStatus();
        Map<String, Long> processStatusMap = processStatusStats.stream()
            .collect(Collectors.toMap(
                row -> row[0].toString(),
                row -> ((Number) row[1]).longValue()
            ));
        stats.setProcessStatusStatistics(processStatusMap);
        
        // 治疗模式统计
        List<Object[]> treatmentModeStats = processRepository.countByTreatmentMode();
        Map<String, Long> treatmentModeMap = treatmentModeStats.stream()
            .collect(Collectors.toMap(
                row -> row[0].toString(),
                row -> ((Number) row[1]).longValue()
            ));
        stats.setTreatmentModeStatistics(treatmentModeMap);
        
        logger.info("Completed global statistics calculation");
        return stats;
    }

    /**
     * 获取部位治疗统计数据
     */
    public BodyPartTreatmentStatistics getBodyPartTreatmentStatistics(String bodyPart) {
        logger.info("Calculating treatment statistics for body part: {}", bodyPart);
        
        BodyPartTreatmentStatistics stats = new BodyPartTreatmentStatistics();
        stats.setBodyPart(bodyPart);
        
        // 获取该部位的所有统计记录
        List<BodyPartStat> bodyPartStats = bodyPartStatRepository.findByBodyPart(bodyPart);
        
        stats.setTotalPatients(bodyPartStats.size());
        stats.setTotalUsageCount(bodyPartStats.stream()
            .mapToLong(BodyPartStat::getTotalUsageCount).sum());
        stats.setTotalDurationMinutes(bodyPartStats.stream()
            .mapToLong(BodyPartStat::getTotalDurationMinutes).sum());
        
        // 计算平均使用情况
        if (!bodyPartStats.isEmpty()) {
            stats.setAverageUsagePerPatient(stats.getTotalUsageCount() / (double) stats.getTotalPatients());
            stats.setAverageDurationPerPatient(stats.getTotalDurationMinutes() / (double) stats.getTotalPatients());
        }
        
        // 获取该部位的治疗详情
        List<TreatmentDetail> details = treatmentDetailRepository.findByBodyPart(bodyPart);
        
        // 强度分布统计
        Map<String, Long> intensityDistribution = details.stream()
            .collect(Collectors.groupingBy(
                d -> d.getIntensity().toString() + "档位",
                Collectors.counting()
            ));
        stats.setIntensityDistribution(intensityDistribution);
        
        // 频率分布统计
        Map<Integer, Long> frequencyDistribution = details.stream()
            .collect(Collectors.groupingBy(TreatmentDetail::getFrequency, Collectors.counting()));
        stats.setFrequencyDistribution(frequencyDistribution);
        
        // 治疗头使用分布
        Map<Integer, Long> headUsageDistribution = details.stream()
            .collect(Collectors.groupingBy(TreatmentDetail::getHeadNumberUsed, Collectors.counting()));
        stats.setHeadUsageDistribution(headUsageDistribution);
        
        logger.info("Completed statistics calculation for body part: {}", bodyPart);
        return stats;
    }

    /**
     * 获取时间段内的治疗统计
     */
    public TimePeriodStatistics getTimePeriodStatistics(LocalDate startDate, LocalDate endDate) {
        logger.info("Calculating statistics for period: {} to {}", startDate, endDate);
        
        TimePeriodStatistics stats = new TimePeriodStatistics();
        stats.setStartDate(startDate);
        stats.setEndDate(endDate);
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        // 该时间段内的进程 - 使用分页查询
        List<com.Bone.BoneSys.entity.Process> processes = processRepository.findByStartTimeBetween(
            startDateTime, endDateTime, org.springframework.data.domain.Pageable.unpaged()).getContent();
        stats.setTotalProcesses(processes.size());

        // 按日期分组统计
        Map<LocalDate, Long> dailyProcessCount = processes.stream()
            .collect(Collectors.groupingBy(
                p -> p.getStartTime().toLocalDate(),
                Collectors.counting()
            ));
        stats.setDailyProcessCount(dailyProcessCount);

        // 治疗详情统计
        List<TreatmentDetail> details = new ArrayList<>();
        for (com.Bone.BoneSys.entity.Process process : processes) {
            details.addAll(treatmentDetailRepository.findByProcessId(process.getId()));
        }
        
        stats.setTotalTreatmentDetails(details.size());
        stats.setTotalTreatmentDuration(details.stream()
            .mapToInt(TreatmentDetail::getDuration).sum());
        
        // 部位使用统计
        Map<String, Long> bodyPartUsage = details.stream()
            .collect(Collectors.groupingBy(TreatmentDetail::getBodyPart, Collectors.counting()));
        stats.setBodyPartUsage(bodyPartUsage);
        
        logger.info("Completed statistics calculation for time period");
        return stats;
    }

    /**
     * 转换BodyPartStat为统计DTO
     */
    private BodyPartStatistics convertToBodyPartStatistics(BodyPartStat stat) {
        BodyPartStatistics dto = new BodyPartStatistics();
        dto.setBodyPart(stat.getBodyPart());
        dto.setTotalUsageCount(stat.getTotalUsageCount().longValue());
        dto.setTotalDurationMinutes(stat.getTotalDurationMinutes().longValue());
        dto.setRecordId(stat.getRecord().getId());
        return dto;
    }

    // DTO类定义
    @Data
    public static class PatientTreatmentStatistics {
        private Long patientId;
        private String patientName;
        private String patientCardId;
        private Integer totalRecords;
        private Integer totalProcesses;
        private Integer completedProcesses;
        private Integer inProgressProcesses;
        private Integer cancelledProcesses;
        private Integer totalTreatmentDetails;
        private Integer totalTreatmentDuration;
        private String mostUsedBodyPart;
        private List<BodyPartStatistics> bodyPartStatistics;
        private Map<Integer, Long> frequencyStatistics;
        private Map<String, Long> intensityStatistics;
    }

    @Data
    public static class GlobalTreatmentStatistics {
        private Long totalPatients;
        private Long totalRecords;
        private Long totalProcesses;
        private Long totalTreatmentDetails;
        private List<TreatmentHeadUsageStatistics> treatmentHeadUsageStatistics;
        private List<BodyPartUsageStatistics> bodyPartUsageStatistics;
        private Map<String, Long> processStatusStatistics;
        private Map<String, Long> treatmentModeStatistics;
    }

    @Data
    public static class BodyPartTreatmentStatistics {
        private String bodyPart;
        private Integer totalPatients;
        private Long totalUsageCount;
        private Long totalDurationMinutes;
        private Double averageUsagePerPatient;
        private Double averageDurationPerPatient;
        private Map<String, Long> intensityDistribution;
        private Map<Integer, Long> frequencyDistribution;
        private Map<Integer, Long> headUsageDistribution;
    }

    @Data
    public static class TimePeriodStatistics {
        private LocalDate startDate;
        private LocalDate endDate;
        private Integer totalProcesses;
        private Integer totalTreatmentDetails;
        private Integer totalTreatmentDuration;
        private Map<LocalDate, Long> dailyProcessCount;
        private Map<String, Long> bodyPartUsage;
    }

    @Data
    public static class BodyPartStatistics {
        private String bodyPart;
        private Long totalUsageCount;
        private Long totalDurationMinutes;
        private Long recordId;
    }

    @Data
    public static class TreatmentHeadUsageStatistics {
        private Integer headNumber;
        private Long usageCount;
        private Long totalDuration;
        
        public TreatmentHeadUsageStatistics(Integer headNumber, Long usageCount, Long totalDuration) {
            this.headNumber = headNumber;
            this.usageCount = usageCount;
            this.totalDuration = totalDuration;
        }
    }

    @Data
    public static class BodyPartUsageStatistics {
        private String bodyPart;
        private Long usageCount;
        private Long totalDuration;
        
        public BodyPartUsageStatistics(String bodyPart, Long usageCount, Long totalDuration) {
            this.bodyPart = bodyPart;
            this.usageCount = usageCount;
            this.totalDuration = totalDuration;
        }
    }
}
