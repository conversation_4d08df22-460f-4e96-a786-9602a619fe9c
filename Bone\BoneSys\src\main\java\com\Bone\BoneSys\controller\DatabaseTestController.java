package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库测试控制器
 * 用于测试数据库连接和Repository是否正常工作
 */
@RestController
@RequestMapping("/api/database")
public class DatabaseTestController {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;
    
    @GetMapping("/test")
    public ApiResponse<Map<String, Object>> testDatabase() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试数据库连接
            try (Connection connection = dataSource.getConnection()) {
                result.put("databaseConnection", "SUCCESS");
                result.put("databaseUrl", connection.getMetaData().getURL());
                result.put("databaseProduct", connection.getMetaData().getDatabaseProductName());
            }
            
            // 测试Repository
            result.put("userCount", userRepository.count());
            result.put("patientCount", patientRepository.count());
            result.put("recordCount", recordRepository.count());
            result.put("treatmentHeadCount", treatmentHeadRepository.count());
            result.put("processCount", processRepository.count());
            result.put("treatmentDetailCount", treatmentDetailRepository.count());
            result.put("bodyPartStatCount", bodyPartStatRepository.count());
            
            result.put("status", "ALL_TESTS_PASSED");
            
            return ApiResponse.success("数据库测试通过", result);
            
        } catch (Exception e) {
            result.put("status", "TEST_FAILED");
            result.put("error", e.getMessage());
            return ApiResponse.error(500, "数据库测试失败", result);
        }
    }
}