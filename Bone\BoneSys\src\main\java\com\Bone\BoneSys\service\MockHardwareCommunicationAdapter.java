package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 模拟硬件通信适配器
 * 用于在没有真实硬件的情况下测试完整的治疗流程
 * 通过配置 hardware.communication.mode=mock 启用
 */
@Service
@ConditionalOnProperty(name = "hardware.communication.mode", havingValue = "mock", matchIfMissing = false)
public class MockHardwareCommunicationAdapter implements HardwareCommunicationInterface {
    
    private static final Logger logger = LoggerFactory.getLogger(MockHardwareCommunicationAdapter.class);
    
    @Autowired(required = false)
    private HardwareSimulatorService hardwareSimulator;
    
    // 模拟连接状态
    private volatile boolean connected = true;
    
    // 模拟治疗头状态
    private final ConcurrentHashMap<Integer, MockTreatmentHeadState> headStates = new ConcurrentHashMap<>();
    
    public MockHardwareCommunicationAdapter() {
        // 初始化20个治疗头的模拟状态
        for (int i = 1; i <= 20; i++) {
            headStates.put(i, new MockTreatmentHeadState(i));
        }
        logger.info("Mock hardware communication adapter initialized with 20 treatment heads");
    }
    
    @Override
    public String sendCommand(String command) throws SerialCommunicationException {
        return sendCommand(command, "upper");
    }
    
    @Override
    public String sendCommand(String command, String layer) throws SerialCommunicationException {
        if (!connected) {
            throw new SerialCommunicationException("Mock hardware is not connected");
        }
        
        logger.debug("Mock hardware received command: {} (layer: {})", command.trim(), layer);
        
        // 模拟网络延迟
        simulateDelay();
        
        // 如果有硬件模拟器，优先使用
        if (hardwareSimulator != null) {
            String response = hardwareSimulator.generateSimulatedResponse(command);
            logger.debug("Mock hardware response: {}", response.trim());
            return response;
        }
        
        // 否则使用简单的模拟逻辑
        return generateMockResponse(command, layer);
    }
    
    @Override
    public boolean isConnected() {
        return connected;
    }
    
    @Override
    public void connect() throws SerialCommunicationException {
        logger.info("Mock hardware connecting...");
        simulateDelay();
        connected = true;
        logger.info("Mock hardware connected successfully");
    }
    
    @Override
    public void disconnect() {
        logger.info("Mock hardware disconnecting...");
        connected = false;
        logger.info("Mock hardware disconnected");
    }
    
    @Override
    public String getCommunicationType() {
        return "mock";
    }
    
    /**
     * 生成模拟响应
     */
    private String generateMockResponse(String command, String layer) {
        String cmd = command.trim().replace("\r\n", "");
        
        if (cmd.startsWith("TRZI")) {
            return generateTRZIResponse(layer);
        } else if (cmd.startsWith("TWSC")) {
            return generateTWSCResponse(cmd);
        } else if (cmd.startsWith("TWSN")) {
            return generateTWSNResponse(cmd);
        } else if (cmd.startsWith("TWSDT")) {
            return generateTWSDTResponse(cmd);
        } else if (cmd.startsWith("TWZS")) {
            return generateTWZSResponse(cmd);
        } else if (cmd.startsWith("TWZO")) {
            return generateTWZOResponse(cmd);
        } else {
            logger.warn("Unknown mock command: {}", cmd);
            return "ERROR\r\n";
        }
    }
    
    /**
     * 生成TRZI查询响应
     */
    private String generateTRZIResponse(String layer) {
        StringBuilder response = new StringBuilder();
        
        // 根据层级确定治疗头范围
        int startHead = "upper".equals(layer) ? 1 : 11;
        int endHead = "upper".equals(layer) ? 10 : 20;
        
        for (int i = startHead; i <= endHead; i++) {
            MockTreatmentHeadState state = headStates.get(i);
            if (state != null) {
                // 格式：治疗头编号,槽位号,电量,状态
                // 状态：1=充电完成(CHARGED), 0=其他状态
                int status = state.isWorking ? 0 : 1; // 如果正在工作则为0，否则为1(CHARGED)
                response.append(String.format("%d,%d,%d,%d\r\n",
                    i, state.slotNumber, state.batteryLevel, status));
            }
        }
        
        logger.debug("Generated TRZI response for {} layer: {} heads", layer, endHead - startHead + 1);
        return response.toString();
    }
    
    /**
     * 生成TWSC点亮指示灯响应
     */
    private String generateTWSCResponse(String command) {
        // 提取治疗头编号
        String data = command.substring(4);
        logger.debug("Mock TWSC response: {}", command);
        return command + "\r\n";
    }
    
    /**
     * 生成TWSN关闭指示灯响应
     */
    private String generateTWSNResponse(String command) {
        logger.debug("Mock TWSN response: {}", command);
        return command + "\r\n";
    }
    
    /**
     * 生成TWSDT发送参数响应
     */
    private String generateTWSDTResponse(String command) {
        logger.debug("Mock TWSDT response: {}", command);
        return command + "\r\n";
    }
    
    /**
     * 生成TWZS启动治疗响应
     */
    private String generateTWZSResponse(String command) {
        try {
            // 解析治疗头编号
            String[] parts = command.substring(4).split(",");
            int headNumber = Integer.parseInt(parts[0]);
            
            // 更新模拟状态
            MockTreatmentHeadState state = headStates.get(headNumber);
            if (state != null) {
                state.isWorking = true;
                state.usageCount++;
            }
            
            logger.info("Mock treatment started on head {}", headNumber);
            return command + "\r\n";
        } catch (Exception e) {
            logger.error("Error generating TWZS response", e);
            return "ERROR\r\n";
        }
    }
    
    /**
     * 生成TWZO停止治疗响应
     */
    private String generateTWZOResponse(String command) {
        try {
            // 解析治疗头编号
            String data = command.substring(4);
            int headNumber = Integer.parseInt(data);
            
            // 更新模拟状态
            MockTreatmentHeadState state = headStates.get(headNumber);
            if (state != null) {
                state.isWorking = false;
            }
            
            logger.info("Mock treatment stopped on head {}", headNumber);
            return command + "\r\n";
        } catch (Exception e) {
            logger.error("Error generating TWZO response", e);
            return "ERROR\r\n";
        }
    }
    
    /**
     * 模拟网络延迟
     */
    private void simulateDelay() {
        try {
            // 随机延迟50-200毫秒，模拟真实硬件响应时间
            Thread.sleep(ThreadLocalRandom.current().nextInt(50, 200));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 模拟治疗头状态
     */
    private static class MockTreatmentHeadState {
        final int headNumber;
        final int slotNumber;
        int batteryLevel;
        boolean isWorking;
        int usageCount;
        
        MockTreatmentHeadState(int headNumber) {
            this.headNumber = headNumber;
            this.slotNumber = headNumber <= 10 ? headNumber : headNumber - 10; // 正确的槽位号
            this.batteryLevel = ThreadLocalRandom.current().nextInt(80, 101); // 80-100%电量
            this.isWorking = false;
            this.usageCount = 0;
        }
    }
    
    /**
     * 获取模拟治疗头状态（用于调试）
     */
    public MockTreatmentHeadState getMockHeadState(int headNumber) {
        return headStates.get(headNumber);
    }
    
    /**
     * 设置连接状态（用于测试连接失败场景）
     */
    public void setConnected(boolean connected) {
        this.connected = connected;
        logger.info("Mock hardware connection state changed to: {}", connected);
    }
}
