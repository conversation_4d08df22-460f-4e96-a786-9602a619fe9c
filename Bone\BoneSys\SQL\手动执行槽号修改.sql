-- 手动执行槽号修改 - 分步骤执行
-- 请在MySQL客户端中逐步执行以下语句

-- 步骤1：选择数据库
USE bonesys;

-- 步骤2：查看当前表结构
DESCRIBE treatment_heads;

-- 步骤3：添加仓位类型字段
ALTER TABLE treatment_heads 
ADD COLUMN compartment_type VARCHAR(10) NULL DEFAULT NULL COMMENT '仓位类型: SHALLOW(浅部上层), DEEP(深部下层)' 
AFTER slot_number;

-- 步骤4：更新仓位类型数据
UPDATE treatment_heads 
SET compartment_type = 'SHALLOW' 
WHERE head_number BETWEEN 1 AND 10;

UPDATE treatment_heads 
SET compartment_type = 'DEEP' 
WHERE head_number BETWEEN 11 AND 20;

-- 步骤5：重置槽号（可选）
UPDATE treatment_heads 
SET slot_number = NULL;

-- 步骤6：创建索引
CREATE INDEX idx_compartment_type ON treatment_heads (compartment_type);
CREATE INDEX idx_compartment_slot ON treatment_heads (compartment_type, slot_number);

-- 步骤7：验证修改结果
SELECT 
    head_number as '治疗头编号',
    slot_number as '槽号',
    compartment_type as '仓位类型',
    CASE 
        WHEN compartment_type = 'SHALLOW' THEN '上层浅部'
        WHEN compartment_type = 'DEEP' THEN '下层深部'
        ELSE '未知'
    END as '仓位描述',
    realtime_status as '状态'
FROM treatment_heads 
ORDER BY head_number;

-- 步骤8：查看修改后的表结构
DESCRIBE treatment_heads;
