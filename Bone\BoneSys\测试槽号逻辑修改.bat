@echo off
chcp 65001 > nul
echo ========================================
echo 治疗仓槽号逻辑修改测试脚本
echo ========================================
echo.

echo [1/6] 执行数据库结构修改...
echo 请确保MySQL服务正在运行，然后按任意键继续...
pause > nul
echo 正在执行数据库修改...
mysql -u root -p bonesys < "SQL/修改槽号逻辑_简化版.sql"
if %errorlevel% neq 0 (
    echo ❌ 数据库修改失败，请手动执行 SQL/手动执行槽号修改.sql
    echo 或者检查MySQL连接和权限
    pause
    exit /b 1
)
echo ✅ 数据库结构修改完成
echo.

echo [2/6] 测试治疗头数据同步...
echo 请求: GET /api/hardware/heads
curl -s "http://localhost:8080/api/hardware/heads?page=1&size=20" | jq .
echo.

echo [3/6] 测试治疗头推荐API...
echo 请求: POST /api/treatment-parameters/generate-recommendations
curl -s -X POST http://localhost:8080/api/treatment-parameters/generate-recommendations ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"P001001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"肩颈部\",\"color\":\"#FF6B6B\",\"parameters\":{\"time\":\"15分钟\",\"intensity\":\"30\",\"frequency\":\"1000\",\"depth\":\"深部\",\"count\":2}}]}" | jq .
echo.

echo [4/6] 测试浅部治疗头查询...
echo 请求: GET /api/hardware/heads?compartmentType=SHALLOW
curl -s "http://localhost:8080/api/hardware/heads?compartmentType=SHALLOW&page=1&size=10" | jq .
echo.

echo [5/6] 测试深部治疗头查询...
echo 请求: GET /api/hardware/heads?compartmentType=DEEP
curl -s "http://localhost:8080/api/hardware/heads?compartmentType=DEEP&page=1&size=10" | jq .
echo.

echo [6/6] 验证数据库数据...
echo 查询治疗头表结构和数据：
mysql -u root -p bonesys -e "
SELECT 
    head_number as '治疗头编号',
    slot_number as '槽号',
    compartment_type as '仓位类型',
    CASE 
        WHEN compartment_type = 'SHALLOW' THEN '上层浅部'
        WHEN compartment_type = 'DEEP' THEN '下层深部'
        ELSE '未知'
    END as '仓位描述',
    realtime_status as '状态',
    battery_level as '电量'
FROM treatment_heads 
ORDER BY head_number;
"
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 🔍 验证要点：
echo.
echo 1. **数据库结构**
echo    - treatment_heads表新增compartment_type字段
echo    - 槽号约束改为1-10
echo    - 治疗头1-10为SHALLOW，11-20为DEEP
echo.
echo 2. **槽号映射**
echo    - 上层浅部：治疗头1-10，槽号1-10
echo    - 下层深部：治疗头11-20，槽号1-10
echo    - 硬件返回的槽号11-20自动转换为1-10
echo.
echo 3. **API响应**
echo    - 治疗头数据包含compartmentType字段
echo    - 推荐API正确识别浅部/深部需求
echo    - 槽号显示为1-10范围
echo.
echo 4. **推荐逻辑**
echo    - 浅部贴片推荐上层治疗头（1-10号）
echo    - 深部贴片推荐下层治疗头（11-20号）
echo    - 槽号描述显示正确的层级信息
echo.
echo 🚨 故障排查：
echo.
echo "如果数据库修改失败："
echo "1. 检查MySQL服务是否运行"
echo "2. 检查数据库连接权限"
echo "3. 手动执行SQL文件中的语句"
echo.
echo "如果API返回错误："
echo "1. 检查应用是否重启"
echo "2. 检查TreatmentHead实体类字段"
echo "3. 检查Repository查询方法"
echo.
echo "如果推荐逻辑错误："
echo "1. 检查isShallowHead/isDeepHead方法"
echo "2. 检查TreatmentHeadInfo的compartmentType设置"
echo "3. 检查HardwareCommandParser的槽号转换逻辑"
echo.
pause
