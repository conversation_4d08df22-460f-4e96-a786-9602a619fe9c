<template>
  <div class="global-notifications">
    <div
      v-for="(notification, index) in notifications"
      :key="notification.id"
      class="notification-wrapper"
      :style="{
        left: `${54 + index * 130}px`,
        top: '100px',
        zIndex: notification.zIndex
      }"
      @mousedown="bringToFront(notification.id)"
      ref="notificationRefs"
    >
      <!-- 治疗完成通知 -->
      <div
        v-if="notification.type === 'TREATMENT_COMPLETED'"
        class="notification treatment-completed"
        :style="{
          backgroundImage: `url(${completedBgImage})`,
          transform: `translate(${notification.offsetX}px, ${notification.offsetY}px)`
        }"
        @mousedown="startDrag(notification.id, $event)"
      >
        <div class="close-btn" @click="closeNotification(notification.id)">
          <img :src="closeIcon" alt="关闭" />
        </div>
        <div class="content">
          <div class="patient-name">{{ notification.data.patientName }}</div>
          <div class="completion-text">全部治疗已完成！</div>
        </div>
      </div>

      <!-- 治疗头待取回通知 -->
      <div
        v-else-if="notification.type === 'PICKUP_REMINDER'"
        class="notification pickup-reminder"
        :style="{
          backgroundImage: `url(${pickupBgImage})`,
          transform: `translate(${notification.offsetX}px, ${notification.offsetY}px)`
        }"
        @mousedown="startDrag(notification.id, $event)"
      >
        <div class="close-btn" @click="closeNotification(notification.id)">
          <img :src="closeIcon" alt="关闭" />
        </div>
        <div class="content">
          <div class="pickup-text">
            {{ formatHeadNumbers(notification.data.headNumbers) }}治疗头待取回
          </div>
        </div>
      </div>


    </div>

    <!-- 复位治疗头弹窗 -->
    <ResetTreatmentHeadModal
      :visible="showResetModal"
      :compartmentType="resetModalData?.compartmentType"
      :slotNumber="resetModalData?.slotNumber"
      :headNumber="resetModalData?.headNumber"
      :failureCount="resetModalData?.failureCount"
      @close="closeResetModal"
      @confirm="closeResetModal"
    />

    <!-- 重新插入治疗头弹窗 -->
    <ReinsertTreatmentHeadModal
      :visible="showReinsertModal"
      :compartmentType="reinsertModalData?.compartmentType"
      :slotNumber="reinsertModalData?.slotNumber"
      :headNumber="reinsertModalData?.headNumber"
      @close="closeReinsertModal"
      @confirm="closeReinsertModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';

// 导入图片（使用静态导入）
import completedBgImage from '@/assets/images/交互界面/主页/提示某人.png';
import pickupBgImage from '@/assets/images/交互界面/主页/提示治疗头.png';
import closeIcon from '@/assets/images/交互界面/主页/提示×.png';

// 导入新的弹窗组件
import ResetTreatmentHeadModal from './ResetTreatmentHeadModal.vue';
import ReinsertTreatmentHeadModal from './ReinsertTreatmentHeadModal.vue';

interface NotificationData {
  id: string;
  type: 'TREATMENT_COMPLETED' | 'PICKUP_REMINDER';
  data: any;
  timestamp: string;
  zIndex: number;
  offsetX: number;
  offsetY: number;
}

const route = useRoute();
const notifications = ref<NotificationData[]>([]);
const notificationRefs = ref([]);
let ws: WebSocket | null = null;

// 弹窗状态
const showResetModal = ref(false);
const showReinsertModal = ref(false);
const resetModalData = ref<any>(null);
const reinsertModalData = ref<any>(null);
let dragState = {
  isDragging: false,
  notificationId: '',
  startX: 0,
  startY: 0,
  startOffsetX: 0,
  startOffsetY: 0
};
let maxZIndex = 1000;

// 判断当前页面是否应该显示通知
const shouldShowNotifications = computed(() => {
  const currentPath = route.path;
  // 不在设置界面和治疗进程页面显示
  return !currentPath.includes('/settings') && 
         !currentPath.includes('/treatment-process');
});

// 格式化治疗头编号
const formatHeadNumbers = (headNumbers: number[]): string => {
  return headNumbers.join(',');
};

// 连接WebSocket
const connectWebSocket = () => {
  try {
    ws = new WebSocket('ws://localhost:8080/ws/notifications');
    
    ws.onopen = () => {
      console.log('WebSocket连接已建立');
    };
    
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleNotification(message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };
    
    ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      // 5秒后重连
      setTimeout(connectWebSocket, 5000);
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  } catch (error) {
    console.error('WebSocket连接失败:', error);
    // 5秒后重连
    setTimeout(connectWebSocket, 5000);
  }
};

// 处理通知消息
const handleNotification = (message: any) => {
  // 只在允许显示通知的页面处理
  if (!shouldShowNotifications.value) {
    return;
  }

  // 处理异常治疗头通知（高优先级，每次都显示）
  if (message.type === 'REINSERT_HEAD' || message.type === 'RESET_HEAD') {
    const headNumber = message.data.headNumber;
    const failureCount = message.data.failureCount || 0;
    
    // 根据失败次数决定显示哪种弹窗
    if (message.type === 'REINSERT_HEAD' && failureCount <= 5) {
      reinsertModalData.value = message.data;
      showReinsertModal.value = true;
      console.log(`治疗头 ${headNumber} 失败${failureCount}次，显示重新插入治疗头弹窗`, message.data);
    } else if (message.type === 'RESET_HEAD' && failureCount > 5) {
      resetModalData.value = message.data;
      showResetModal.value = true;
      console.log(`治疗头 ${headNumber} 失败${failureCount}次，显示复位治疗头弹窗`, message.data);
    }
    return;
  }

  // 处理其他类型通知（显示可拖拽通知）
  if (message.type === 'TREATMENT_COMPLETED' || message.type === 'PICKUP_REMINDER') {
    // 检查提醒间隔 - 针对每个具体的人或治疗头
    const currentTime = Date.now();
    let shouldShow = true;
    
    if (message.type === 'TREATMENT_COMPLETED') {
      const patientName = message.data.patientName;
      const patientIntervalTime = localStorage.getItem(`patientNotificationInterval_${patientName}`);
      if (patientIntervalTime && currentTime < parseInt(patientIntervalTime)) {
        shouldShow = false;
        console.log(`患者 ${patientName} 的通知在提醒间隔内，跳过显示`);
      }
    } else if (message.type === 'PICKUP_REMINDER') {
      // 检查是否有任何治疗头在间隔时间内
      const headNumbers = message.data.headNumbers;
      for (const headNumber of headNumbers) {
        const headIntervalTime = localStorage.getItem(`headNotificationInterval_${headNumber}`);
        if (headIntervalTime && currentTime < parseInt(headIntervalTime)) {
          shouldShow = false;
          console.log(`治疗头 ${headNumber} 的通知在提醒间隔内，跳过显示`);
          break; // 只要有一个治疗头在间隔内就不显示整个通知
        }
      }
    }
    
    // 如果在提醒间隔内，不显示通知
    if (!shouldShow) {
      return;
    }
    
    const notification: NotificationData = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type: message.type,
      data: message.data,
      timestamp: message.timestamp,
      zIndex: ++maxZIndex,
      offsetX: 0,
      offsetY: 0
    };
    
    notifications.value.push(notification);
    console.log(`显示新通知: ${message.type}`, message.data);
  }
};

// 置顶通知
const bringToFront = (notificationId: string) => {
  const notification = notifications.value.find(n => n.id === notificationId);
  if (notification) {
    notification.zIndex = ++maxZIndex;
  }
};

// 关闭通知
const closeNotification = (notificationId: string) => {
  const notification = notifications.value.find(n => n.id === notificationId);
  if (notification) {
    // 记录关闭时间，用于提醒间隔控制
    const reminderTime = parseInt(localStorage.getItem('reminderTime') || '10');
    const closeTime = Date.now();
    const nextAllowTime = closeTime + (reminderTime * 60 * 1000); // 转换为毫秒
    
    // 针对每个具体的人或治疗头保存下次允许显示的时间
    if (notification.type === 'TREATMENT_COMPLETED') {
      const patientName = notification.data.patientName;
      // 为每个具体患者单独记录提醒间隔
      localStorage.setItem(`patientNotificationInterval_${patientName}`, nextAllowTime.toString());
      console.log(`设置患者 ${patientName} 的下次提醒时间: ${new Date(nextAllowTime).toLocaleString()}`);
    } else if (notification.type === 'PICKUP_REMINDER') {
      // 为每个具体的治疗头组合单独记录提醒间隔
      notification.data.headNumbers.forEach((headNumber: number) => {
        localStorage.setItem(`headNotificationInterval_${headNumber}`, nextAllowTime.toString());
        console.log(`设置治疗头 ${headNumber} 的下次提醒时间: ${new Date(nextAllowTime).toLocaleString()}`);
      });
    }
  }
  
  const index = notifications.value.findIndex(n => n.id === notificationId);
  if (index > -1) {
    notifications.value.splice(index, 1);
  }
};

// 关闭复位弹窗
const closeResetModal = () => {
  if (resetModalData.value) {
    const headNumber = resetModalData.value.headNumber;
    console.log(`关闭复位治疗头弹窗 - 治疗头 ${headNumber}（高优先级通知，无间隔限制）`);
  }
  
  showResetModal.value = false;
  resetModalData.value = null;
};

// 关闭重新插入弹窗
const closeReinsertModal = () => {
  if (reinsertModalData.value) {
    const headNumber = reinsertModalData.value.headNumber;
    console.log(`关闭重新插入治疗头弹窗 - 治疗头 ${headNumber}（高优先级通知，无间隔限制）`);
  }
  
  showReinsertModal.value = false;
  reinsertModalData.value = null;
};

// 开始拖拽
const startDrag = (notificationId: string, event: MouseEvent) => {
  event.preventDefault();
  const notification = notifications.value.find(n => n.id === notificationId);
  if (!notification) return;

  dragState = {
    isDragging: true,
    notificationId,
    startX: event.clientX,
    startY: event.clientY,
    startOffsetX: notification.offsetX,
    startOffsetY: notification.offsetY
  };

  // 置顶当前拖拽的通知
  bringToFront(notificationId);

  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
};

// 拖拽过程
const handleDrag = (event: MouseEvent) => {
  if (!dragState.isDragging) return;

  const notification = notifications.value.find(n => n.id === dragState.notificationId);
  if (!notification) return;

  const deltaX = event.clientX - dragState.startX;
  const deltaY = event.clientY - dragState.startY;

  notification.offsetX = dragState.startOffsetX + deltaX;
  notification.offsetY = dragState.startOffsetY + deltaY;
};

// 停止拖拽
const stopDrag = () => {
  dragState.isDragging = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 监听自定义事件（用于测试）
const handleMockNotification = (event: CustomEvent) => {
  handleNotification(event.detail);
};

// 生命周期
onMounted(() => {
  connectWebSocket();
  // 监听自定义测试事件
  window.addEventListener('mock-notification', handleMockNotification as EventListener);
});

onUnmounted(() => {
  if (ws) {
    ws.close();
  }
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  // 清理自定义事件监听器
  window.removeEventListener('mock-notification', handleMockNotification as EventListener);
});
</script>

<style scoped>
.global-notifications {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.notification-wrapper {
  position: absolute;
  pointer-events: auto;
}

.notification {
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  cursor: move;
  user-select: none;
}

.treatment-completed {
  width: 404px;
  height: 496px;
}

.pickup-reminder {
  width: 404px;
  height: 496px;
}

.close-btn {
  position: absolute;
  top: 25px;
  right: 25px;
  width: 60px;
  height: 60px;
  cursor: pointer;
  z-index: 10;
}

.close-btn img {
  width: 100%;
  height: 100%;
}

.content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

/* 治疗完成通知样式 */
.treatment-completed .patient-name {
  font-size: 42.95px;
  color: #7C7979;
  margin: -150px 0 0 0px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  margin-bottom: 10px;
}

.treatment-completed .completion-text {
  font-size: 24.15px;
  color: #7C7979;
  font-family: MicrosoftYaHei;
  font-weight: normal;
}

/* 治疗头待取回通知样式 */
.pickup-reminder .pickup-text {
  font-size: 42.95px;
  color: #7C7979;
  margin: -150px 0 0 0px;
  width: 404px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
}



/* 拖拽时禁用文本选择 */
.notification.dragging {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style> 