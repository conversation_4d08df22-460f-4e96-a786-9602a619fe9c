package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.TreatmentHeadAbnormalDetectionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 治疗头异常管理控制器
 * 提供异常状态查询和管理的API接口
 */
@RestController
@RequestMapping("/api/hardware/treatment-heads/abnormal")
@CrossOrigin(origins = "*")
public class TreatmentHeadAbnormalController {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadAbnormalController.class);
    
    @Autowired
    private TreatmentHeadAbnormalDetectionService abnormalDetectionService;
    
    /**
     * 获取异常状态统计
     * GET /api/hardware/treatment-heads/abnormal/statistics
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getAbnormalStatistics() {
        try {
            Map<String, Object> statistics = abnormalDetectionService.getAbnormalStatusStatistics();
            return ApiResponse.success("异常状态统计获取成功", statistics);
        } catch (Exception e) {
            logger.error("获取异常状态统计失败", e);
            return ApiResponse.error(500, "获取异常状态统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置指定治疗头的异常状态
     * POST /api/hardware/treatment-heads/abnormal/{headNumber}/reset
     */
    @PostMapping("/{headNumber}/reset")
    public ApiResponse<String> resetAbnormalStatus(@PathVariable Integer headNumber) {
        try {
            if (headNumber < 1 || headNumber > 20) {
                return ApiResponse.badRequest("治疗头编号必须在1-20之间");
            }
            
            abnormalDetectionService.resetAbnormalStatus(headNumber);
            logger.info("治疗头 {} 的异常状态已重置", headNumber);
            
            return ApiResponse.success("异常状态重置成功", 
                                     String.format("治疗头 %d 的异常状态已重置", headNumber));
        } catch (Exception e) {
            logger.error("重置治疗头 {} 异常状态失败", headNumber, e);
            return ApiResponse.error(500, "重置异常状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除所有异常状态记录
     * POST /api/hardware/treatment-heads/abnormal/clear-all
     */
    @PostMapping("/clear-all")
    public ApiResponse<String> clearAllAbnormalStatus() {
        try {
            abnormalDetectionService.clearAllAbnormalStatus();
            logger.info("所有异常状态记录已清除");
            
            return ApiResponse.success("所有异常状态已清除", "所有治疗头的异常状态记录已清除");
        } catch (Exception e) {
            logger.error("清除所有异常状态失败", e);
            return ApiResponse.error(500, "清除所有异常状态失败: " + e.getMessage());
        }
    }
}
