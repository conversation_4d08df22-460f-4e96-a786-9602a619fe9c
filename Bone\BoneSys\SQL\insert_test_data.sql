-- ========================================
-- FREEBONE医疗系统 - 测试数据插入脚本
-- ========================================
-- 
-- 数据逻辑：
-- 1. 一个患者对应多个档案（不同时期的治疗）
-- 2. 一个档案对应多个进程（不同的治疗会话）
-- 3. 一个进程有多个治疗详情（最多3个部位）
-- 4. 一个治疗详情对应一个治疗部位
-- 5. 每次完成治疗详情后更新身体部位统计表
--
-- ========================================

USE bonesys;

-- ========================================
-- 1. 患者数据（5个患者）
-- ========================================

INSERT INTO `patients` (`patient_card_id`, `name`, `gender`, `age`, `contact_info`, `created_at`) VALUES
('P001001', '张三', '男', '45', '13800138001', '2024-01-01 08:00:00'),
('P001002', '李四', '女', '38', '13800138002', '2024-01-02 09:00:00'),
('P001003', '王五', '男', '52', '13800138003', '2024-01-03 10:00:00'),
('P001004', '赵六', '女', '29', '13800138004', '2024-01-04 11:00:00'),
('P001005', '钱七', '男', '61', '13800138005', '2024-01-05 12:00:00');

-- ========================================
-- 2. 档案数据（每个患者2-3个档案）
-- ========================================

INSERT INTO `records` (`record_number`, `patient_id`, `treatment_mode`, `sessions_total_count`, `sessions_completed_count`, `created_at`) VALUES
-- 张三的档案
('R20240101001', 1, 'LOCAL', 10, 8, '2024-01-15'),
('R20240301001', 1, 'TAKEAWAY', 15, 12, '2024-03-20'),

-- 李四的档案  
('R20240102001', 2, 'LOCAL', 8, 6, '2024-01-18'),
('R20240202001', 2, 'TAKEAWAY', 12, 10, '2024-02-25'),
('R20240402001', 2, 'LOCAL', 6, 4, '2024-04-10'),

-- 王五的档案
('R20240103001', 3, 'TAKEAWAY', 20, 15, '2024-01-22'),
('R20240303001', 3, 'LOCAL', 10, 8, '2024-03-15'),

-- 赵六的档案
('R20240104001', 4, 'LOCAL', 12, 9, '2024-01-25'),
('R20240304001', 4, 'TAKEAWAY', 8, 6, '2024-03-28'),

-- 钱七的档案
('R20240105001', 5, 'TAKEAWAY', 15, 12, '2024-01-30'),
('R20240405001', 5, 'LOCAL', 10, 7, '2024-04-05');

-- ========================================
-- 3. 进程数据（每个档案2-4个进程）
-- ========================================

INSERT INTO `processes` (`record_id`, `status`, `start_time`, `end_time`) VALUES
-- 张三档案1的进程
(1, 'COMPLETED', '2024-01-15 09:00:00', '2024-01-15 09:45:00'),
(1, 'COMPLETED', '2024-01-17 14:30:00', '2024-01-17 15:15:00'),
(1, 'COMPLETED', '2024-01-20 10:00:00', '2024-01-20 10:50:00'),
(1, 'IN_PROGRESS', '2024-01-22 16:00:00', NULL),

-- 张三档案2的进程
(2, 'COMPLETED', '2024-03-20 08:30:00', '2024-03-20 09:20:00'),
(2, 'COMPLETED', '2024-03-25 15:00:00', '2024-03-25 15:45:00'),
(2, 'IN_PROGRESS', '2024-03-28 11:00:00', NULL),

-- 李四档案1的进程
(3, 'COMPLETED', '2024-01-18 13:00:00', '2024-01-18 13:40:00'),
(3, 'COMPLETED', '2024-01-22 09:30:00', '2024-01-22 10:15:00'),
(3, 'COMPLETED', '2024-01-25 14:00:00', '2024-01-25 14:45:00'),

-- 李四档案2的进程
(4, 'COMPLETED', '2024-02-25 10:00:00', '2024-02-25 10:50:00'),
(4, 'COMPLETED', '2024-03-01 16:30:00', '2024-03-01 17:20:00'),

-- 李四档案3的进程
(5, 'COMPLETED', '2024-04-10 11:30:00', '2024-04-10 12:15:00'),
(5, 'IN_PROGRESS', '2024-04-15 14:00:00', NULL),

-- 王五档案1的进程
(6, 'COMPLETED', '2024-01-22 15:00:00', '2024-01-22 16:00:00'),
(6, 'COMPLETED', '2024-01-28 09:00:00', '2024-01-28 10:00:00'),
(6, 'COMPLETED', '2024-02-05 13:30:00', '2024-02-05 14:30:00'),

-- 王五档案2的进程
(7, 'COMPLETED', '2024-03-15 10:30:00', '2024-03-15 11:20:00'),
(7, 'IN_PROGRESS', '2024-03-20 15:00:00', NULL),

-- 赵六档案1的进程
(8, 'COMPLETED', '2024-01-25 08:00:00', '2024-01-25 08:45:00'),
(8, 'COMPLETED', '2024-02-01 14:30:00', '2024-02-01 15:15:00'),

-- 赵六档案2的进程
(9, 'COMPLETED', '2024-03-28 11:00:00', '2024-03-28 11:50:00'),
(9, 'IN_PROGRESS', '2024-04-02 16:30:00', NULL),

-- 钱七档案1的进程
(10, 'COMPLETED', '2024-01-30 09:30:00', '2024-01-30 10:30:00'),
(10, 'COMPLETED', '2024-02-10 13:00:00', '2024-02-10 14:00:00'),

-- 钱七档案2的进程
(11, 'COMPLETED', '2024-04-05 15:30:00', '2024-04-05 16:20:00'),
(11, 'IN_PROGRESS', '2024-04-12 10:00:00', NULL);

-- ========================================
-- 4. 治疗详情数据（每个进程1-3个治疗详情）
-- ========================================

INSERT INTO `treatment_details` (`process_id`, `body_part`, `head_number_used`, `duration`, `intensity`, `frequency`, `patch_type`, `patch_quantity`, `status`) VALUES
-- 张三档案1进程1（已完成）- 肩颈部+腰背部
(1, '肩颈部', 15, 20, 45.00, 1000, 'SHALLOW', 2, 'RETURNED'),
(1, '腰背部', 17, 25, 60.00, 100, 'DEEP', 2, 'RETURNED'),

-- 张三档案1进程2（已完成）- 上肢
(2, '上肢', 18, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 张三档案1进程3（已完成）- 肩颈部+下肢+髋部
(3, '肩颈部', 19, 20, 45.00, 1000, 'SHALLOW', 2, 'RETURNED'),
(3, '下肢', 14, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),
(3, '髋部', 20, 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),

-- 张三档案1进程4（进行中）- 腰背部
(4, '腰背部', 12, 25, 60.00, 100, 'DEEP', 2, 'AWAITING_RETURN'),

-- 张三档案2进程1（已完成）- 肩颈部+上肢
(5, '肩颈部', 11, 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),
(5, '上肢', 16, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 张三档案2进程2（已完成）- 腰背部+下肢
(6, '腰背部', 1, 30, 60.00, 100, 'DEEP', 3, 'RETURNED'),
(6, '下肢', 2, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 张三档案2进程3（进行中）- 肩颈部
(7, '肩颈部', 3, 25, 60.00, 1000, 'SHALLOW', 3, 'AWAITING_RETURN'),

-- 李四档案1进程1（已完成）- 肩颈部
(8, '肩颈部', 4, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 李四档案1进程2（已完成）- 腰背部+髋部
(9, '腰背部', 5, 20, 30.00, 100, 'DEEP', 1, 'RETURNED'),
(9, '髋部', 6, 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),

-- 李四档案1进程3（已完成）- 上肢+下肢
(10, '上肢', 7, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),
(10, '下肢', 8, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 李四档案2进程1（已完成）- 肩颈部+腰背部
(11, '肩颈部', 9, 20, 45.00, 1000, 'SHALLOW', 2, 'RETURNED'),
(11, '腰背部', 10, 25, 60.00, 100, 'DEEP', 2, 'RETURNED'),

-- 李四档案2进程2（已完成）- 下肢
(12, '下肢', 13, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 李四档案3进程1（已完成）- 肩颈部+上肢
(13, '肩颈部', 1, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),
(13, '上肢', 2, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 李四档案3进程2（进行中）- 腰背部
(14, '腰背部', 3, 20, 30.00, 100, 'DEEP', 1, 'AWAITING_RETURN'),

-- 王五档案1进程1（已完成）- 肩颈部+腰背部+下肢
(15, '肩颈部', 4, 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),
(15, '腰背部', 5, 30, 60.00, 100, 'DEEP', 3, 'RETURNED'),
(15, '下肢', 6, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 王五档案1进程2（已完成）- 髋部+上肢
(16, '髋部', 7, 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),
(16, '上肢', 8, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 王五档案1进程3（已完成）- 肩颈部
(17, '肩颈部', 9, 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),

-- 王五档案2进程1（已完成）- 腰背部+下肢
(18, '腰背部', 10, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),
(18, '下肢', 13, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 王五档案2进程2（进行中）- 肩颈部+上肢
(19, '肩颈部', 1, 20, 45.00, 1000, 'SHALLOW', 2, 'AWAITING_RETURN'),
(19, '上肢', 2, 15, 30.00, 1000, 'SHALLOW', 1, 'AWAITING_RETURN'),

-- 赵六档案1进程1（已完成）- 肩颈部+腰背部
(20, '肩颈部', 3, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),
(20, '腰背部', 4, 20, 30.00, 100, 'DEEP', 1, 'RETURNED'),

-- 赵六档案1进程2（已完成）- 上肢
(21, '上肢', 5, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 赵六档案2进程1（已完成）- 下肢+髋部
(22, '下肢', 6, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),
(22, '髋部', 7, 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),

-- 赵六档案2进程2（进行中）- 肩颈部
(23, '肩颈部', 8, 15, 30.00, 1000, 'SHALLOW', 1, 'AWAITING_RETURN'),

-- 钱七档案1进程1（已完成）- 肩颈部+腰背部+下肢
(24, '肩颈部', 9, 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),
(24, '腰背部', 10, 30, 60.00, 100, 'DEEP', 3, 'RETURNED'),
(24, '下肢', 13, 20, 45.00, 100, 'DEEP', 2, 'RETURNED'),

-- 钱七档案1进程2（已完成）- 髋部
(25, '髋部', 1, 20, 45.00, 1000, 'DEEP', 1, 'RETURNED'),

-- 钱七档案2进程1（已完成）- 肩颈部+上肢
(26, '肩颈部', 2, 25, 60.00, 1000, 'SHALLOW', 3, 'RETURNED'),
(26, '上肢', 3, 15, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 钱七档案2进程2（进行中）- 腰背部+下肢
(27, '腰背部', 4, 30, 60.00, 100, 'DEEP', 3, 'AWAITING_RETURN'),
(27, '下肢', 5, 20, 45.00, 100, 'DEEP', 2, 'AWAITING_RETURN');

-- ========================================
-- 5. 身体部位统计数据（基于已完成的治疗详情）
-- ========================================

INSERT INTO `body_part_stats` (`record_id`, `body_part`, `total_sessions`, `total_duration`, `avg_intensity`, `last_treatment_date`) VALUES
-- 张三档案1的统计
(1, '肩颈部', 2, 40, 45.00, '2024-01-20'),
(1, '腰背部', 1, 25, 60.00, '2024-01-15'),
(1, '上肢', 1, 15, 30.00, '2024-01-17'),
(1, '下肢', 1, 20, 45.00, '2024-01-20'),
(1, '髋部', 1, 20, 45.00, '2024-01-20'),

-- 张三档案2的统计
(2, '肩颈部', 1, 25, 60.00, '2024-03-20'),
(2, '上肢', 1, 15, 30.00, '2024-03-20'),
(2, '腰背部', 1, 30, 60.00, '2024-03-25'),
(2, '下肢', 1, 20, 45.00, '2024-03-25'),

-- 李四档案1的统计
(3, '肩颈部', 1, 15, 30.00, '2024-01-18'),
(3, '腰背部', 1, 20, 30.00, '2024-01-22'),
(3, '髋部', 1, 20, 45.00, '2024-01-22'),
(3, '上肢', 1, 15, 30.00, '2024-01-25'),
(3, '下肢', 1, 20, 45.00, '2024-01-25'),

-- 李四档案2的统计
(4, '肩颈部', 1, 20, 45.00, '2024-02-25'),
(4, '腰背部', 1, 25, 60.00, '2024-02-25'),
(4, '下肢', 1, 20, 45.00, '2024-03-01'),

-- 李四档案3的统计
(5, '肩颈部', 1, 15, 30.00, '2024-04-10'),
(5, '上肢', 1, 15, 30.00, '2024-04-10'),

-- 王五档案1的统计
(6, '肩颈部', 2, 50, 52.50, '2024-02-05'),
(6, '腰背部', 1, 30, 60.00, '2024-01-22'),
(6, '下肢', 1, 20, 45.00, '2024-01-22'),
(6, '髋部', 1, 20, 45.00, '2024-01-28'),
(6, '上肢', 1, 15, 30.00, '2024-01-28'),

-- 王五档案2的统计
(7, '腰背部', 1, 20, 45.00, '2024-03-15'),
(7, '下肢', 1, 20, 45.00, '2024-03-15'),

-- 赵六档案1的统计
(8, '肩颈部', 1, 15, 30.00, '2024-01-25'),
(8, '腰背部', 1, 20, 30.00, '2024-01-25'),
(8, '上肢', 1, 15, 30.00, '2024-02-01'),

-- 赵六档案2的统计
(9, '下肢', 1, 20, 45.00, '2024-03-28'),
(9, '髋部', 1, 20, 45.00, '2024-03-28'),

-- 钱七档案1的统计
(10, '肩颈部', 1, 25, 60.00, '2024-01-30'),
(10, '腰背部', 1, 30, 60.00, '2024-01-30'),
(10, '下肢', 1, 20, 45.00, '2024-01-30'),
(10, '髋部', 1, 20, 45.00, '2024-02-10'),

-- 钱七档案2的统计
(11, '肩颈部', 1, 25, 60.00, '2024-04-05'),
(11, '上肢', 1, 15, 30.00, '2024-04-05');

-- ========================================
-- 6. 系统参数更新（确保贴片数量范围正确）
-- ========================================

UPDATE `system_parameters`
SET `parameter_value` = '{"min": 1, "max": 6}'
WHERE `parameter_key` = 'patch_count_range';

INSERT INTO `system_parameters` (`parameter_category`, `parameter_key`, `parameter_value`, `parameter_type`, `description`) VALUES
('TREATMENT', 'patch_count_range', '{"min": 1, "max": 6}', 'JSON', '贴片数量范围')
ON DUPLICATE KEY UPDATE
  `parameter_value` = VALUES(`parameter_value`);

-- ========================================
-- 测试数据插入完成
-- ========================================

SELECT '=== 测试数据统计 ===' as info;

SELECT
    '患者数量' as item,
    COUNT(*) as count
FROM patients
UNION ALL
SELECT
    '档案数量' as item,
    COUNT(*) as count
FROM records
UNION ALL
SELECT
    '进程数量' as item,
    COUNT(*) as count
FROM processes
UNION ALL
SELECT
    '治疗详情数量' as item,
    COUNT(*) as count
FROM treatment_details
UNION ALL
SELECT
    '身体部位统计数量' as item,
    COUNT(*) as count
FROM body_part_stats;

SELECT '=== 数据插入完成 ===' as message;
