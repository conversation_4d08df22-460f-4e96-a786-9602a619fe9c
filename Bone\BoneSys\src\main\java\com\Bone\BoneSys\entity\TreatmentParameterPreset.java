package com.Bone.BoneSys.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.Bone.BoneSys.entity.enums.PatchType;

import java.time.LocalDateTime;

/**
 * 治疗参数预设实体类
 * 对应数据库表：treatment_parameter_presets
 * 
 * 支持每个部位1-6个贴片数量的配置
 */
@Entity
@Table(name = "treatment_parameter_presets")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TreatmentParameterPreset {
    
    /**
     * 预设ID（主键）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    /**
     * 预设名称
     */
    @Column(name = "preset_name", nullable = false, length = 100)
    private String presetName;
    
    /**
     * 治疗部位
     */
    @Column(name = "body_part", nullable = false, length = 50)
    private String bodyPart;
    
    /**
     * 默认治疗时长（分钟）
     */
    @Column(name = "default_duration", nullable = false)
    private Integer defaultDuration = 20;
    
    /**
     * 默认治疗强度档位（30/45/60）
     */
    @Column(name = "default_intensity", nullable = false)
    private Integer defaultIntensity = 45;
    
    /**
     * 默认治疗频率（100/1000Hz）
     */
    @Column(name = "default_frequency", nullable = false)
    private Integer defaultFrequency = 1000;
    
    /**
     * 贴片类型：SHALLOW（浅部）, DEEP（深部）
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "patch_type", nullable = false, length = 20)
    private PatchType patchType = PatchType.SHALLOW;
    
    /**
     * 推荐贴片数量（1-6）
     */
    @Column(name = "recommended_count", nullable = false)
    private Integer recommendedCount = 1;
    
    /**
     * 最小贴片数量
     */
    @Column(name = "min_patch_count", nullable = false)
    private Integer minPatchCount = 1;
    
    /**
     * 最大贴片数量
     */
    @Column(name = "max_patch_count", nullable = false)
    private Integer maxPatchCount = 6;
    
    /**
     * 是否为默认预设
     */
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;
    
    /**
     * 是否启用
     */
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 设置创建时间和更新时间
     */
    @PrePersist
    public void prePersist() {
        LocalDateTime now = LocalDateTime.now();
        if (this.createdAt == null) {
            this.createdAt = now;
        }
        this.updatedAt = now;
    }
    
    /**
     * 更新时间
     */
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
