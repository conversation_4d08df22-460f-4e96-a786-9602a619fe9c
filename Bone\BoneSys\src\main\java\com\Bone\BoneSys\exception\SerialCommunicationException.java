package com.Bone.BoneSys.exception;

import com.Bone.BoneSys.util.Constants;

/**
 * 串口通信异常
 */
public class SerialCommunicationException extends BaseException {
    
    public SerialCommunicationException(String message) {
        super(Constants.ResponseCode.SERIAL_COMMUNICATION_ERROR, message);
    }
    
    public SerialCommunicationException(String message, Throwable cause) {
        super(Constants.ResponseCode.SERIAL_COMMUNICATION_ERROR, message, cause);
    }
}