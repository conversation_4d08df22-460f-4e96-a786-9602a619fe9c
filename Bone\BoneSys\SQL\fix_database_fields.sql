-- ========================================
-- 修复数据库字段名不匹配问题
-- 问题1：后端代码期望 user_password_hash，但数据库中是 users_password_hash
-- 问题2：后端代码期望 compartment_type，但数据库中是 compartment
-- 解决：重命名字段以匹配后端代码
-- ========================================

USE bonesys;

-- 检查当前表结构
SELECT '=== 修复前的表结构 ===' as message;
DESCRIBE users;
DESCRIBE treatment_heads;

-- 检查字段是否存在
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN 'users_password_hash 字段存在，需要重命名'
        ELSE 'users_password_hash 字段不存在，可能已经修复'
    END as users_check
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = 'bonesys'
  AND TABLE_NAME = 'users'
  AND COLUMN_NAME = 'users_password_hash';

SELECT
    CASE
        WHEN COUNT(*) > 0 THEN 'compartment 字段存在，需要重命名'
        ELSE 'compartment 字段不存在，可能已经修复'
    END as treatment_heads_check
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = 'bonesys'
  AND TABLE_NAME = 'treatment_heads'
  AND COLUMN_NAME = 'compartment';

-- 修复用户表字段名（仅在字段存在时执行）
SET @sql = (
    SELECT CASE
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE `users` CHANGE COLUMN `users_password_hash` `user_password_hash` VARCHAR(50) NOT NULL DEFAULT ''123456'' COMMENT ''用户密码（明文存储）'';'
        ELSE 'SELECT ''users_password_hash 字段不存在，跳过修复'' as message;'
    END
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'bonesys'
      AND TABLE_NAME = 'users'
      AND COLUMN_NAME = 'users_password_hash'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修复治疗头表字段名（仅在字段存在时执行）
SET @sql = (
    SELECT CASE
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE `treatment_heads` CHANGE COLUMN `compartment` `compartment_type` VARCHAR(10) NULL DEFAULT NULL COMMENT ''仓位 (UPPER=上层浅部, LOWER=下层深部)'';'
        ELSE 'SELECT ''compartment 字段不存在，跳过修复'' as message;'
    END
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'bonesys'
      AND TABLE_NAME = 'treatment_heads'
      AND COLUMN_NAME = 'compartment'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证修改结果
SELECT '=== 修复后的表结构 ===' as message;
DESCRIBE users;
DESCRIBE treatment_heads;

-- 显示修复完成信息
SELECT '=== 字段名修复完成 ===' as message;
SELECT 'users_password_hash 已重命名为 user_password_hash' as fix_info_1;
SELECT 'compartment 已重命名为 compartment_type' as fix_info_2;

-- 验证数据
SELECT id, username, user_password_hash FROM users;
SELECT head_number, slot_number, compartment_type, realtime_status FROM treatment_heads ORDER BY head_number;
