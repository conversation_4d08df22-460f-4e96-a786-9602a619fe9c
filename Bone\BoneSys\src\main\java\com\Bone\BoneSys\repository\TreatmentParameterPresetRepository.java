package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.TreatmentParameterPreset;
import com.Bone.BoneSys.entity.enums.PatchType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 治疗参数预设数据访问接口
 */
@Repository
public interface TreatmentParameterPresetRepository extends JpaRepository<TreatmentParameterPreset, Long> {
    
    /**
     * 根据部位查找预设
     */
    List<TreatmentParameterPreset> findByBodyPart(String bodyPart);
    
    /**
     * 根据部位和预设名称查找预设
     */
    Optional<TreatmentParameterPreset> findByBodyPartAndPresetName(String bodyPart, String presetName);
    
    /**
     * 查找默认预设
     */
    List<TreatmentParameterPreset> findByIsDefaultTrue();
    
    /**
     * 根据部位查找默认预设
     */
    Optional<TreatmentParameterPreset> findByBodyPartAndIsDefaultTrue(String bodyPart);
    
    /**
     * 查找启用的预设
     */
    List<TreatmentParameterPreset> findByIsActiveTrue();
    
    /**
     * 根据部位和贴片类型查找预设
     */
    List<TreatmentParameterPreset> findByBodyPartAndPatchType(String bodyPart, PatchType patchType);
    
    /**
     * 根据贴片数量范围查找预设
     */
    @Query("SELECT p FROM TreatmentParameterPreset p WHERE " +
           "p.minPatchCount <= :patchCount AND p.maxPatchCount >= :patchCount")
    List<TreatmentParameterPreset> findByPatchCountRange(@Param("patchCount") Integer patchCount);
    
    /**
     * 根据部位查找启用的预设
     */
    List<TreatmentParameterPreset> findByBodyPartAndIsActiveTrue(String bodyPart);
}
