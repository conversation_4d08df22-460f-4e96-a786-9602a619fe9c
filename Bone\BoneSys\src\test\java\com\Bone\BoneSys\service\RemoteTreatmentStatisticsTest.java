package com.Bone.BoneSys.service;

import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.entity.enums.*;
import com.Bone.BoneSys.repository.*;
import com.Bone.BoneSys.controller.TreatmentProcessController;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 远端治疗统计更新逻辑测试
 * 验证：
 * 1. 详情变为 AWAITING_RETURN 时更新 body_part_stats
 * 2. 全部详情为 AWAITING_RETURN/TERMINATED 时更新 sessions_completed_count
 * 3. remoteCounted 防重机制
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class RemoteTreatmentStatisticsTest {

    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;
    
    @Autowired
    private TreatmentProcessController treatmentProcessController;
    
    @Autowired
    private TreatmentDetailAutoUpdateService autoUpdateService;

    private Patient testPatient;
    private Record testRecord;
    private Process testProcess;

    @BeforeEach
    void setUp() {
        // 创建测试患者
        testPatient = new Patient();
        testPatient.setPatientCardId("TEST001");
        testPatient.setName("测试患者");
        testPatient.setGender("男");
        testPatient.setAge("30");
        testPatient.setContactInfo("13800000000");
        testPatient.setCreatedAt(LocalDateTime.now());
        testPatient = patientRepository.save(testPatient);

        // 创建测试档案
        testRecord = new Record();
        testRecord.setRecordNumber("REC-TEST-001");
        testRecord.setPatient(testPatient);
        testRecord.setDiagnosisDescription("测试诊断");
        testRecord.setSessionsCompletedCount(0);
        testRecord.setCreatedAt(LocalDate.now());
        testRecord = recordRepository.save(testRecord);

        // 创建远端治疗进程
        testProcess = new Process();
        testProcess.setRecord(testRecord);
        testProcess.setTreatmentMode(TreatmentMode.TAKE_AWAY);
        testProcess.setStatus(ProcessStatus.IN_PROGRESS);
        testProcess.setStartTime(LocalDateTime.now());
        testProcess.setRemoteCounted(false);
        testProcess = processRepository.save(testProcess);
    }

    @Test
    void testBodyPartStatsUpdateOnAwaitingReturn() {
        // 创建治疗详情
        TreatmentDetail detail = createTreatmentDetail("肩部", 30);
        
        // 验证初始状态：没有部位统计
        List<BodyPartStat> initialStats = bodyPartStatRepository.findByRecordIdAndBodyPart(
            testRecord.getId(), "肩部");
        assertTrue(initialStats.isEmpty());

        // 手动设置为待取回状态
        treatmentProcessController.setAwaitingReturn(detail.getId());

        // 验证部位统计已更新
        List<BodyPartStat> updatedStats = bodyPartStatRepository.findByRecordIdAndBodyPart(
            testRecord.getId(), "肩部");
        assertEquals(1, updatedStats.size());
        
        BodyPartStat stat = updatedStats.get(0);
        assertEquals(1, stat.getTotalUsageCount());
        assertEquals(30, stat.getTotalDurationMinutes());
    }

    @Test
    void testSessionsCompletedCountUpdateOnAllAwaitingReturn() {
        // 创建两个治疗详情
        TreatmentDetail detail1 = createTreatmentDetail("肩部", 30);
        TreatmentDetail detail2 = createTreatmentDetail("腰部", 25);

        // 验证初始档案完成次数
        assertEquals(0, testRecord.getSessionsCompletedCount());
        assertFalse(testProcess.getRemoteCounted());

        // 设置第一个详情为待取回
        treatmentProcessController.setAwaitingReturn(detail1.getId());
        
        // 刷新进程状态
        testProcess = processRepository.findById(testProcess.getId()).orElse(null);
        testRecord = recordRepository.findById(testRecord.getId()).orElse(null);
        
        // 此时应该还没有更新档案完成次数（因为不是全部）
        assertEquals(0, testRecord.getSessionsCompletedCount());
        assertFalse(testProcess.getRemoteCounted());

        // 设置第二个详情为待取回
        treatmentProcessController.setAwaitingReturn(detail2.getId());
        
        // 刷新状态
        testProcess = processRepository.findById(testProcess.getId()).orElse(null);
        testRecord = recordRepository.findById(testRecord.getId()).orElse(null);
        
        // 现在应该更新了档案完成次数
        assertEquals(1, testRecord.getSessionsCompletedCount());
        assertTrue(testProcess.getRemoteCounted());
    }

    @Test
    void testRemoteCountedPreventsDuplicateUpdate() {
        // 创建治疗详情
        TreatmentDetail detail = createTreatmentDetail("肩部", 30);
        
        // 设置为待取回状态
        treatmentProcessController.setAwaitingReturn(detail.getId());
        
        // 刷新状态
        testRecord = recordRepository.findById(testRecord.getId()).orElse(null);
        assertEquals(1, testRecord.getSessionsCompletedCount());

        // 再次调用检查方法（模拟重复触发）
        treatmentProcessController.checkAndUpdateProcessForAwaitingReturn(testProcess.getId());
        
        // 刷新状态
        testRecord = recordRepository.findById(testRecord.getId()).orElse(null);
        
        // 验证没有重复更新
        assertEquals(1, testRecord.getSessionsCompletedCount());
    }

    @Test
    void testMixedAwaitingReturnAndTerminated() {
        // 创建两个治疗详情
        TreatmentDetail detail1 = createTreatmentDetail("肩部", 30);
        TreatmentDetail detail2 = createTreatmentDetail("腰部", 25);

        // 一个设为待取回，一个设为终止
        treatmentProcessController.setAwaitingReturn(detail1.getId());
        treatmentProcessController.terminateDetail(detail2.getId());
        
        // 刷新状态
        testProcess = processRepository.findById(testProcess.getId()).orElse(null);
        testRecord = recordRepository.findById(testRecord.getId()).orElse(null);
        
        // 验证档案完成次数已更新（混合状态也算临床完成）
        assertEquals(1, testRecord.getSessionsCompletedCount());
        assertTrue(testProcess.getRemoteCounted());
        
        // 验证部位统计（只有待取回的会更新统计）
        List<BodyPartStat> shoulderStats = bodyPartStatRepository.findByRecordIdAndBodyPart(
            testRecord.getId(), "肩部");
        assertEquals(1, shoulderStats.size());
        
        List<BodyPartStat> waistStats = bodyPartStatRepository.findByRecordIdAndBodyPart(
            testRecord.getId(), "腰部");
        assertTrue(waistStats.isEmpty()); // 终止的不更新统计
    }

    private TreatmentDetail createTreatmentDetail(String bodyPart, int duration) {
        TreatmentDetail detail = new TreatmentDetail();
        detail.setProcess(testProcess);
        detail.setBodyPart(bodyPart);
        detail.setHeadNumberUsed(1);
        detail.setDuration(duration);
        detail.setIntensity(new BigDecimal("45.0"));
        detail.setFrequency(1000);
        detail.setPatchType(PatchType.SHALLOW);
        detail.setPatchQuantity(2);
        detail.setStatus(TreatmentDetailStatus.TREATING);
        return treatmentDetailRepository.save(detail);
    }
}
