package com.Bone.BoneSys.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

/**
 * 启动时同步服务
 * 在应用启动时自动同步硬件治疗头数据到数据库
 */
@Service
public class StartupSyncService implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(StartupSyncService.class);
    
    @Autowired
    private HardwareService hardwareService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("Starting application startup synchronization...");
        
        try {
            // 等待一段时间确保所有服务都已启动
            Thread.sleep(5000);
            
            logger.info("Synchronizing treatment heads from hardware...");
            
            // 同步治疗头数据
            hardwareService.syncAllTreatmentHeads();
            
            logger.info("Application startup synchronization completed successfully");
            
        } catch (Exception e) {
            logger.error("Failed to synchronize treatment heads during startup: {}", e.getMessage());
            logger.warn("Application will continue running, but treatment head data may not be up to date");
            // 不抛出异常，避免影响应用启动
        }
    }
}
