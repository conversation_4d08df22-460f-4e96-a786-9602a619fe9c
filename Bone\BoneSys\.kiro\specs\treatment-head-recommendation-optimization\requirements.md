# 治疗头推荐系统优化需求文档

## 介绍

本文档定义了治疗头推荐系统的优化需求，主要目标是根据每个身体部位需要的贴片数量和深浅部位类型，智能判断治疗头数量是否充足，并在充足时推荐最优的治疗头组合并控制指示灯亮起。

系统需要精确匹配每个部位的贴片需求（数量+类型），确保浅部贴片使用上仓治疗头（1-10号），深部贴片使用下仓治疗头（11-20号）。

## 需求

### 需求 1 - 精确的贴片需求输入

**用户故事：** 作为医护人员，我希望能够为每个治疗部位精确指定需要的贴片数量和类型，确保治疗方案的准确性。

#### 验收标准

1. 当设置治疗参数时，系统应允许为每个身体部位单独指定贴片数量（1-4个）
2. 当设置治疗参数时，系统应允许为每个身体部位单独指定贴片类型（SHALLOW浅部/DEEP深部）
3. 当输入贴片需求时，系统应验证每个部位的贴片数量在有效范围内（1-4个）
4. 当输入贴片需求时，系统应验证贴片类型为有效值（SHALLOW或DEEP）
5. 当完成参数设置时，系统应保存每个部位的具体贴片需求信息

### 需求 2 - 智能治疗头数量计算

**用户故事：** 作为系统，我需要根据各部位的贴片需求自动计算所需的治疗头总数量，并按类型分类统计。

#### 验收标准

1. 当接收到贴片需求时，系统应自动计算总的治疗头需求数量（各部位贴片数量之和）
2. 当计算需求时，系统应分别统计浅部贴片需求数量（SHALLOW类型的贴片总数）
3. 当计算需求时，系统应分别统计深部贴片需求数量（DEEP类型的贴片总数）
4. 当需求发生变化时，系统应实时更新计算结果
5. 当计算完成时，系统应提供清晰的需求统计信息

### 需求 3 - 分类治疗头可用性检查

**用户故事：** 作为系统，我需要分别检查浅部和深部治疗头的可用性，确保各类型治疗头数量充足。

#### 验收标准

1. 当检查可用性时，系统应通过TRZI指令查询所有治疗头状态
2. 当获取治疗头状态时，系统应按位置分类筛选可用的浅部治疗头（1-10号，电量>60%，主板查询所有在盒子里面的治疗头数据）
3. 当获取治疗头状态时，系统应按位置分类筛选可用的深部治疗头（11-20号，电量>60%，主板查询所有在盒子里面的治疗头数据）
4. 当检查数量充足性时，系统应验证可用浅部治疗头数量 >= 浅部贴片需求数量
5. 当检查数量充足性时，系统应验证可用深部治疗头数量 >= 深部贴片需求数量
6. 当任一类型治疗头数量不足时，系统应判定为整体不充足

### 需求 4 - 智能治疗头推荐算法

**用户故事：** 作为系统，我需要在治疗头数量充足时，按照优化算法推荐最佳的治疗头组合。

#### 验收标准

1. 当治疗头数量充足时，系统应按电量高低和使用次数少优先的原则排序浅部可用治疗头
2. 当治疗头数量充足时，系统应按电量高低和使用次数少优先的原则排序深部可用治疗头
3. 当生成推荐时，系统应从排序后的浅部治疗头中选择前N个（N=浅部贴片需求数量）
4. 当生成推荐时，系统应从排序后的深部治疗头中选择前M个（M=深部贴片需求数量）
5. 当生成推荐时，系统应为不同部位的治疗头分配不同的指示灯颜色（第一个选择部位橙色、第二个蓝色、第三个绿色）

### 需求 5 - 治疗头指示灯控制

**用户故事：** 作为医护人员，我希望推荐的治疗头能够自动亮起指示灯，方便我快速找到正确的设备。

#### 验收标准

1. 当生成推荐结果时，系统应通过TWSC指令为每个推荐的治疗头点亮指示灯
2. 当点亮指示灯时，系统应按部位的先后选择顺序分配颜色（第1个橙色、第2个蓝色、第3个绿色，循环使用）
3. 当指示灯点亮成功时，系统应记录每个治疗头的指示灯颜色和状态
4. 当需要取消推荐时，系统应通过TWSN指令关闭所有推荐治疗头的指示灯
5. 当指示灯控制失败时，系统应记录错误信息并提供重试机制

### 需求 6 - 详细的反馈信息

**用户故事：** 作为医护人员，我希望系统能够提供详细的可用性检查结果和推荐信息，帮助我了解当前状态。

#### 验收标准

1. 当治疗头充足时，系统应显示"治疗头数量充足，浅部可用X个(需要Y个)，深部可用A个(需要B个)"的信息
2. 当治疗头不足时，系统应显示具体的不足信息，区分浅部和深部的缺口
3. 当显示推荐结果时，系统应显示每个推荐治疗头的详细信息（编号、槽位、电量、使用次数、指示灯颜色）
4. 当显示推荐理由时，系统应说明推荐依据（如"电量85%，使用次数12次，适用于浅部贴片"）
5. 当推荐完成时，系统应提供推荐治疗头的总览信息和使用指导

### 需求 7 - 向后兼容性支持

**用户故事：** 作为系统维护者，我希望新的推荐系统能够兼容现有的API接口，确保平滑升级。

#### 验收标准

1. 当接收到旧格式请求时，系统应自动转换为新格式处理
2. 当处理旧格式时，系统应根据原有的requiredCount和patchType生成对应的部位需求
3. 当返回响应时，系统应保持现有的响应格式不变
4. 当使用旧接口时，系统应在日志中记录兼容性处理信息
5. 当新旧格式混用时，系统应优先使用新格式的数据进行处理

### 需求 8 - 错误处理和异常情况

**用户故事：** 作为系统，我需要妥善处理各种异常情况，确保系统的稳定性和可靠性。

#### 验收标准

1. 当串口通信失败时，系统应返回明确的错误信息并建议重试
2. 当治疗头状态查询超时时，系统应提供超时处理和重试机制
3. 当指示灯控制失败时，系统应记录失败的治疗头编号并继续处理其他治疗头
4. 当输入参数无效时，系统应返回详细的参数验证错误信息
5. 当系统异常时，系统应记录详细的错误日志便于问题排查