package com.Bone.BoneSys;

import com.Bone.BoneSys.controller.DashboardController;
import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.repository.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DashboardController单元测试
 * 测试主界面数据接口的功能完整性
 */
@SpringBootTest
@TestPropertySource(properties = {
    "hardware.simulator.enabled=true"
})
public class DashboardControllerTest {

    @Autowired
    private DashboardController dashboardController;

    @MockBean
    private PatientRepository patientRepository;
    
    @MockBean
    private RecordRepository recordRepository;
    
    @MockBean
    private ProcessRepository processRepository;
    
    @MockBean
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @MockBean
    private TreatmentHeadRepository treatmentHeadRepository;
    
    @MockBean
    private HardwareService hardwareService;

    @BeforeEach
    void setUp() {
        // 设置模拟数据
        when(patientRepository.count()).thenReturn(150L);
        when(recordRepository.count()).thenReturn(320L);
        when(processRepository.count()).thenReturn(890L);
        when(treatmentDetailRepository.count()).thenReturn(1250L);
        when(treatmentHeadRepository.count()).thenReturn(20L);
        
        // 模拟治疗头状态统计
        Object[] statusStat1 = {"CHARGING", 3L};
        Object[] statusStat2 = {"CHARGED", 15L};
        Object[] statusStat3 = {"TREATING", 2L};
        when(treatmentHeadRepository.countByStatus()).thenReturn(Arrays.asList(statusStat1, statusStat2, statusStat3));
        when(treatmentHeadRepository.findLowBatteryHeads(20)).thenReturn(Arrays.asList(new com.Bone.BoneSys.entity.TreatmentHead()));
        when(treatmentHeadRepository.findHeadsNeedingReplacement()).thenReturn(Arrays.asList(new com.Bone.BoneSys.entity.TreatmentHead(), new com.Bone.BoneSys.entity.TreatmentHead()));
        
        // 模拟进程状态统计
        Object[] processStat1 = {"IN_PROGRESS", 25L};
        Object[] processStat2 = {"COMPLETED", 800L};
        Object[] processStat3 = {"CANCELLED", 65L};
        when(processRepository.countByStatus()).thenReturn(Arrays.asList(processStat1, processStat2, processStat3));
        when(processRepository.countTodayProcesses()).thenReturn(8L);
    }

    @Test
    void testGetMainDashboardData_Success() {
        System.out.println("🧪 测试主界面数据获取 - 成功场景");
        
        ApiResponse<?> response = dashboardController.getMainDashboard();
        
        // 验证响应状态
        assertNotNull(response, "响应不应为空");
        assertEquals(200, response.getCode(), "响应码应为200");
        assertEquals("主界面数据获取成功", response.getMessage(), "响应消息应正确");
        assertNotNull(response.getData(), "响应数据不应为空");
        
        // 验证数据结构
        Map<String, Object> data = (Map<String, Object>) response.getData();
        assertTrue(data.containsKey("systemInfo"), "应包含系统信息");
        assertTrue(data.containsKey("dataOverview"), "应包含数据概览");
        assertTrue(data.containsKey("treatmentHeadStatus"), "应包含治疗头状态");
        assertTrue(data.containsKey("processStatus"), "应包含进程状态");
        assertTrue(data.containsKey("hardwareStatus"), "应包含硬件状态");
        assertTrue(data.containsKey("recentActivities"), "应包含最近活动");
        assertTrue(data.containsKey("systemAlerts"), "应包含系统警告");
        
        System.out.println("✅ 主界面数据结构验证通过");
        
        // 验证数据概览
        Map<String, Object> dataOverview = (Map<String, Object>) data.get("dataOverview");
        assertEquals(150, dataOverview.get("totalPatients"), "患者总数应正确");
        assertEquals(320, dataOverview.get("totalRecords"), "档案总数应正确");
        assertEquals(890, dataOverview.get("totalProcesses"), "进程总数应正确");
        assertEquals(1250, dataOverview.get("totalTreatmentDetails"), "治疗详情总数应正确");
        assertEquals(20, dataOverview.get("totalTreatmentHeads"), "治疗头总数应正确");
        
        System.out.println("✅ 数据概览统计验证通过");
        
        // 验证治疗头状态统计
        Map<String, Object> headStatus = (Map<String, Object>) data.get("treatmentHeadStatus");
        assertEquals(3, headStatus.get("charging"), "充电中数量应正确");
        assertEquals(15, headStatus.get("charged"), "充电完成数量应正确");
        assertEquals(2, headStatus.get("treating"), "治疗中数量应正确");
        assertEquals(1, headStatus.get("lowBattery"), "低电量数量应正确");
        assertEquals(2, headStatus.get("needReplacement"), "需更换数量应正确");
        
        System.out.println("✅ 治疗头状态统计验证通过");
        
        // 验证进程状态统计
        Map<String, Object> processStatus = (Map<String, Object>) data.get("processStatus");
        assertEquals(25, processStatus.get("inProgress"), "进行中数量应正确");
        assertEquals(800, processStatus.get("completed"), "已完成数量应正确");
        assertEquals(65, processStatus.get("cancelled"), "已取消数量应正确");
        assertEquals(8, processStatus.get("todayNew"), "今日新增数量应正确");
        
        System.out.println("✅ 进程状态统计验证通过");
    }

    @Test
    void testGetMainDashboardData_SystemInfo() {
        System.out.println("🧪 测试系统信息模块");
        
        ApiResponse<?> response = dashboardController.getMainDashboard();
        Map<String, Object> data = (Map<String, Object>) response.getData();
        Map<String, Object> systemInfo = (Map<String, Object>) data.get("systemInfo");
        
        // 验证系统信息
        assertEquals("FREEBONE医疗系统", systemInfo.get("systemName"), "系统名称应正确");
        assertEquals("1.0.0", systemInfo.get("version"), "版本号应正确");
        assertEquals("运行中", systemInfo.get("status"), "运行状态应正确");
        assertNotNull(systemInfo.get("currentTime"), "当前时间不应为空");
        
        System.out.println("✅ 系统信息验证通过");
        System.out.println("   系统名称: " + systemInfo.get("systemName"));
        System.out.println("   版本号: " + systemInfo.get("version"));
        System.out.println("   运行状态: " + systemInfo.get("status"));
    }

    @Test
    void testGetMainDashboardData_HardwareStatus() {
        System.out.println("🧪 测试硬件状态模块");
        
        ApiResponse<?> response = dashboardController.getMainDashboard();
        Map<String, Object> data = (Map<String, Object>) response.getData();
        Map<String, Object> hardwareStatus = (Map<String, Object>) data.get("hardwareStatus");
        
        // 验证硬件状态
        assertNotNull(hardwareStatus.get("connected"), "连接状态不应为空");
        assertNotNull(hardwareStatus.get("lastSyncTime"), "最后同步时间不应为空");
        
        System.out.println("✅ 硬件状态验证通过");
        System.out.println("   连接状态: " + hardwareStatus.get("connected"));
        System.out.println("   最后同步: " + hardwareStatus.get("lastSyncTime"));
    }

    @Test
    void testGetMainDashboardData_SystemAlerts() {
        System.out.println("🧪 测试系统警告模块");
        
        ApiResponse<?> response = dashboardController.getMainDashboard();
        Map<String, Object> data = (Map<String, Object>) response.getData();
        java.util.List<Map<String, Object>> systemAlerts =
            (java.util.List<Map<String, Object>>) data.get("systemAlerts");
        
        // 验证系统警告
        assertNotNull(systemAlerts, "系统警告列表不应为空");
        
        // 检查是否有低电量警告
        boolean hasLowBatteryAlert = systemAlerts.stream()
            .anyMatch(alert -> "LOW_BATTERY".equals(alert.get("type")));
        assertTrue(hasLowBatteryAlert, "应有低电量警告");
        
        // 检查是否有需更换警告
        boolean hasReplacementAlert = systemAlerts.stream()
            .anyMatch(alert -> "NEED_REPLACEMENT".equals(alert.get("type")));
        assertTrue(hasReplacementAlert, "应有需更换警告");
        
        System.out.println("✅ 系统警告验证通过");
        System.out.println("   警告数量: " + systemAlerts.size());
        systemAlerts.forEach(alert -> 
            System.out.println("   - " + alert.get("type") + ": " + alert.get("message")));
    }

    @Test
    void testGetMainDashboardData_RecentActivities() {
        System.out.println("🧪 测试最近活动模块");
        
        ApiResponse<?> response = dashboardController.getMainDashboard();
        Map<String, Object> data = (Map<String, Object>) response.getData();
        java.util.List<Map<String, Object>> recentActivities =
            (java.util.List<Map<String, Object>>) data.get("recentActivities");
        
        // 验证最近活动
        assertNotNull(recentActivities, "最近活动列表不应为空");
        assertTrue(recentActivities.size() <= 5, "最近活动应不超过5条");
        
        System.out.println("✅ 最近活动验证通过");
        System.out.println("   活动数量: " + recentActivities.size());
    }

    @Test
    void testGetMainDashboardData_PerformanceMetrics() {
        System.out.println("🧪 测试性能指标");
        
        long startTime = System.currentTimeMillis();
        ApiResponse<?> response = dashboardController.getMainDashboard();
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        // 验证响应时间
        assertTrue(responseTime < 1000, "响应时间应小于1秒，实际: " + responseTime + "ms");
        
        System.out.println("✅ 性能指标验证通过");
        System.out.println("   响应时间: " + responseTime + "ms");
    }

    @Test
    void testGetMainDashboardData_DataConsistency() {
        System.out.println("🧪 测试数据一致性");
        
        ApiResponse<?> response = dashboardController.getMainDashboard();
        Map<String, Object> data = (Map<String, Object>) response.getData();
        
        // 验证治疗头状态数据一致性
        Map<String, Object> headStatus = (Map<String, Object>) data.get("treatmentHeadStatus");
        int totalHeads = (Integer) headStatus.get("charging") + 
                        (Integer) headStatus.get("charged") + 
                        (Integer) headStatus.get("treating") + 
                        (Integer) headStatus.get("available");
        
        assertEquals(20, totalHeads, "治疗头状态统计总数应等于治疗头总数");
        
        System.out.println("✅ 数据一致性验证通过");
        System.out.println("   治疗头状态统计总数: " + totalHeads);
    }
}
