-- Fix compartment_type field for treatment heads
USE bonesys;

-- Check current status
SELECT 'Current status' as info;
SELECT 
    head_number,
    compartment_type,
    CASE 
        WHEN compartment_type IS NULL THEN 'NULL'
        WHEN compartment_type = '' THEN 'EMPTY'
        ELSE compartment_type
    END as status
FROM treatment_heads 
ORDER BY head_number;

-- Add column if not exists
ALTER TABLE treatment_heads 
ADD COLUMN IF NOT EXISTS compartment_type VARCHAR(10) NULL DEFAULT NULL 
COMMENT 'Compartment type: SHALLOW(shallow upper), DEEP(deep lower)' 
AFTER slot_number;

-- Update compartment_type for all heads
UPDATE treatment_heads 
SET compartment_type = CASE 
    WHEN head_number BETWEEN 1 AND 10 THEN 'SHALLOW'
    WHEN head_number BETWEEN 11 AND 20 THEN 'DEEP'
    ELSE NULL
END
WHERE compartment_type IS NULL OR compartment_type = '';

-- Verify the fix
SELECT 'After fix' as info;
SELECT 
    head_number,
    compartment_type,
    realtime_status,
    battery_level,
    CASE 
        WHEN compartment_type = 'SHALLOW' AND battery_level >= 20 THEN 'SHALLOW_AVAILABLE'
        WHEN compartment_type = 'DEEP' AND battery_level >= 20 THEN 'DEEP_AVAILABLE'
        WHEN compartment_type IS NULL THEN 'NULL_COMPARTMENT'
        ELSE 'NOT_AVAILABLE'
    END as availability
FROM treatment_heads 
ORDER BY head_number;

-- Summary statistics
SELECT 'Summary' as info;
SELECT 
    compartment_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN battery_level >= 20 THEN 1 END) as available_count
FROM treatment_heads 
GROUP BY compartment_type
ORDER BY compartment_type;
