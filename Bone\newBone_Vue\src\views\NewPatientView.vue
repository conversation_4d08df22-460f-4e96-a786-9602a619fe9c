<template>
  <div class="page flex-col">
    <!-- 主界面 -->
    <div class="group_1 flex-col" v-if="!showNewPatientForm">
      <div class="section_1 flex-row justify-between">
          <img
          class="image_1 button"
            referrerpolicy="no-referrer"
          src="@/assets/images/treatmentsettingsview/img/ps0rbopt6gpo79v6n1nus8nyhtdkihjzd4vl8a5a6d1c-9d09-4ddd-a32d-e212ee18a676.png"
            @click="goBack"
          />
        <span class="text_1">新建档案</span>
        </div>
      <div class="text-wrapper_1 flex-col">
        <span class="text_2">搜索</span>
          <input 
            type="text" 
            v-model="searchKeyword" 
            :placeholder="inputFocusStates.search ? '' : '请输入关键词进行搜索'" 
            class="search-input"
            @input="searchPatients"
            @focus="handleInputFocus('search')"
            @blur="handleInputBlur('search')"
          />
        </div>
      <div class="section_3 flex-col">
        <div class="table-header flex-row">
          <span class="header-item">就诊卡号</span>
          <span class="header-item">姓名</span>
          <span class="header-item">年龄</span>
          <span class="header-item">性别</span>
          <span class="header-item">就诊时间</span>
          <span class="header-item">治疗部位</span>
          <span class="header-item">次数</span>
          </div>
        <img
          class="header-divider"
          referrerpolicy="no-referrer"
          src="@/assets/images/newpatient/0477715182022ad8be13ca873c0a369f.png"
        />
        
        <template v-if="paginatedPatients.length > 0">
          <div v-for="(patient, index) in paginatedPatients" :key="patient.id" class="patient-row-container">
            <div class="patient-row flex-row" 
                 @click="selectPatient(patient)" 
                 :class="{ 'selected': selectedPatient?.id === patient.id }">
              <span class="row-item">{{ patient.medicalRecordId || patient.medicalCardId }}</span>
              <span class="row-item">{{ patient.name }}</span>
              <span class="row-item">{{ patient.age }}</span>
              <span class="row-item">{{ patient.sex === 0 ? '男' : '女' }}</span>
              <span class="row-item">{{ formatDate(patient.creationTimestamp) }}</span>
              <span class="row-item">{{ patient.treatmentSite || '其他部位' }}</span>
              <span class="row-item">{{ patient.treatmentCount || 0 }}</span>
          </div>
            <img
              class="row-divider"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/psd3u43bdmbh0tnr5zdtfhn7f8ycpxfue931fa8813-8377-4d8c-961d-22533231d54c.png"
            />
          </div>
        </template>
          
        <!-- 空行填充，确保有分割线 -->
        <template v-if="paginatedPatients.length < 7">
          <div v-for="emptyIndex in (7 - paginatedPatients.length)" :key="'empty_' + emptyIndex" class="patient-row-container">
            <div class="patient-row flex-row empty-row">
              <span class="row-item">&nbsp;</span>
              <span class="row-item">&nbsp;</span>
              <span class="row-item">&nbsp;</span>
              <span class="row-item">&nbsp;</span>
              <span class="row-item">&nbsp;</span>
              <span class="row-item">&nbsp;</span>
              <span class="row-item">&nbsp;</span>
            </div>
            <img
              class="row-divider"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/psd3u43bdmbh0tnr5zdtfhn7f8ycpxfue931fa8813-8377-4d8c-961d-22533231d54c.png"
            />
          </div>
        </template>
        
        <!-- 最后一根分割线 -->
            <img
          class="row-divider final-divider"
              referrerpolicy="no-referrer"
          src="@/assets/images/newpatient/psd3u43bdmbh0tnr5zdtfhn7f8ycpxfue931fa8813-8377-4d8c-961d-22533231d54c.png"
        />

        <div class="pagination-container">
          <div class="pagination-controls">
            <div class="pagination-btn prev-btn button"
                @click="prevPage"
                :class="{ disabled: currentPage === 1 }">
              <img src="../assets/images/patientmangement/pscfuqtlgklge35gqvwmjemf1ftzt5cw0k164a5578-5e3c-4723-bc66-875bb1108d45.png" />
          </div>
            <div class="page-info">
              第 {{ currentPage }} 页/共 {{ totalPages || 1 }} 页
            </div>
            <div class="pagination-btn next-btn button"
                @click="nextPage"
                :class="{ disabled: currentPage === totalPages || totalPages === 0 }">
              <img src="../assets/images/patientmangement/psmqhsvt3d8c3z2wc722ghc6ga6sph03fs58fbf288-2ea0-4006-995a-1fb501817a56.png" />
            </div>
        </div>
      </div>
    </div>

      <!-- 底部新建和进入档案按钮 - 绝对定位 -->
      <div class="text-wrapper_3 flex-col button" @click="showNewPatientDialog">
        <span class="text_52">新建</span>
      </div>
      <div class="text-wrapper_4 flex-col button" @click="enterPatientFile" :class="{ 'disabled': !selectedPatient }">
        <span class="text_53">进入档案</span>
      </div>
    </div>

    <!-- 新建患者表单窗体 - 完全按照蓝湖UI设计 -->
    <div class="page flex-col" v-if="showNewPatientForm">
      <div class="dialog-section_1 flex-col">
        <div class="dialog-group_1 flex-col">
          <div class="dialog-image-wrapper_1 flex-row">
            <img
              class="dialog-label_1"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/psvaakst5ur2g9sbt96vss9sk9roa3t9ub4810b37a-9221-49d4-a318-b9ab65a1752d.png"
              @click="showNewPatientForm = false"
            />
          </div>
          <div class="dialog-image-wrapper_2 flex-row justify-between">
            <img
              class="dialog-image_1"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/pss3f2qsy734ck5ut6g86p8o9e17lzn6s7h70235b50-0e0c-4730-a5a0-27d7ce8e64e4.png"
            />
            <img
              class="dialog-image_2"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/pssx6e1wobkhof1dwjubaf85b5yti4k49mff34dc37-a309-4f9a-8043-22473eb559c3.png"
            />
          </div>
          <div class="dialog-image-wrapper_3 flex-row">
            <img
              class="dialog-image_3"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/ps8d2mz24ec9vcj2cvvn3u4uw4vzp1aztqfcf9b139-072b-435b-ae8d-ce1091d9a1f1.png"
            />
            <img
              class="dialog-image_4"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/psu94z9og90p9hgtg4u3zoawl6pxi6tgp92f388bfc-8e30-48d1-b703-a38be6971043.png"
            />
            <img
              class="dialog-image_5"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/pss8hgo14ez6og25cg9z5jpsbzgb7vd9c428f88-c553-43a2-92af-17a4b769bdc5.png"
            />
            <img
              class="dialog-image_6"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/psb3h7q429tsf43egd47mw0j0km4vi99vte986a5aaa-a73b-4a59-be19-74f1cbdff9d2.png"
            />
          </div>
          <div class="dialog-image-wrapper_4 flex-row">
            <img
              class="dialog-image_7"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/psz3f82vob8qkiumbor17dhn07uysrh8y8lp1f516418-102b-4fd6-8c14-e9bd49e0cd5c.png"
            />
            <img
              class="dialog-image_8"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/psdxceteah6j7jgyguqrd5b69gp6o4dvynbae039ca-58b3-4973-b680-3e5c4bac0fbd.png"
            />
            <img
              class="dialog-image_9"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/ps5xp1q2hxeq1xe4s2gfnb3bgm9u018y48cb7a7ff5-edef-4443-8309-e0221f00f4ec.png"
            />
            <img
              class="dialog-thumbnail_1"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/pseay7xn7in7jneawu94ly4fqtam3f2g24mb94fb3e5-0e3f-4a73-a769-7d8628cda3dc.png"
            />
            <img
              class="dialog-label_2"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/ps3q3mcu3j0p8q1dp5gzfh488dbbppvqrztb024e7ee-bdea-430f-8ef6-5edd7541b79c.png"
            />
            <img
              class="dialog-thumbnail_2"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/ps8x4nhdyic4ytewaq7mnv10v2l09hz3eo36f178b1-2b6d-4de6-a27d-f6f371eeb831.png"
            />
            <img
              class="dialog-label_3"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/pse21n6wlymca5p5bavmc1t431g1wuthmb617758e8-cc0a-4687-8352-1f1738d376d7.png"
            />
          </div>
          <div class="dialog-image-wrapper_5 flex-row justify-between">
            <img
              class="dialog-image_10"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/psubtpsxtatpxu29xxm2rd928swguy0yq44cfb731-1d0d-418f-b02a-dd1e0ae48fbe.png"
            />
            <img
              class="dialog-image_11"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/pskg1fwa2fbqdw4neto4yujmb7s55wxp6f4406566-32d3-4e61-b8f8-97dd07a40545.png"
            />
          </div>
          <div class="dialog-image-wrapper_6 flex-row justify-between">
            <img
              class="dialog-image_12"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/ps7im80pmtakffitvvo2b8qon3ordxp0cumbf688e50-9add-46f1-a6ed-957e05bb7f7a.png"
            />
            <img
              class="dialog-image_13"
              referrerpolicy="no-referrer"
              src="@/assets/images/newpatient/dialog/pstyo0j8f5leikbqeqnr6mmatgzch8limkda377a96-133d-442c-8d9b-1cd87da1b82f.png"
            />
          </div>
          <div class="dialog-image-wrapper_7 flex-row">
            <div class="create-btn-container" @click="createPatient">
              <img
                class="dialog-image_14 btn-hover-effect"
                referrerpolicy="no-referrer"
                src="@/assets/images/newpatient/dialog/pscs52b7pivtfeqhdwaimrio6xtgslidhjb3952d35d-50e9-4119-b2a8-d715c14e349b.png"
              />
              <span class="create-btn-text">确&nbsp;&nbsp;&nbsp;认</span>
            </div>
          </div>
          
          <!-- 透明输入框，绝对定位覆盖在对应图片上，尺寸位置完全一致 -->
          <!-- 编号输入框 - 覆盖在 dialog-image_2 上 -->
          <input 
            type="text" 
            v-model="newPatient.medicalRecordId" 
            placeholder="" 
            disabled 
            class="dialog-transparent-input dialog-id-input" 
          />
          
          <!-- 姓名输入框 - 覆盖在 dialog-image_4 上 -->
          <input 
            type="text" 
            v-model="newPatient.name" 
            :placeholder="inputFocusStates.name ? '' : '请输入'" 
            class="dialog-transparent-input dialog-name-input"
            @focus="handleInputFocus('name')"
            @blur="handleInputBlur('name')"
          />
          
          <!-- 就诊卡号输入框 - 覆盖在 dialog-image_6 上 -->
          <input 
            type="text" 
            v-model="newPatient.medicalCardId" 
            :placeholder="inputFocusStates.medicalCardId ? '' : '请输入'" 
            class="dialog-transparent-input dialog-card-input"
            @focus="handleInputFocus('medicalCardId')"
            @blur="handleInputBlur('medicalCardId')"
          />
          
          <!-- 年龄输入框 - 覆盖在 dialog-image_8 上 -->
          <input 
            type="number" 
            v-model="newPatient.age" 
            :placeholder="inputFocusStates.age ? '' : '请输入'" 
            min="0"
            max="150"
            class="dialog-transparent-input dialog-age-input"
            @focus="handleInputFocus('age')"
            @blur="handleInputBlur('age')"
          />
          
          <!-- 性别选择 - 覆盖在 thumbnail_1 和 thumbnail_2 位置 -->
          <div class="dialog-radio-container">
            <div class="dialog-male-label">
              <input type="radio" name="sex" :value="0" v-model="newPatient.sex" id="male-radio" />
              <label for="male-radio"></label>
          </div>
            <div class="dialog-female-label">
              <input type="radio" name="sex" :value="1" v-model="newPatient.sex" id="female-radio" />
              <label for="female-radio"></label>
            </div>
          </div>
          
          <!-- 联系电话输入框 - 覆盖在 dialog-image_11 上 -->
          <input 
            type="text" 
            v-model="newPatient.contactInfo" 
            :placeholder="inputFocusStates.contactInfo ? '' : '请输入'" 
            class="dialog-transparent-input dialog-phone-input"
            @focus="handleInputFocus('contactInfo')"
            @blur="handleInputBlur('contactInfo')"
          />
          
          <!-- 诊断输入框 - 覆盖在 dialog-image_13 上 -->
          <textarea 
            v-model="newPatient.diagnosis" 
            :placeholder="inputFocusStates.diagnosis ? '' : '请输入诊断信息'" 
            class="dialog-transparent-input dialog-diagnosis-input"
            @focus="handleInputFocus('diagnosis')"
            @blur="handleInputBlur('diagnosis')"
          ></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { 
  getEnhancedCandidates,
  getRecordsCandidates, 
  getNextPatientNumber as apiGetNextPatientNumber, 
  createPatient as apiCreatePatient 
} from '@/api';

const router = useRouter();

// 搜索条件
const searchKeyword = ref('');

// 分页参数
const currentPage = ref(1);
const pageSize = ref(7); // 每页7条数据
const totalPatients = ref(0);

// 患者数据
const patients = ref<any[]>([]);
const selectedPatient = ref<any>(null);

// 新建患者表单显示状态
const showNewPatientForm = ref(false);
const newPatient = ref({
  name: '',
  medicalCardId: '',
  medicalRecordId: '',
  sex: 0,
  age: null as number | null,
  contactInfo: '',
  diagnosis: ''
});

// 输入框焦点状态
const inputFocusStates = ref({
  search: false,
  name: false,
  medicalCardId: false,
  age: false,
  contactInfo: false,
  diagnosis: false
});

// 过滤后的患者数据
const filteredPatientsComputed = computed(() => {
  let result = [...patients.value];
  
  // 根据搜索关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(patient => 
      (patient.name && patient.name.toLowerCase().includes(keyword)) ||
      (patient.medicalRecordId && patient.medicalRecordId.toLowerCase().includes(keyword)) ||
      (patient.medicalCardId && patient.medicalCardId.toLowerCase().includes(keyword))
    );
  }
  
  return result;
});

// 当前页显示的患者数据（每页6条）
const paginatedPatients = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  return filteredPatientsComputed.value.slice(startIndex, startIndex + pageSize.value);
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredPatientsComputed.value.length / pageSize.value);
});

// 获取患者列表
const fetchPatients = async () => {
  try {
    console.log('开始获取候选患者列表...');
    
    // 使用新的增强API获取真实数据
    const response = await getEnhancedCandidates({
      page: 1,
      size: 1000, // 获取足够大的数据量
      search: undefined // 前端自己处理搜索
    });
    
    console.log('API响应:', response);
    
    // 处理新的API响应格式
    if (response.data && response.data.candidates && Array.isArray(response.data.candidates)) {
      // 将增强的候选数据转换为前端需要的格式
      patients.value = response.data.candidates.map((candidate: any) => ({
        id: candidate.patientId || `temp_${Date.now()}_${Math.random()}`,
        patientId: candidate.patientId, // 真实的患者ID
        name: candidate.name || '未知患者',
        medicalCardId: candidate.cardId || '未知',
        medicalRecordId: candidate.cardId || '未知', // 使用cardId作为医疗记录ID
        sex: candidate.gender === '男' ? 0 : 1,
        age: candidate.age?.replace('岁', '') || '未知',
        contactInfo: '', // 候选列表中没有联系方式
        creationTimestamp: candidate.visitTime || '', // 使用真实的就诊时间
        treatmentSite: candidate.treatmentParts || '待确定', // 使用真实的治疗部位
        treatmentCount: candidate.totalSessions || 0 // 使用真实的治疗次数
      }));
      
      // 设置总数据量
      totalPatients.value = patients.value.length;
      
      console.log(`成功获取${patients.value.length}条候选患者数据`);
      
      // 记录数据质量信息
      const withRecords = patients.value.filter(p => p.creationTimestamp && p.creationTimestamp !== '无档案').length;
      const withTreatments = patients.value.filter(p => p.treatmentCount > 0).length;
      console.log(`数据质量: 有档案记录${withRecords}/${patients.value.length}, 有治疗记录${withTreatments}/${patients.value.length}`);
      
    } else {
      console.warn('API响应格式异常，尝试使用兼容模式');
      // 降级到旧版API
      await fetchPatientsLegacy();
    }
  } catch (error) {
    console.error('获取增强候选列表失败:', error);
    console.log('尝试使用兼容模式...');
    
    // 降级到旧版API
    try {
      await fetchPatientsLegacy();
    } catch (legacyError) {
      console.error('兼容模式也失败:', legacyError);
      MessagePlugin.error('获取候选列表失败，请稍后重试');
      patients.value = [];
      totalPatients.value = 0;
    }
  }
};

// 兼容模式的患者列表获取
const fetchPatientsLegacy = async () => {
  const response = await getRecordsCandidates({
    page: 1,
    size: 1000,
    search: undefined
  });
  
  if (response.data && response.data.candidates && Array.isArray(response.data.candidates)) {
    patients.value = response.data.candidates.map((candidate: any) => ({
      id: candidate.patientId || candidate.id || `temp_${Date.now()}_${Math.random()}`,
      patientId: candidate.patientId,
      name: candidate.name,
      medicalCardId: candidate.cardId,
      medicalRecordId: candidate.cardId,
      sex: candidate.gender === '男' ? 0 : 1,
      age: candidate.age?.replace('岁', '') || '',
      contactInfo: candidate.phone || '',
      creationTimestamp: candidate.appointmentTime,
      treatmentSite: candidate.bodyPart || '其他部位',
      treatmentCount: candidate.sessions || 0
    }));
    
    totalPatients.value = patients.value.length;
    console.log(`兼容模式获取${patients.value.length}条数据`);
  } else {
    patients.value = [];
    totalPatients.value = 0;
  }
};

// 搜索患者
const searchPatients = () => {
  currentPage.value = 1; // 搜索时重置到第一页
};

// 分页方法
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 根据列位置获取患者数据
const getPatientDataByPosition = (colIndex: number, patient: any) => {
  if (!patient) {
    return '';
  }
  
  try {
    switch (colIndex) {
      case 1: // 就诊卡号
        return patient.medicalRecordId || patient.medicalCardId || '未知';
      case 2: // 姓名
        return patient.name || '未知患者';
      case 3: // 年龄
        const age = patient.age ? patient.age.toString() : '未知';
        return age.endsWith('岁') ? age : (age === '未知' ? age : age + '岁');
      case 4: // 性别
        return patient.sex === 0 ? '男' : '女';
      case 5: // 就诊时间
        return formatDate(patient.creationTimestamp) || '无档案';
      case 6: // 治疗部位
        return patient.treatmentSite || '待确定';
      case 7: // 次数
        return (patient.treatmentCount || 0).toString();
      default:
        return '';
    }
  } catch (error) {
    console.warn('获取患者数据失败:', colIndex, patient, error);
    return '';
  }
};

// 验证患者数据完整性
const validatePatientData = (patient: any) => {
  if (!patient) return false;
  
  return patient.patientId && 
         patient.name && 
         patient.medicalCardId &&
         patient.name !== '未知患者' &&
         patient.medicalCardId !== '未知';
};

// 获取数据完整性提示
const getDataCompletenessHint = (patient: any) => {
  if (!patient) return '数据不存在';
  
  const hints = [];
  
  if (!patient.creationTimestamp || patient.creationTimestamp === '无档案') {
    hints.push('尚未建立档案记录');
  }
  
  if (!patient.treatmentSite || patient.treatmentSite === '待确定') {
    hints.push('治疗部位待确定');
  }
  
  if (!patient.treatmentCount || patient.treatmentCount === 0) {
    hints.push('尚未开始治疗');
  }
  
  if (patient.name === '未知患者') {
    hints.push('患者姓名信息缺失');
  }
  
  if (patient.medicalCardId === '未知') {
    hints.push('就诊卡号信息缺失');
  }
  
  return hints.length > 0 ? hints.join('；') : '数据完整';
};



// 选择患者
const selectPatient = (patient: any) => {
  selectedPatient.value = patient;
  
  // 记录选择的患者信息（用于调试）
  console.log('选择患者:', {
    patientId: patient.patientId,
    name: patient.name,
    cardId: patient.medicalCardId,
    hasRecords: patient.creationTimestamp && patient.creationTimestamp !== '无档案',
    hasTreatments: patient.treatmentCount > 0,
    completenessHint: getDataCompletenessHint(patient)
  });
};

// 显示新建患者表单
const showNewPatientDialog = () => {
  // 重置表单
  newPatient.value = {
    name: '',
    medicalCardId: '',
    medicalRecordId: '',
    sex: 0,
    age: null as number | null,
    contactInfo: '',
    diagnosis: ''
  };
  
  showNewPatientForm.value = true;
};

// 获取下一个患者编号
const getNextPatientNumber = async (): Promise<string> => {
  try {
    const response = await apiGetNextPatientNumber();
    return response.data.patientNumber;
  } catch (error) {
    console.error('获取患者编号失败:', error);
    // 如果API调用失败，生成一个简单的编号
    const timestamp = Date.now().toString().slice(-6);
    return `P${timestamp}`;
  }
};

// 创建新患者
const createPatient = async (event: Event) => {
  // 添加点击动画
  const button = event.target as HTMLElement;
  button.classList.add('clicked');
  setTimeout(() => {
    button.classList.remove('clicked');
  }, 600);
  
  // 验证必填项
  if (!newPatient.value.name) {
    MessagePlugin.warning('请输入患者姓名');
    return;
  }
  
  if (!newPatient.value.medicalCardId) {
    MessagePlugin.warning('请输入就诊卡号');
    return;
  }
  
  try {
    // 获取下一个患者编号
    const patientNumber = await getNextPatientNumber();
    
    // 准备提交的数据，适配新的后端API格式
    const patientData = {
      patientNumber: patientNumber,
      name: newPatient.value.name,
      age: newPatient.value.age ? newPatient.value.age.toString() : '',
      gender: newPatient.value.sex === 0 ? '男' : '女',
      phone: newPatient.value.contactInfo || '',
      cardId: newPatient.value.medicalCardId,
      diagnosisDescription: newPatient.value.diagnosis || ''
    };
    
    const response = await apiCreatePatient(patientData);
    
    MessagePlugin.success('患者创建成功');
    showNewPatientForm.value = false;
    
    // 刷新患者列表
    await fetchPatients();
    
    // 跳转到参数设置页面
    router.push(`/treatment/new/${response.data.patientId}`);
  } catch (error: any) {
    console.error('创建患者失败:', error);
    if (error.response && error.response.data && error.response.data.message) {
      MessagePlugin.error(error.response.data.message);
    } else {
      MessagePlugin.error('创建患者失败，请稍后重试');
    }
  }
};

// 进入患者档案
const enterPatientFile = () => {
  if (selectedPatient.value) {
    // 使用真实的患者ID，如果没有则使用id
    const patientId = selectedPatient.value.patientId || selectedPatient.value.id;
    router.push(`/treatment/new/${patientId}`);
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  
  // 如果已经是YYYY.MM.DD格式，直接返回
  if (dateString.match(/^\d{4}\.\d{2}\.\d{2}$/)) {
    return dateString;
  }
  
  // 处理特殊值
  if (dateString === '无档案' || dateString === '数据异常') {
    return dateString;
  }
  
  // 尝试解析日期
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString; // 如果无法解析，返回原值
    }
    return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
  } catch (error) {
    console.warn('日期格式化失败:', dateString, error);
    return dateString;
  }
};

// 返回主页
const goBack = () => {
  router.push('/home');
};

// 处理输入框聚焦和失焦
const handleInputFocus = (inputName: string) => {
  inputFocusStates.value[inputName as keyof typeof inputFocusStates.value] = true;
};

const handleInputBlur = (inputName: string) => {
  inputFocusStates.value[inputName as keyof typeof inputFocusStates.value] = false;
};

// 数据刷新功能
const refreshPatients = async () => {
  console.log('手动刷新患者数据...');
  await fetchPatients();
  MessagePlugin.success('数据已刷新');
};

// 页面加载时获取患者列表
onMounted(() => {
  console.log('页面加载，开始获取患者列表...');
  fetchPatients();
  
  // 添加键盘快捷键支持（F5刷新数据）
  const handleKeyPress = (event: KeyboardEvent) => {
    if (event.key === 'F5') {
      event.preventDefault();
      refreshPatients();
    }
  };
  
  document.addEventListener('keydown', handleKeyPress);
  
  // 清理事件监听器
  return () => {
    document.removeEventListener('keydown', handleKeyPress);
  };
});
</script>

<style scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.group_1 {
  width: 1920px;
  height: 1080px;
  background: url(@/assets/images/newpatient/729506f7b0e9c8ed38af1c322c6e9cba.png) -1px 0px no-repeat;
  background-size: 1921px 1080px;
}

/* 顶部标题区域 */
.section_1 {
  width: 998px;
  height: 70px;
  margin: 18px 0 0 104px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image_1 {
  width: 151px;
  margin-left: -4px;
  margin-top: 10px;
  height: 61px;
  cursor: pointer;
  transition: transform 0.1s ease;
}

.text_1 {
  width: 294px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41.67px;
  font-family: MicrosoftYaHei, sans-serif;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 8px;
}

/* 搜索区域 */
.text-wrapper_1 {
  height: 95px;
  background: url(@/assets/images/newpatient/f76b271dd10ae46f4ad862f0468cb5e2.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 790px;
  margin: 46px 0 0 130px;
  display: flex;
  align-items: center;
  position: relative;
}

.text_2 {
  width: 88px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei, sans-serif;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 25px 0 655px;
}

.search-input {
  position: absolute;
  left: 20px;
  top: 30px;
  transform: translateY(-50%);
  width: 580px;
  height: 60px;
  border: none;
  background: transparent;
  font-size: 24px;
  color: #333;
  outline: none;
  padding: 0 15px;
  font-family: MicrosoftYaHei, sans-serif;
}

.search-input::placeholder {
  color: rgba(154, 154, 154, 0.7);
  font-family: MicrosoftYaHei, sans-serif;
  font-size: 24px;
}

/* 主表格区域 - 按照用户要求精确定位 */
.section_3 {
  width: 1689px;
  height: 837px;
  background: url(@/assets/images/newpatient/b7f4af218e44f0d3b8bc57a569417bba.png) 100% no-repeat;
  background-size: 100% 100%;
  position: absolute;
  left: 112px;
  top: 229px;
}

.table-header {
  width: 1350px;
  height: 58px; /* 用户要求58px高度 */
  margin: -10px 0 0 168px;
  display: flex;
  align-items: center;
}

.header-item {
    
  font-size: 25px;
  font-family: MicrosoftYaHei, sans-serif;
  text-align: center;
  white-space: nowrap;
  margin-top: 100px;
  color: rgba(0, 0, 0, 1);
}


/* 新建档案的7列布局，就诊卡号加宽显示完全 */
.header-item:nth-child(1) { width: 200px; }                    /* 就诊卡号 - 加宽显示完全 */
.header-item:nth-child(2) { width: 71px; margin-left: 68px; }  /* 姓名 */
.header-item:nth-child(3) { width: 74px; margin-left: 94px; }  /* 年龄 */
.header-item:nth-child(4) { width: 73px; margin-left: 61px; }  /* 性别 */
.header-item:nth-child(5) { width: 157px; margin-left: 99px; } /* 就诊时间 */
.header-item:nth-child(6) { width: 155px; margin-left: 75px; } /* 治疗部位 */
.header-item:nth-child(7) { width: 75px; margin-left: 166px; } /* 次数 */

.header-divider {
  width: 1476px;
  height: 1px;
  margin: 60px 0 0 116px;
}

.patient-row-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.patient-row {
  width: 1500px;
  height: 70px; /* 用户要求的70px高度 */
  margin: 0 0 0 108px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.patient-row:hover:not(.empty-row) {
   background-color: rgba(98, 215, 226, 0.2);

}

.patient-row.selected {
   background-color: rgba(98, 215, 226, 0.2);

}

.patient-row.empty-row {
  cursor: default;
}

.row-item {
  margin-left: 55px;
  font-size: 25px;
  font-family: MicrosoftYaHei, sans-serif;
  text-align: center;
  white-space: nowrap;
  color: #9A9A9A;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 数据行的7列布局，就诊卡号加宽显示完全 */
.row-item:nth-child(1) { width: 200px; }                    /* 就诊卡号 - 加宽显示完全 */
.row-item:nth-child(2) { width: 100px; margin-left: 48px; }  /* 姓名 */
.row-item:nth-child(3) { width: 74px; margin-left: 74px; }  /* 年龄 */
.row-item:nth-child(4) { width: 73px; margin-left: 61px; }  /* 性别 */
.row-item:nth-child(5) { width: 157px; margin-left: 99px; } /* 就诊时间 */
.row-item:nth-child(6) { width: 155px; margin-left: 65px; } /* 治疗部位 */
.row-item:nth-child(7) { width: 75px; margin-left: 166px; } /* 次数 */

/* 分割线位置 - 第一根116，108px，后面每根加70px */
.row-divider {
  position: absolute;
  width: 1476px;
  height: 1px;
  left: 116px;
}

/* 分割线精确定位 */
.patient-row-container:nth-child(3) .row-divider { top: 108px; } /* 第1根：108px */
.patient-row-container:nth-child(4) .row-divider { top: 178px; } /* 第2根：108+70=178px */
.patient-row-container:nth-child(5) .row-divider { top: 248px; } /* 第3根：178+70=248px */
.patient-row-container:nth-child(6) .row-divider { top: 318px; } /* 第4根：248+70=318px */
.patient-row-container:nth-child(7) .row-divider { top: 388px; } /* 第5根：318+70=388px */
.patient-row-container:nth-child(8) .row-divider { top: 458px; } /* 第6根：388+70=458px */
.patient-row-container:nth-child(9) .row-divider { top: 528px; } /* 第7根：458+70=528px */

/* 空行分割线也要定位 */
.patient-row-container[data-empty="true"]:nth-child(3) .row-divider { top: 108px; }
.patient-row-container[data-empty="true"]:nth-child(4) .row-divider { top: 178px; }
.patient-row-container[data-empty="true"]:nth-child(5) .row-divider { top: 248px; }
.patient-row-container[data-empty="true"]:nth-child(6) .row-divider { top: 318px; }
.patient-row-container[data-empty="true"]:nth-child(7) .row-divider { top: 388px; }
.patient-row-container[data-empty="true"]:nth-child(8) .row-divider { top: 458px; }
.patient-row-container[data-empty="true"]:nth-child(9) .row-divider { top: 528px; }

/* 最后一根分割线定位 */
.final-divider {
  top: 598px; /* 第8根分割线：528+70=598px */
}



/* 分页样式 - 相对表格定位 */
.pagination-container {
  position: absolute;
  left: 1336px;
  top: 638px;
  /* 长和宽是235px, 38px */

  width: 310px;
  height: 110%;

  bottom: 60px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 6px 18px;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-info {
  margin: 0 18px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 19px;
  color: #333;
}

.pagination-btn {
  width: 50px;
  height: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(.disabled) {
  transform: scale(1.05);
}


.pagination-btn img {
  width: 46px;
  height: 46px;
}

.pagination-btn:hover:not(.disabled) {
  transform: scale(1.05);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.text-wrapper_3 {
  height: 77px;
  background: url(@/assets/images/newpatient/ee2226d2bc022e95a4140f84b614e115.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 216px;
  position: absolute;
  left: 541px;
  top: 887px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.1s ease;
}

.text-wrapper_3:hover {
  transform: scale(1.05);
}

.text-wrapper_3:active {
  transform: scale(0.95);
}

.text_52 {
  width: 88px;
  height: 39px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei, sans-serif;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text-wrapper_4 {
  height: 77px;
  background: url(@/assets/images/newpatient/1c8e5eddae0e5143f83eda86a9623880.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 216px;
  position: absolute;
  left: 1160px;
  top: 887px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.1s ease;
}

.text-wrapper_4:hover:not(.disabled) {
  transform: scale(1.05);
}

.text-wrapper_4:active:not(.disabled) {
  transform: scale(0.95);
}

.text-wrapper_4.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.text_53 {
  width: 173px;
  height: 39px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei, sans-serif;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

/* 按钮交互效果 */
.button {
  transition: transform 0.1s ease;
}

.button:active {
  transform: scale(0.95);
}

/* 新建患者表单窗体样式 - 完全按照蓝湖UI设计 */
.dialog-section_1 {
  height: 1080px;
  background: url(@/assets/images/newpatient/dialog/pszzvunw777lixipbg2gagmn8qfsgfiyo3f1db3a3-b9d7-4087-b63d-b3ef0d19bf75.png) -21px -1px no-repeat;
  background-size: 1961px 1085px;
  width: 1920px;
}

.dialog-group_1 {
  height: 965px;
  background: url(@/assets/images/newpatient/dialog/psrmn9ikzcjdm7l86rx7kwqzsjawmq9c44947404-37fe-4936-8990-95a748e3a419.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 1217px;
  margin: 62px 0 0 352px;
  position: relative;
}

.dialog-image-wrapper_1 {
  width: 49px;
  height: 49px;
  margin: 41px 0 0 1117px;
}

.dialog-label_1 {
  width: 49px;
  height: 49px;
  cursor: pointer;
}

.dialog-image-wrapper_2 {
  width: 515px;
  height: 48px;
  margin: 89px 0 0 191px;
}

.dialog-image_1 {
  width: 66px;
  height: 26px;
  margin-top: 10px;
}

.dialog-image_2 {
  width: 425px;
  height: 48px;
}

.dialog-image-wrapper_3 {
  width: 879px;
  height: 48px;
  margin: 43px 0 0 192px;
}

.dialog-image_3 {
  width: 80px;
  height: 27px;
  margin-top: 5px;
}

.dialog-image_4 {
  width: 213px;
  height: 47px;
  margin: 1px 0 0 9px;
}

.dialog-image_5 {
  width: 137px;
  height: 26px;
  margin: 7px 0 0 187px;
}

.dialog-image_6 {
  width: 239px;
  height: 47px;
  margin-left: 14px;
}

.dialog-image-wrapper_4 {
  width: 740px;
  height: 47px;
  margin: 42px 0 0 193px;
}

.dialog-image_7 {
  width: 64px;
  height: 26px;
  margin-top: 9px;
}

.dialog-image_8 {
  width: 213px;
  height: 47px;
  margin-left: 24px;
}

.dialog-image_9 {
  width: 66px;
  height: 27px;
  margin: 9px 0 0 189px;
}

.dialog-thumbnail_1 {
  width: 18px;
  height: 18px;
  margin: 14px 0 0 29px;
}

.dialog-label_2 {
  width: 24px;
  height: 26px;
  margin: 10px 0 0 10px;
}

.dialog-thumbnail_2 {
  width: 18px;
  height: 18px;
  margin: 14px 0 0 50px;
}

.dialog-label_3 {
  width: 25px;
  height: 26px;
  margin: 10px 0 0 10px;
}

.dialog-image-wrapper_5 {
  width: 417px;
  height: 47px;
  margin: 56px 0 0 192px;
}

.dialog-image_10 {
  width: 123px;
  height: 27px;
  margin-top: 7px;
}

.dialog-image_11 {
  width: 269px;
  height: 47px;
}

.dialog-image-wrapper_6 {
  width: 848px;
  height: 233px;
  margin: 34px 0 0 192px;
}

.dialog-image_12 {
  width: 65px;
  height: 27px;
  margin-top: 16px;
}

.dialog-image_13 {
  width: 752px;
  height: 233px;
}

.dialog-image-wrapper_7 {
  width: 296px;
  height: 122px;
  margin: 28px 0 38px 465px;
}

.dialog-image_14 {
  width: 187px;
  height: 78px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.dialog-image_14:hover {
  filter: brightness(1.1);
}

.dialog-image_14:active {
  transition: all 0.1s ease;
}

/* 创建按钮点击动画 */
@keyframes buttonPop {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.dialog-image_14.clicked {
  animation: buttonPop 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 创建按钮容器样式 */
.create-btn-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 296px;
  height: 122px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.create-btn-container:hover {
  transform: scale(1.05);
}

.create-btn-container:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

/* 确认文字样式 */
.create-btn-text {
  position: absolute;
  bottom: 38x;
  left: 50%;
  transform: translateX(-50%);
  font-size: 33px;
  font-family: MicrosoftYaHei, sans-serif;
  color: rgba(89, 89, 89, 1);
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  pointer-events: none;
}

/* 透明输入框样式 - 绝对定位覆盖在对应图片上 */
.dialog-transparent-input {
  position: absolute;
  border: none;
  background: transparent;
  font-size: 25px;
  color: #333;
  outline: none;
  z-index: 10;
  padding: 0 15px;
  margin: 0;
  font-weight: 500;
  line-height: normal;
}

/* 编号输入框 - 覆盖在 dialog-image_2 上 */
.dialog-id-input {
  width: 425px;
  height: 48px;
  left: 281px;
  top: 179px;
}

/* 姓名输入框 - 覆盖在 dialog-image_4 上 */
.dialog-name-input {
  width: 213px;
  height: 47px;
  left: 281px; /* 192px + 80px + 9px + 20px = image-wrapper_3 margin-left + image_3 width + image_4 margin + 调整 */
  top: 271px; /* 89px + 43px + 48px + 53px = 各层级累计 + 调整 */
}

/* 就诊卡号输入框 - 覆盖在 dialog-image_6 上 */
.dialog-card-input {
  width: 239px;
  height: 47px;
  left: 832px; /* 192px + 80px + 213px + 187px + 29px = 各层级位置累计 + 调整 */
  top: 271px; /* 与姓名输入框同行 */
}

/* 年龄输入框 - 覆盖在 dialog-image_8 上 */
.dialog-age-input {
  width: 213px;
  height: 47px;
  left: 281px; /* 193px + 64px + 24px + 20px = 各层级位置累计 + 调整 */
  top: 360px; /* 89px + 43px + 42px + 48px + 58px = 各层级margin-top累计 + 调整 */
}

/* 性别选择 - 覆盖在 thumbnail_1 和 thumbnail_2 位置 */
.dialog-radio-container {
  position: absolute;
  left: 777px; /* 193px + 64px + 213px + 189px + 44px = 各层级位置累计 + 调整 */
  top: 372px; /* 年龄输入框位置 + 14px 调整 */
  width: 150px;
  height: 18px;
  z-index: 10;
}

.dialog-male-label {
  position: absolute;
  left: 0px; /* thumbnail_1 的 margin-left */
  top: 0px;
  cursor: pointer;
  width: 30px;
  height: 20px;
}

.dialog-female-label {
  position: absolute;
  left: 100px; /* thumbnail_2 的相对位置，调整间距 */
  top: 0px;
  cursor: pointer;
  width: 30px;
  height: 20px;
}

.dialog-radio-container input[type="radio"] {
  width: 20px;
  height: 20px;
  opacity: 0;
  position: absolute;
  cursor: pointer;
}

/* 自定义单选按钮样式 - 实心点 */
.dialog-radio-container label {
  position: relative;
  cursor: pointer;
  padding-left: 30px;
}

.dialog-radio-container label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  /*border: 2px solid #666;*/
  border-radius: 100%;
  background: transparent;
  transition: all 0.3s ease;
}

.dialog-radio-container input[type="radio"]:checked + label::before,
.dialog-radio-container label:has(input[type="radio"]:checked)::before {
  background: #4a90e2;
  border-color: #4a90e2;
}

.dialog-radio-container label::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 6px;
  width: 8px;
  height: 8px;
  border-radius: 100%;
  background: white;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.dialog-radio-container input[type="radio"]:checked + label::after,
.dialog-radio-container label:has(input[type="radio"]:checked)::after {
  transform: scale(1);
}

/* 联系电话输入框 - 覆盖在 dialog-image_11 上 */
.dialog-phone-input {
  width: 269px;
  height: 47px;
  left: 340px; /* 192px + 123px + 24px = 各层级位置累计 + 调整 */
  top: 463px; /* 89px + 43px + 42px + 56px + 48px + 81px = 各层级margin-top累计 + 调整 */
}

/* 诊断输入框 - 覆盖在 dialog-image_13 上 */
.dialog-diagnosis-input {
  width: 752px;
  height: 233px;
  left: 288px; /* 192px + 65px + 24px = 各层级位置累计 + 调整 */
  top: 544px; /* 89px + 43px + 42px + 56px + 34px + 48px + 113px = 各层级margin-top累计 + 调整 */
  padding: 15px;
  resize: none;
  font-size: 25px;
  font-weight: 500;
  line-height: 1.4;
}

/* 按钮悬停效果 */
.btn-hover-effect {
  transition: transform 0.3s ease, filter 0.3s ease;
}

.btn-hover-effect:hover {
  filter: brightness(1.05);
}

.btn-hover-effect:active {
  transform: scale(0.95);
}

/* flex布局样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style> 