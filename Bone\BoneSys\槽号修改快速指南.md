# 🚀 槽号修改快速指南

## ❌ SQL错误解决方案

刚才的SQL执行出现语法错误，这里提供几种解决方案：

## 🔧 方案1：手动执行SQL（推荐）

### **步骤1：打开MySQL客户端**
```bash
mysql -u root -p
```

### **步骤2：选择数据库**
```sql
USE bonesys;
```

### **步骤3：逐步执行修改**
```sql
-- 添加仓位类型字段
ALTER TABLE treatment_heads 
ADD COLUMN compartment_type VARCHAR(10) NULL DEFAULT NULL 
COMMENT '仓位类型: SHALLOW(浅部上层), DEEP(深部下层)' 
AFTER slot_number;

-- 更新仓位类型数据
UPDATE treatment_heads 
SET compartment_type = 'SHALLOW' 
WHERE head_number BETWEEN 1 AND 10;

UPDATE treatment_heads 
SET compartment_type = 'DEEP' 
WHERE head_number BETWEEN 11 AND 20;

-- 重置槽号
UPDATE treatment_heads 
SET slot_number = NULL;

-- 创建索引
CREATE INDEX idx_compartment_type ON treatment_heads (compartment_type);
CREATE INDEX idx_compartment_slot ON treatment_heads (compartment_type, slot_number);
```

### **步骤4：验证结果**
```sql
SELECT 
    head_number as '治疗头编号',
    slot_number as '槽号',
    compartment_type as '仓位类型',
    CASE 
        WHEN compartment_type = 'SHALLOW' THEN '上层浅部'
        WHEN compartment_type = 'DEEP' THEN '下层深部'
        ELSE '未知'
    END as '仓位描述'
FROM treatment_heads 
ORDER BY head_number;
```

## 🔧 方案2：使用简化版SQL文件

```bash
# 执行简化版SQL
mysql -u root -p bonesys < "SQL/修改槽号逻辑_简化版.sql"
```

## 🔧 方案3：使用MySQL Workbench

1. 打开MySQL Workbench
2. 连接到bonesys数据库
3. 复制`SQL/手动执行槽号修改.sql`中的语句
4. 逐步执行每个语句

## ✅ 验证修改成功

执行以下查询确认修改成功：

```sql
-- 检查表结构
DESCRIBE treatment_heads;

-- 检查数据
SELECT head_number, compartment_type, 
       CASE 
           WHEN compartment_type = 'SHALLOW' THEN '上层浅部'
           WHEN compartment_type = 'DEEP' THEN '下层深部'
           ELSE '未知'
       END as description
FROM treatment_heads 
ORDER BY head_number;
```

**预期结果**：
- 表中应该有`compartment_type`字段
- 治疗头1-10的`compartment_type`为`SHALLOW`
- 治疗头11-20的`compartment_type`为`DEEP`

## 🚀 修改完成后的测试

数据库修改完成后，执行以下步骤：

### **1. 重启应用**
```bash
# 停止当前应用（Ctrl+C）
# 重新启动
./gradlew bootRun
```

### **2. 测试API**
```bash
# 测试治疗头数据
curl "http://localhost:8080/api/hardware/heads?page=1&size=20"

# 测试推荐API
curl -X POST http://localhost:8080/api/treatment-parameters/generate-recommendations \
  -H "Content-Type: application/json" \
  -d '{"patientId":"P001001","treatmentMode":"local","bodyParts":[{"name":"肩颈部","parameters":{"depth":"深部","count":2}}]}'
```

### **3. 验证前端**
访问治疗头管理页面，确认：
- 治疗头显示正确的仓位类型
- 槽号显示为1-10范围
- 推荐功能正常工作

## 🚨 如果仍有问题

### **检查应用日志**
查找以下错误：
- `Column 'compartment_type' not found`
- `Unknown column 'compartment_type'`

### **回滚方案**
如果需要回滚：
```sql
-- 删除新增字段
ALTER TABLE treatment_heads DROP COLUMN compartment_type;

-- 删除索引
DROP INDEX idx_compartment_type ON treatment_heads;
DROP INDEX idx_compartment_slot ON treatment_heads;
```

## 📞 获取帮助

如果遇到问题：
1. 检查MySQL服务是否运行
2. 确认数据库连接权限
3. 查看应用启动日志
4. 验证表结构是否正确修改

---

## ✅ 成功标准

- [ ] `compartment_type`字段成功添加
- [ ] 治疗头1-10设置为SHALLOW
- [ ] 治疗头11-20设置为DEEP
- [ ] 应用重启无错误
- [ ] API返回正确的仓位类型
- [ ] 推荐功能正常工作

完成以上检查后，槽号逻辑修改就成功了！🎉
