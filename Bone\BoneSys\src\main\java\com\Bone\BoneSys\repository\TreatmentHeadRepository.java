package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 治疗头数据访问接口
 */
@Repository
public interface TreatmentHeadRepository extends JpaRepository<TreatmentHead, Long> {
    
    /**
     * 根据治疗头编号查找治疗头
     */
    Optional<TreatmentHead> findByHeadNumber(Integer headNumber);
    
    /**
     * 根据槽位号查找治疗头
     */
    Optional<TreatmentHead> findBySlotNumber(Integer slotNumber);
    
    /**
     * 检查治疗头编号是否存在
     */
    boolean existsByHeadNumber(Integer headNumber);
    
    /**
     * 检查槽位号是否存在
     */
    boolean existsBySlotNumber(Integer slotNumber);
    
    /**
     * 根据状态查找治疗头
     */
    List<TreatmentHead> findByRealtimeStatus(TreatmentHeadStatus status);
    
    /**
     * 查找可用的治疗头（充电完成状态且非异常状态）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGED' AND t.realtimeStatus != 'ABNORMAL' AND t.batteryLevel >= :minBattery")
    List<TreatmentHead> findAvailableHeads(@Param("minBattery") Integer minBattery);
    
    /**
     * 查找推荐的治疗头（按电量和使用次数排序，排除异常状态）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGED' AND t.realtimeStatus != 'ABNORMAL' AND t.batteryLevel >= :minBattery " +
           "ORDER BY t.batteryLevel DESC, t.totalUsageCount ASC")
    List<TreatmentHead> findRecommendedHeads(@Param("minBattery") Integer minBattery, Pageable pageable);
    
    /**
     * 查找正在充电的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGING'")
    List<TreatmentHead> findChargingHeads();
    
    /**
     * 查找充电完成的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGED'")
    List<TreatmentHead> findChargedHeads();
    
    /**
     * 查找正在治疗的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'TREATING'")
    List<TreatmentHead> findTreatingHeads();

    /**
     * 查找异常状态的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'ABNORMAL'")
    List<TreatmentHead> findAbnormalHeads();
    
    /**
     * 根据仓位类型查找治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.compartmentType = :compartmentType")
    List<TreatmentHead> findByCompartmentType(@Param("compartmentType") String compartmentType);

    /**
     * 查找浅部治疗头（上层）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.compartmentType = 'SHALLOW'")
    List<TreatmentHead> findShallowCompartmentHeads();

    /**
     * 查找深部治疗头（下层）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.compartmentType = 'DEEP'")
    List<TreatmentHead> findDeepCompartmentHeads();

    /**
     * 根据仓位类型和槽号查找治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.compartmentType = :compartmentType AND t.slotNumber = :slotNumber")
    Optional<TreatmentHead> findByCompartmentTypeAndSlotNumber(@Param("compartmentType") String compartmentType, @Param("slotNumber") Integer slotNumber);
    
    /**
     * 根据指示灯颜色查找治疗头
     */
    List<TreatmentHead> findByLightColor(Integer lightColor);
    
    /**
     * 查找指示灯开启的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.lightColor > 0")
    List<TreatmentHead> findHeadsWithLightOn();
    
    /**
     * 查找电量不足的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.batteryLevel < :lowBatteryThreshold")
    List<TreatmentHead> findLowBatteryHeads(@Param("lowBatteryThreshold") Integer lowBatteryThreshold);
    
    /**
     * 查找需要更换的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.totalUsageCount >= t.maxUsageCount * 0.9")
    List<TreatmentHead> findHeadsNeedingReplacement();
    
    /**
     * 统计各状态的治疗头数量
     */
    @Query("SELECT t.realtimeStatus, COUNT(t) FROM TreatmentHead t GROUP BY t.realtimeStatus")
    List<Object[]> countByStatus();
    
    /**
     * 获取治疗头使用统计
     */
    @Query("SELECT AVG(t.totalUsageCount), MAX(t.totalUsageCount), MIN(t.totalUsageCount) FROM TreatmentHead t")
    Object[] getUsageStatistics();
}