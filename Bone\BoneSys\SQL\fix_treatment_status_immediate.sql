-- ========================================
-- 立即修复治疗状态问题
-- 解决状态不更新和数据重复问题
-- ========================================

USE bonesys;

-- 1. 显示当前问题状态
SELECT '=== 修复前状态检查 ===' as message;

SELECT 
    td.id,
    td.process_id,
    td.body_part,
    td.status,
    td.duration,
    TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) as elapsed_minutes,
    p.treatment_mode,
    CASE 
        WHEN p.treatment_mode = 'ON_SITE' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration 
        THEN 'SHOULD_BE_COMPLETED'
        WHEN p.treatment_mode = 'TAKE_AWAY' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15)
        THEN 'SHOULD_BE_AWAITING_RETURN'
        ELSE 'OK'
    END as should_be
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE p.status = 'IN_PROGRESS'
ORDER BY td.process_id, td.id;

-- 2. 修复过期的本地治疗状态（TREATING -> COMPLETED）
SELECT '=== 修复本地治疗过期状态 ===' as message;

UPDATE treatment_details td
JOIN processes p ON td.process_id = p.id
SET td.status = 'COMPLETED'
WHERE td.status = 'TREATING'
  AND p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'ON_SITE'
  AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration;

SELECT ROW_COUNT() as '本地治疗更新数量';

-- 3. 修复过期的远端治疗状态（TREATING -> AWAITING_RETURN）
SELECT '=== 修复远端治疗过期状态 ===' as message;

UPDATE treatment_details td
JOIN processes p ON td.process_id = p.id
SET td.status = 'AWAITING_RETURN'
WHERE td.status = 'TREATING'
  AND p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'TAKE_AWAY'
  AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15);

SELECT ROW_COUNT() as '远端治疗更新数量';

-- 4. 检查并修复进程状态
SELECT '=== 检查进程状态更新 ===' as message;

-- 4.1 本地治疗进程：所有详情都完成或归还或终止时，进程变为完成
UPDATE processes p
SET p.status = 'COMPLETED', p.end_time = NOW()
WHERE p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'ON_SITE'
  AND NOT EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id 
    AND td.status NOT IN ('COMPLETED', 'RETURNED', 'TERMINATED')
  )
  AND EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id 
    AND td.status IN ('COMPLETED', 'RETURNED')
  );

SELECT ROW_COUNT() as '本地进程完成数量';

-- 4.2 远端治疗进程：所有详情都归还时，进程变为完成
UPDATE processes p
SET p.status = 'COMPLETED', p.end_time = NOW()
WHERE p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'TAKE_AWAY'
  AND NOT EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id 
    AND td.status != 'RETURNED'
  )
  AND EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id
  );

SELECT ROW_COUNT() as '远端进程完成数量';

-- 5. 删除重复的治疗详情（保留ID最小的）
SELECT '=== 删除重复的治疗详情 ===' as message;

DELETE td1 FROM treatment_details td1
INNER JOIN treatment_details td2 
WHERE td1.process_id = td2.process_id
  AND td1.body_part = td2.body_part
  AND td1.head_number_used = td2.head_number_used
  AND td1.id > td2.id;

SELECT ROW_COUNT() as '删除重复详情数量';

-- 6. 更新身体部位统计（对于新完成的治疗）
SELECT '=== 更新身体部位统计 ===' as message;

INSERT INTO body_part_stats (record_id, body_part, treatment_count, total_treatment_time)
SELECT 
    p.record_id,
    td.body_part,
    COUNT(*) as treatment_count,
    SUM(td.duration) as total_treatment_time
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE td.status = 'COMPLETED'
GROUP BY p.record_id, td.body_part
ON DUPLICATE KEY UPDATE
    treatment_count = treatment_count + VALUES(treatment_count),
    total_treatment_time = total_treatment_time + VALUES(total_treatment_time);

-- 7. 更新档案完成次数
SELECT '=== 更新档案完成次数 ===' as message;

UPDATE records r
SET sessions_completed_count = (
    SELECT COUNT(*)
    FROM processes p
    WHERE p.record_id = r.id
    AND p.status = 'COMPLETED'
)
WHERE EXISTS (
    SELECT 1 FROM processes p
    WHERE p.record_id = r.id
    AND p.status = 'COMPLETED'
);

-- 8. 显示修复后的状态
SELECT '=== 修复后状态检查 ===' as message;

SELECT 
    p.id as process_id,
    p.status as process_status,
    p.treatment_mode,
    COUNT(td.id) as total_details,
    SUM(CASE WHEN td.status = 'TREATING' THEN 1 ELSE 0 END) as treating,
    SUM(CASE WHEN td.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN td.status = 'AWAITING_RETURN' THEN 1 ELSE 0 END) as awaiting_return,
    SUM(CASE WHEN td.status = 'TERMINATED' THEN 1 ELSE 0 END) as terminated
FROM processes p
LEFT JOIN treatment_details td ON p.id = td.process_id
WHERE p.id >= 28  -- 查看最近的进程
GROUP BY p.id, p.status, p.treatment_mode
ORDER BY p.id DESC;

-- 9. 显示当前活跃的治疗详情
SELECT '=== 当前活跃治疗详情 ===' as message;

SELECT 
    td.id,
    td.process_id,
    td.body_part,
    td.status,
    td.duration,
    TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) as elapsed_minutes,
    p.treatment_mode
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE td.status IN ('TREATING', 'AWAITING_RETURN')
ORDER BY td.process_id, td.id;

SELECT '=== 修复完成 ===' as message;
