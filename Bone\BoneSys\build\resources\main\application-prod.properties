# 生产环境配置

# 数据库配置（生产环境）
spring.datasource.url=*******************************************************************************************************************
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:111111}

# JPA配置（生产环境）
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# 日志配置（生产环境）
logging.level.com.Bone.BoneSys=INFO
logging.level.org.springframework.security=WARN
logging.level.root=WARN

# 串口配置（生产环境）
serial.port.name=${SERIAL_PORT:}
serial.port.auto-detect=${SERIAL_AUTO_DETECT:true}

# JWT配置（生产环境）
jwt.secret=${JWT_SECRET:BoneSysSecretKeyForJWTTokenGeneration2025}
jwt.expiration=86400000

# 跨域配置（生产环境）
cors.allowed-origins=${CORS_ORIGINS:http://localhost:3000}