package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗头指示灯响应DTO
 * 用于封装硬件返回的指示灯设置结果
 */
public class TreatmentHeadLightResponse {
    
    private int headNumber;  // 治疗头编号
    private int colorCode;   // 颜色代码
    private int slotNumber;  // 槽编号
    
    public TreatmentHeadLightResponse() {}
    
    public TreatmentHeadLightResponse(int headNumber, int colorCode, int slotNumber) {
        this.headNumber = headNumber;
        this.colorCode = colorCode;
        this.slotNumber = slotNumber;
    }
    
    // Getters and Setters
    public int getHeadNumber() {
        return headNumber;
    }
    
    public void setHeadNumber(int headNumber) {
        this.headNumber = headNumber;
    }
    
    public int getColorCode() {
        return colorCode;
    }
    
    public void setColorCode(int colorCode) {
        this.colorCode = colorCode;
    }
    
    public int getSlotNumber() {
        return slotNumber;
    }
    
    public void setSlotNumber(int slotNumber) {
        this.slotNumber = slotNumber;
    }
    
    /**
     * 获取颜色描述
     */
    public String getColorDescription() {
        switch (colorCode) {
            case 0: return "关闭";
            case 1: return "橙色";
            case 2: return "蓝色";
            case 3: return "绿色";
            default: return "未知";
        }
    }
    
    @Override
    public String toString() {
        return String.format("TreatmentHeadLightResponse{headNumber=%d, colorCode=%d(%s), slotNumber=%d}", 
                           headNumber, colorCode, getColorDescription(), slotNumber);
    }
}