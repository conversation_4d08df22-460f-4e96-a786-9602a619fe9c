@echo off
chcp 65001 > nul
echo ========================================
echo FREEBONE医疗系统 - 快速重建数据库
echo ========================================
echo.

echo [1/4] 删除现有数据库...
mysql -u root -p -e "DROP DATABASE IF EXISTS bonesys;"
if %errorlevel% neq 0 (
    echo 错误：删除数据库失败
    pause
    exit /b 1
)

echo.
echo [2/4] 重建数据库结构...
mysql -u root -p < SQL\create_database_enhanced.sql
if %errorlevel% neq 0 (
    echo 错误：创建数据库失败
    pause
    exit /b 1
)

echo.
echo [3/4] 插入基础测试数据...
mysql -u root -p < SQL\clean_and_insert_test_data.sql
if %errorlevel% neq 0 (
    echo 警告：插入测试数据失败，但基础结构已创建
)

echo.
echo [4/4] 验证数据库状态...
mysql -u root -p -e "USE bonesys; SELECT COUNT(*) as treatment_heads_count FROM treatment_heads; SELECT COUNT(*) as patients_count FROM patients;"
if %errorlevel% neq 0 (
    echo 警告：验证数据库失败，但数据库可能已创建成功
)

echo.
echo ========================================
echo 数据库重建完成！
echo ========================================
echo.
echo 预期结果：
echo - 20个治疗头记录（状态：TREATING）
echo - 5个患者，11个档案，27个进程
echo.
echo 现在可以启动应用：
echo ./gradlew bootRun
echo.
pause
