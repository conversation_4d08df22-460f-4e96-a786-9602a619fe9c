package com.Bone.BoneSys.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 串口通信配置类
 * 配置上下层治疗仓的串口参数
 */
@Configuration
@ConfigurationProperties(prefix = "hardware.serial")
public class SerialPortConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(SerialPortConfiguration.class);
    
    // 上层治疗仓串口配置
    private String upperPort = "/dev/ttysWK2";
    private int upperBaudRate = 115200;
    private int upperDataBits = 8;
    private int upperStopBits = 1;
    private String upperParity = "NONE";
    
    // 下层治疗仓串口配置
    private String lowerPort = "/dev/ttysWK3";
    private int lowerBaudRate = 115200;
    private int lowerDataBits = 8;
    private int lowerStopBits = 1;
    private String lowerParity = "NONE";
    
    // 通用配置
    private int connectionTimeout = 5000; // 连接超时时间（毫秒）
    private int readTimeout = 3000;       // 读取超时时间（毫秒）
    private int writeTimeout = 3000;      // 写入超时时间（毫秒）
    private boolean autoReconnect = true; // 自动重连
    private int maxRetryAttempts = 3;     // 最大重试次数
    
    // getters and setters
    public String getUpperPort() {
        return upperPort;
    }
    
    public void setUpperPort(String upperPort) {
        this.upperPort = upperPort;
        logger.info("上层治疗仓串口设置为: {}", upperPort);
    }
    
    public int getUpperBaudRate() {
        return upperBaudRate;
    }
    
    public void setUpperBaudRate(int upperBaudRate) {
        this.upperBaudRate = upperBaudRate;
    }
    
    public int getUpperDataBits() {
        return upperDataBits;
    }
    
    public void setUpperDataBits(int upperDataBits) {
        this.upperDataBits = upperDataBits;
    }
    
    public int getUpperStopBits() {
        return upperStopBits;
    }
    
    public void setUpperStopBits(int upperStopBits) {
        this.upperStopBits = upperStopBits;
    }
    
    public String getUpperParity() {
        return upperParity;
    }
    
    public void setUpperParity(String upperParity) {
        this.upperParity = upperParity;
    }
    
    public String getLowerPort() {
        return lowerPort;
    }
    
    public void setLowerPort(String lowerPort) {
        this.lowerPort = lowerPort;
        logger.info("下层治疗仓串口设置为: {}", lowerPort);
    }
    
    public int getLowerBaudRate() {
        return lowerBaudRate;
    }
    
    public void setLowerBaudRate(int lowerBaudRate) {
        this.lowerBaudRate = lowerBaudRate;
    }
    
    public int getLowerDataBits() {
        return lowerDataBits;
    }
    
    public void setLowerDataBits(int lowerDataBits) {
        this.lowerDataBits = lowerDataBits;
    }
    
    public int getLowerStopBits() {
        return lowerStopBits;
    }
    
    public void setLowerStopBits(int lowerStopBits) {
        this.lowerStopBits = lowerStopBits;
    }
    
    public String getLowerParity() {
        return lowerParity;
    }
    
    public void setLowerParity(String lowerParity) {
        this.lowerParity = lowerParity;
    }
    
    public int getConnectionTimeout() {
        return connectionTimeout;
    }
    
    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public int getWriteTimeout() {
        return writeTimeout;
    }
    
    public void setWriteTimeout(int writeTimeout) {
        this.writeTimeout = writeTimeout;
    }
    
    public boolean isAutoReconnect() {
        return autoReconnect;
    }
    
    public void setAutoReconnect(boolean autoReconnect) {
        this.autoReconnect = autoReconnect;
    }
    
    public int getMaxRetryAttempts() {
        return maxRetryAttempts;
    }
    
    public void setMaxRetryAttempts(int maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }
    
    /**
     * 根据治疗头编号确定使用的串口
     */
    public String getPortForTreatmentHead(int headNumber) {
        if (headNumber >= 1 && headNumber <= 10) {
            return upperPort; // 上层治疗仓
        } else if (headNumber >= 11 && headNumber <= 20) {
            return lowerPort; // 下层治疗仓
        } else {
            logger.warn("无效的治疗头编号: {}, 使用默认上层串口", headNumber);
            return upperPort;
        }
    }
    
    /**
     * 根据仓位类型确定使用的串口
     */
    public String getPortForCompartmentType(String compartmentType) {
        if ("上仓(浅部)".equals(compartmentType)) {
            return upperPort;
        } else if ("下仓(深部)".equals(compartmentType)) {
            return lowerPort;
        } else {
            logger.warn("无效的仓位类型: {}, 使用默认上层串口", compartmentType);
            return upperPort;
        }
    }
    
    /**
     * 获取串口配置摘要信息
     */
    public String getConfigurationSummary() {
        return String.format("串口配置 - 上层: %s@%d, 下层: %s@%d, 超时: %dms", 
                           upperPort, upperBaudRate, lowerPort, lowerBaudRate, connectionTimeout);
    }
    
    /**
     * 验证串口配置是否有效
     */
    public boolean isConfigurationValid() {
        boolean valid = true;
        
        if (upperPort == null || upperPort.trim().isEmpty()) {
            logger.error("上层治疗仓串口配置无效");
            valid = false;
        }
        
        if (lowerPort == null || lowerPort.trim().isEmpty()) {
            logger.error("下层治疗仓串口配置无效");
            valid = false;
        }
        
        if (upperBaudRate <= 0 || lowerBaudRate <= 0) {
            logger.error("波特率配置无效");
            valid = false;
        }
        
        if (connectionTimeout <= 0 || readTimeout <= 0 || writeTimeout <= 0) {
            logger.error("超时配置无效");
            valid = false;
        }
        
        return valid;
    }
}
