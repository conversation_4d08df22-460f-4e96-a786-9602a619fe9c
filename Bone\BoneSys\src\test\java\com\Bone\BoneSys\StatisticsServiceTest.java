package com.Bone.BoneSys;

import com.Bone.BoneSys.service.StatisticsService;
import com.Bone.BoneSys.service.StatisticsService.*;
import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.repository.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * StatisticsService单元测试
 * 测试统计数据计算服务的功能完整性
 */
@SpringBootTest
@TestPropertySource(properties = {
    "hardware.simulator.enabled=true"
})
public class StatisticsServiceTest {

    @Autowired
    private StatisticsService statisticsService;

    @MockBean
    private PatientRepository patientRepository;
    
    @MockBean
    private RecordRepository recordRepository;
    
    @MockBean
    private ProcessRepository processRepository;
    
    @MockBean
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @MockBean
    private BodyPartStatRepository bodyPartStatRepository;
    
    @MockBean
    private TreatmentHeadRepository treatmentHeadRepository;

    private Patient mockPatient;
    private com.Bone.BoneSys.entity.Record mockRecord;
    private com.Bone.BoneSys.entity.Process mockProcess;
    private TreatmentDetail mockTreatmentDetail;
    private BodyPartStat mockBodyPartStat;

    @BeforeEach
    void setUp() {
        // 创建模拟患者
        mockPatient = new Patient();
        mockPatient.setId(1L);
        mockPatient.setName("张三");
        mockPatient.setPatientCardId("P001");
        
        // 创建模拟档案
        mockRecord = new com.Bone.BoneSys.entity.Record();
        mockRecord.setId(1L);
        mockRecord.setPatient(mockPatient);
        
        // 创建模拟进程
        mockProcess = new com.Bone.BoneSys.entity.Process();
        mockProcess.setId(1L);
        mockProcess.setRecord(mockRecord);
        mockProcess.setStartTime(LocalDateTime.now());
        
        // 创建模拟治疗详情
        mockTreatmentDetail = new TreatmentDetail();
        mockTreatmentDetail.setId(1L);
        mockTreatmentDetail.setProcess(mockProcess);
        mockTreatmentDetail.setBodyPart("胸部");
        mockTreatmentDetail.setHeadNumberUsed(1);
        mockTreatmentDetail.setDuration(20);
        mockTreatmentDetail.setFrequency(1000);
        
        // 创建模拟部位统计
        mockBodyPartStat = new BodyPartStat();
        mockBodyPartStat.setId(1L);
        mockBodyPartStat.setRecord(mockRecord);
        mockBodyPartStat.setBodyPart("胸部");
        mockBodyPartStat.setTotalUsageCount(5);
        mockBodyPartStat.setTotalDurationMinutes(100);
    }

    @Test
    void testGetPatientTreatmentStatistics_Success() {
        System.out.println("🧪 测试获取患者治疗统计 - 成功场景");
        
        // 设置模拟数据
        when(patientRepository.findById(1L)).thenReturn(Optional.of(mockPatient));
        when(recordRepository.findByPatientId(1L)).thenReturn(Arrays.asList(mockRecord));
        when(processRepository.findByRecordId(1L)).thenReturn(Arrays.asList(mockProcess));
        when(treatmentDetailRepository.findByPatientId(1L)).thenReturn(Arrays.asList(mockTreatmentDetail));
        when(bodyPartStatRepository.findByPatientId(1L)).thenReturn(Arrays.asList(mockBodyPartStat));
        
        // 执行统计计算
        PatientTreatmentStatistics stats = statisticsService.getPatientTreatmentStatistics(1L);
        
        // 验证基本信息
        assertNotNull(stats, "统计结果不应为空");
        assertEquals(1L, stats.getPatientId(), "患者ID应正确");
        assertEquals("张三", stats.getPatientName(), "患者姓名应正确");
        assertEquals("P001", stats.getPatientCardId(), "患者卡号应正确");
        
        // 验证统计数据
        assertEquals(1, stats.getTotalRecords(), "档案总数应为1");
        assertEquals(1, stats.getTotalProcesses(), "进程总数应为1");
        assertEquals(1, stats.getTotalTreatmentDetails(), "治疗详情总数应为1");
        assertEquals(20, stats.getTotalTreatmentDuration(), "总治疗时长应为20分钟");
        assertEquals("胸部", stats.getMostUsedBodyPart(), "最常用部位应为胸部");
        
        // 验证部位统计
        assertNotNull(stats.getBodyPartStatistics(), "部位统计不应为空");
        assertEquals(1, stats.getBodyPartStatistics().size(), "部位统计数量应为1");
        
        BodyPartStatistics bodyPartStat = stats.getBodyPartStatistics().get(0);
        assertEquals("胸部", bodyPartStat.getBodyPart(), "部位名称应正确");
        assertEquals(5L, bodyPartStat.getTotalUsageCount(), "使用次数应正确");
        assertEquals(100L, bodyPartStat.getTotalDurationMinutes(), "使用时长应正确");
        
        System.out.println("✅ 患者治疗统计验证通过");
        System.out.println("   患者: " + stats.getPatientName() + " (" + stats.getPatientCardId() + ")");
        System.out.println("   档案数: " + stats.getTotalRecords());
        System.out.println("   进程数: " + stats.getTotalProcesses());
        System.out.println("   最常用部位: " + stats.getMostUsedBodyPart());
    }

    @Test
    void testGetPatientTreatmentStatistics_PatientNotFound() {
        System.out.println("🧪 测试获取患者治疗统计 - 患者不存在");
        
        // 设置模拟数据
        when(patientRepository.findById(999L)).thenReturn(Optional.empty());
        
        // 执行统计计算
        PatientTreatmentStatistics stats = statisticsService.getPatientTreatmentStatistics(999L);
        
        // 验证结果
        assertNotNull(stats, "统计结果不应为空");
        assertEquals(999L, stats.getPatientId(), "患者ID应正确");
        assertNull(stats.getPatientName(), "患者姓名应为空");
        
        System.out.println("✅ 患者不存在验证通过");
    }

    @Test
    void testGetGlobalTreatmentStatistics() {
        System.out.println("🧪 测试获取全局治疗统计");
        
        // 设置模拟数据
        when(patientRepository.count()).thenReturn(150L);
        when(recordRepository.count()).thenReturn(320L);
        when(processRepository.count()).thenReturn(890L);
        when(treatmentDetailRepository.count()).thenReturn(1250L);
        
        // 模拟治疗头使用统计
        Object[] headUsageStat = {1, 45L, 900L}; // headNumber, usageCount, totalDuration
        when(treatmentDetailRepository.getTreatmentHeadUsageStats())
            .thenReturn(java.util.Collections.singletonList(headUsageStat));
        
        // 模拟部位使用统计
        Object[] bodyPartUsageStat = {"胸部", 120L, 2400L}; // bodyPart, usageCount, totalDuration
        when(treatmentDetailRepository.getBodyPartUsageStats())
            .thenReturn(java.util.Collections.singletonList(bodyPartUsageStat));
        
        // 模拟进程状态统计
        Object[] processStatusStat1 = {"COMPLETED", 750L};
        Object[] processStatusStat2 = {"IN_PROGRESS", 20L};
        when(processRepository.countByStatus())
            .thenReturn(Arrays.asList(processStatusStat1, processStatusStat2));
        
        // 模拟治疗模式统计
        Object[] treatmentModeStat1 = {"ON_SITE", 600L};
        Object[] treatmentModeStat2 = {"TAKE_AWAY", 290L};
        when(processRepository.countByTreatmentMode())
            .thenReturn(Arrays.asList(treatmentModeStat1, treatmentModeStat2));
        
        // 执行统计计算
        GlobalTreatmentStatistics stats = statisticsService.getGlobalTreatmentStatistics();
        
        // 验证基础统计
        assertNotNull(stats, "统计结果不应为空");
        assertEquals(150L, stats.getTotalPatients(), "患者总数应正确");
        assertEquals(320L, stats.getTotalRecords(), "档案总数应正确");
        assertEquals(890L, stats.getTotalProcesses(), "进程总数应正确");
        assertEquals(1250L, stats.getTotalTreatmentDetails(), "治疗详情总数应正确");
        
        // 验证治疗头使用统计
        assertNotNull(stats.getTreatmentHeadUsageStatistics(), "治疗头使用统计不应为空");
        assertEquals(1, stats.getTreatmentHeadUsageStatistics().size(), "治疗头使用统计数量应为1");
        
        TreatmentHeadUsageStatistics headStat = stats.getTreatmentHeadUsageStatistics().get(0);
        assertEquals(1, headStat.getHeadNumber(), "治疗头编号应正确");
        assertEquals(45L, headStat.getUsageCount(), "使用次数应正确");
        assertEquals(900L, headStat.getTotalDuration(), "总时长应正确");
        
        // 验证部位使用统计
        assertNotNull(stats.getBodyPartUsageStatistics(), "部位使用统计不应为空");
        assertEquals(1, stats.getBodyPartUsageStatistics().size(), "部位使用统计数量应为1");
        
        BodyPartUsageStatistics bodyPartStat = stats.getBodyPartUsageStatistics().get(0);
        assertEquals("胸部", bodyPartStat.getBodyPart(), "部位名称应正确");
        assertEquals(120L, bodyPartStat.getUsageCount(), "使用次数应正确");
        assertEquals(2400L, bodyPartStat.getTotalDuration(), "总时长应正确");
        
        // 验证进程状态统计
        assertNotNull(stats.getProcessStatusStatistics(), "进程状态统计不应为空");
        assertEquals(750L, stats.getProcessStatusStatistics().get("COMPLETED"), "已完成进程数应正确");
        assertEquals(20L, stats.getProcessStatusStatistics().get("IN_PROGRESS"), "进行中进程数应正确");
        
        // 验证治疗模式统计
        assertNotNull(stats.getTreatmentModeStatistics(), "治疗模式统计不应为空");
        assertEquals(600L, stats.getTreatmentModeStatistics().get("ON_SITE"), "现场治疗数应正确");
        assertEquals(290L, stats.getTreatmentModeStatistics().get("TAKE_AWAY"), "取走治疗数应正确");
        
        System.out.println("✅ 全局治疗统计验证通过");
        System.out.println("   患者总数: " + stats.getTotalPatients());
        System.out.println("   档案总数: " + stats.getTotalRecords());
        System.out.println("   进程总数: " + stats.getTotalProcesses());
        System.out.println("   治疗详情总数: " + stats.getTotalTreatmentDetails());
    }

    @Test
    void testGetBodyPartTreatmentStatistics() {
        System.out.println("🧪 测试获取部位治疗统计");
        
        // 设置模拟数据
        when(bodyPartStatRepository.findByBodyPart("胸部")).thenReturn(Arrays.asList(mockBodyPartStat));
        when(treatmentDetailRepository.findByBodyPart("胸部")).thenReturn(Arrays.asList(mockTreatmentDetail));
        
        // 执行统计计算
        BodyPartTreatmentStatistics stats = statisticsService.getBodyPartTreatmentStatistics("胸部");
        
        // 验证统计结果
        assertNotNull(stats, "统计结果不应为空");
        assertEquals("胸部", stats.getBodyPart(), "部位名称应正确");
        assertEquals(1, stats.getTotalPatients(), "患者总数应为1");
        assertEquals(5L, stats.getTotalUsageCount(), "总使用次数应为5");
        assertEquals(100L, stats.getTotalDurationMinutes(), "总时长应为100分钟");
        assertEquals(5.0, stats.getAverageUsagePerPatient(), "人均使用次数应为5.0");
        assertEquals(100.0, stats.getAverageDurationPerPatient(), "人均时长应为100.0分钟");
        
        // 验证分布统计
        assertNotNull(stats.getFrequencyDistribution(), "频率分布不应为空");
        assertNotNull(stats.getHeadUsageDistribution(), "治疗头使用分布不应为空");
        
        System.out.println("✅ 部位治疗统计验证通过");
        System.out.println("   部位: " + stats.getBodyPart());
        System.out.println("   患者数: " + stats.getTotalPatients());
        System.out.println("   总使用次数: " + stats.getTotalUsageCount());
        System.out.println("   总时长: " + stats.getTotalDurationMinutes() + "分钟");
    }

    @Test
    void testGetTimePeriodStatistics() {
        System.out.println("🧪 测试获取时间段治疗统计");
        
        LocalDate startDate = LocalDate.of(2025, 7, 1);
        LocalDate endDate = LocalDate.of(2025, 7, 27);
        
        // 设置模拟数据
        Page<com.Bone.BoneSys.entity.Process> processPage = new PageImpl<>(Arrays.asList(mockProcess));
        when(processRepository.findByStartTimeBetween(any(LocalDateTime.class), any(LocalDateTime.class), any(Pageable.class)))
            .thenReturn(processPage);
        when(treatmentDetailRepository.findByProcessId(1L)).thenReturn(Arrays.asList(mockTreatmentDetail));
        
        // 执行统计计算
        TimePeriodStatistics stats = statisticsService.getTimePeriodStatistics(startDate, endDate);
        
        // 验证统计结果
        assertNotNull(stats, "统计结果不应为空");
        assertEquals(startDate, stats.getStartDate(), "开始日期应正确");
        assertEquals(endDate, stats.getEndDate(), "结束日期应正确");
        assertEquals(1, stats.getTotalProcesses(), "进程总数应为1");
        assertEquals(1, stats.getTotalTreatmentDetails(), "治疗详情总数应为1");
        assertEquals(20, stats.getTotalTreatmentDuration(), "总治疗时长应为20分钟");
        
        // 验证日期分布
        assertNotNull(stats.getDailyProcessCount(), "日期分布不应为空");
        
        // 验证部位使用
        assertNotNull(stats.getBodyPartUsage(), "部位使用不应为空");
        assertEquals(1L, stats.getBodyPartUsage().get("胸部"), "胸部使用次数应为1");
        
        System.out.println("✅ 时间段治疗统计验证通过");
        System.out.println("   时间段: " + startDate + " 至 " + endDate);
        System.out.println("   进程数: " + stats.getTotalProcesses());
        System.out.println("   治疗详情数: " + stats.getTotalTreatmentDetails());
        System.out.println("   总时长: " + stats.getTotalTreatmentDuration() + "分钟");
    }

    @Test
    void testStatisticsPerformance() {
        System.out.println("🧪 测试统计计算性能");
        
        // 设置模拟数据
        when(patientRepository.count()).thenReturn(1000L);
        when(recordRepository.count()).thenReturn(5000L);
        when(processRepository.count()).thenReturn(10000L);
        when(treatmentDetailRepository.count()).thenReturn(25000L);
        when(treatmentDetailRepository.getTreatmentHeadUsageStats()).thenReturn(Arrays.asList());
        when(treatmentDetailRepository.getBodyPartUsageStats()).thenReturn(Arrays.asList());
        when(processRepository.countByStatus()).thenReturn(Arrays.asList());
        when(processRepository.countByTreatmentMode()).thenReturn(Arrays.asList());
        
        // 测试性能
        long startTime = System.currentTimeMillis();
        GlobalTreatmentStatistics stats = statisticsService.getGlobalTreatmentStatistics();
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        // 验证性能
        assertNotNull(stats, "统计结果不应为空");
        assertTrue(responseTime < 1000, "统计计算时间应小于1秒，实际: " + responseTime + "ms");
        
        System.out.println("✅ 统计计算性能验证通过");
        System.out.println("   计算时间: " + responseTime + "ms");
        System.out.println("   数据规模: 1000患者, 5000档案, 10000进程, 25000治疗详情");
    }

    @Test
    void testStatisticsDataConsistency() {
        System.out.println("🧪 测试统计数据一致性");
        
        // 设置模拟数据
        when(patientRepository.findById(1L)).thenReturn(Optional.of(mockPatient));
        when(recordRepository.findByPatientId(1L)).thenReturn(Arrays.asList(mockRecord));
        when(processRepository.findByRecordId(1L)).thenReturn(Arrays.asList(mockProcess));
        when(treatmentDetailRepository.findByPatientId(1L)).thenReturn(Arrays.asList(mockTreatmentDetail));
        when(bodyPartStatRepository.findByPatientId(1L)).thenReturn(Arrays.asList(mockBodyPartStat));
        
        // 执行统计计算
        PatientTreatmentStatistics stats = statisticsService.getPatientTreatmentStatistics(1L);
        
        // 验证数据一致性
        assertEquals(stats.getTotalRecords().intValue(), 1, "档案数应一致");
        assertEquals(stats.getTotalProcesses().intValue(), 1, "进程数应一致");
        assertEquals(stats.getTotalTreatmentDetails().intValue(), 1, "治疗详情数应一致");
        
        // 验证部位统计一致性
        assertNotNull(stats.getBodyPartStatistics(), "部位统计不应为空");
        if (!stats.getBodyPartStatistics().isEmpty()) {
            BodyPartStatistics bodyPartStat = stats.getBodyPartStatistics().get(0);
            assertEquals(mockBodyPartStat.getBodyPart(), bodyPartStat.getBodyPart(), "部位名称应一致");
        }
        
        System.out.println("✅ 统计数据一致性验证通过");
    }
}
