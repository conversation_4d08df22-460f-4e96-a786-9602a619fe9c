package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.fazecast.jSerialComm.SerialPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 双层串口通信服务
 * 支持上层(UPPER)和下层(LOWER)两个独立的串口连接
 */
@Service
public class DualLayerSerialCommunicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(DualLayerSerialCommunicationService.class);
    
    // 串口通信参数常量
    private static final int BAUD_RATE = 115200;
    private static final int DATA_BITS = 8;
    private static final int STOP_BITS = SerialPort.ONE_STOP_BIT;
    private static final int PARITY = SerialPort.NO_PARITY;
    private static final int TIMEOUT_READ = 1000;
    private static final int TIMEOUT_WRITE = 1000;
    private static final int RESPONSE_WAIT_TIME = 100;
    
    @Value("${serial.port.upper:/dev/ttysWK2}")
    private String upperPortName;
    
    @Value("${serial.port.lower:/dev/ttysWK3}")
    private String lowerPortName;
    
    @Value("${serial.port.auto-detect:true}")
    private boolean autoDetectPort;
    
    @Value("${hardware.serial.enabled:true}")
    private boolean serialEnabled;
    
    @Value("${hardware.serial.dev-mode:false}")
    private boolean devMode;
    
    private final Map<String, SerialPort> serialPorts = new ConcurrentHashMap<>();
    private final Map<String, Boolean> connectionStatus = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        if (!serialEnabled) {
            logger.info("串口通信已禁用，跳过串口初始化");
            connectionStatus.put("upper", false);
            connectionStatus.put("lower", false);
            return;
        }
        
        if (devMode) {
            logger.info("开发模式启用，模拟串口连接");
            connectionStatus.put("upper", true);
            connectionStatus.put("lower", true);
            logger.info("Dual layer serial communication service initialized in dev mode");
            return;
        }
        
        try {
            connectToSerialPorts();
            logger.info("Dual layer serial communication service initialized successfully");
        } catch (Exception e) {
            logger.warn("Failed to initialize dual layer serial communication service: {}", e.getMessage());
            // 在生产环境中串口初始化失败时，设置连接状态为false，但不阻止应用启动
            connectionStatus.put("upper", false);
            connectionStatus.put("lower", false);
        }
    }
    
    /**
     * 连接到双层串口
     */
    private void connectToSerialPorts() throws SerialCommunicationException {
        try {
            // 连接上层串口
            connectToPort("upper", upperPortName);
            
            // 连接下层串口  
            connectToPort("lower", lowerPortName);
            
        } catch (Exception e) {
            logger.error("Failed to connect to serial ports", e);
            disconnect();
            throw new SerialCommunicationException("Failed to initialize dual layer serial communication", e);
        }
    }
    
    /**
     * 连接到指定层级的串口
     */
    private void connectToPort(String layer, String portName) throws SerialCommunicationException {
        try {
            logger.info("Connecting to {} layer serial port: {}", layer, portName);
            
            SerialPort port = findSerialPort(portName);
            if (port == null) {
                throw new SerialCommunicationException("Serial port not found: " + portName);
            }
            
            // 配置串口参数
            port.setBaudRate(BAUD_RATE);
            port.setNumDataBits(DATA_BITS);
            port.setNumStopBits(STOP_BITS);
            port.setParity(PARITY);
            port.setComPortTimeouts(
                SerialPort.TIMEOUT_READ_BLOCKING | SerialPort.TIMEOUT_WRITE_BLOCKING,
                TIMEOUT_READ,
                TIMEOUT_WRITE
            );
            
            if (port.openPort()) {
                serialPorts.put(layer, port);
                connectionStatus.put(layer, true);
                logger.info("Successfully connected to {} layer port: {}", layer, port.getSystemPortName());
            } else {
                throw new SerialCommunicationException("Failed to open " + layer + " layer port: " + portName);
            }
            
        } catch (Exception e) {
            connectionStatus.put(layer, false);
            throw new SerialCommunicationException("Failed to connect to " + layer + " layer port", e);
        }
    }
    
    /**
     * 查找串口设备
     */
    private SerialPort findSerialPort(String portName) {
        SerialPort[] availablePorts = SerialPort.getCommPorts();
        
        // 首先尝试精确匹配
        for (SerialPort port : availablePorts) {
            if (port.getSystemPortName().equals(portName)) {
                return port;
            }
        }
        
        // 如果启用自动检测且找不到指定端口
        if (autoDetectPort) {
            logger.warn("Port {} not found, available ports:", portName);
            for (SerialPort port : availablePorts) {
                logger.warn("  - {} ({})", port.getSystemPortName(), port.getPortDescription());
            }
            
            // 尝试找到相似的端口
            String baseName = portName.replaceAll("\\d+$", ""); // 移除数字后缀
            for (SerialPort port : availablePorts) {
                if (port.getSystemPortName().startsWith(baseName)) {
                    logger.warn("Using similar port {} instead of {}", port.getSystemPortName(), portName);
                    return port;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 发送命令到指定层级
     */
    public synchronized String sendCommand(String command, String layer) throws SerialCommunicationException {
        // 开发模式下返回模拟响应
        if (devMode || !serialEnabled) {
            logger.debug("Dev mode: Simulating command to {} layer: {}", layer, command.trim());
            String mockResponse = generateMockResponse(command);
            logger.debug("Dev mode: Mock response from {} layer: {}", layer, mockResponse);
            return mockResponse;
        }
        
        if (!isConnected(layer)) {
            throw new SerialCommunicationException(layer + " layer serial port is not connected");
        }
        
        SerialPort port = serialPorts.get(layer);
        if (port == null) {
            throw new SerialCommunicationException(layer + " layer serial port not found");
        }
        
        try {
            logger.debug("Sending command to {} layer: {}", layer, command.trim());
            
            String response = sendCommandInternal(port, command);
            
            logger.debug("Received response from {} layer: {}", layer, response.trim());
            return response;
            
        } catch (Exception e) {
            logger.error("Failed to send command to {} layer: {}", layer, command.trim(), e);
            
            // 尝试重连该层级
            try {
                String portName = layer.equals("upper") ? upperPortName : lowerPortName;
                reconnectLayer(layer, portName);
                // 重连成功后重试一次
                return sendCommandInternal(serialPorts.get(layer), command);
            } catch (Exception reconnectEx) {
                logger.error("Failed to reconnect {} layer", layer, reconnectEx);
            }
            
            throw new SerialCommunicationException("Failed to send command to " + layer + " layer: " + command.trim(), e);
        }
    }
    
    /**
     * 内部命令发送实现
     */
    private String sendCommandInternal(SerialPort port, String command) throws Exception {
        // 清空输入缓冲区
        if (port.bytesAvailable() > 0) {
            byte[] buffer = new byte[port.bytesAvailable()];
            port.readBytes(buffer, buffer.length);
            logger.debug("Cleared {} bytes from input buffer", buffer.length);
        }
        
        // 发送命令
        byte[] commandBytes = command.getBytes(StandardCharsets.UTF_8);
        int bytesWritten = port.writeBytes(commandBytes, commandBytes.length);
        
        if (bytesWritten != commandBytes.length) {
            throw new SerialCommunicationException(
                String.format("Failed to write complete command. Expected: %d, Written: %d", 
                    commandBytes.length, bytesWritten));
        }
        
        // 等待响应
        Thread.sleep(RESPONSE_WAIT_TIME);
        
        // 读取响应
        StringBuilder responseBuilder = new StringBuilder();
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < TIMEOUT_READ) {
            if (port.bytesAvailable() > 0) {
                byte[] buffer = new byte[port.bytesAvailable()];
                int bytesRead = port.readBytes(buffer, buffer.length);
                
                if (bytesRead > 0) {
                    String chunk = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                    responseBuilder.append(chunk);
                    
                    // 检查是否收到完整响应（以\r\n结尾）
                    if (responseBuilder.toString().contains("\r\n")) {
                        break;
                    }
                }
            } else {
                Thread.sleep(10);
            }
        }
        
        String response = responseBuilder.toString();
        if (response.isEmpty()) {
            throw new SerialCommunicationException("No response received from hardware");
        }
        
        return response;
    }
    
    /**
     * 检查指定层级连接状态
     */
    public boolean isConnected(String layer) {
        SerialPort port = serialPorts.get(layer);
        Boolean status = connectionStatus.get(layer);
        return status != null && status && port != null && port.isOpen();
    }
    
    /**
     * 检查所有层级连接状态
     */
    public boolean isAllConnected() {
        return isConnected("upper") && isConnected("lower");
    }
    
    /**
     * 重连指定层级
     */
    public synchronized void reconnectLayer(String layer, String portName) throws SerialCommunicationException {
        logger.info("Reconnecting {} layer...", layer);
        disconnectLayer(layer);
        connectToPort(layer, portName);
    }
    
    /**
     * 断开指定层级连接
     */
    public synchronized void disconnectLayer(String layer) {
        try {
            SerialPort port = serialPorts.get(layer);
            if (port != null && port.isOpen()) {
                port.closePort();
                logger.info("Disconnected from {} layer port: {}", layer, port.getSystemPortName());
            }
            serialPorts.remove(layer);
            connectionStatus.put(layer, false);
        } catch (Exception e) {
            logger.error("Error disconnecting from {} layer", layer, e);
        }
    }
    
    /**
     * 生成模拟响应（开发模式）
     */
    private String generateMockResponse(String command) {
        String trimmedCommand = command.trim();
        
        if (trimmedCommand.startsWith("TRZI")) {
            // 模拟查询治疗头响应：TRZI + 治疗头数量 + 治疗头数据
            return "TRZI02010190001010280001\r\n"; // 2个治疗头的模拟数据
        } else if (trimmedCommand.startsWith("TWSC")) {
            // 模拟点亮指示灯响应
            return trimmedCommand + "\r\n"; // 原样返回
        } else if (trimmedCommand.startsWith("TWSN")) {
            // 模拟关闭指示灯响应
            return trimmedCommand + "\r\n"; // 原样返回
        } else if (trimmedCommand.startsWith("TWSDT")) {
            // 模拟下载参数响应
            return trimmedCommand + "\r\n"; // 原样返回
        } else if (trimmedCommand.startsWith("TWZS")) {
            // 模拟本地治疗响应
            return trimmedCommand + "\r\n"; // 原样返回
        } else if (trimmedCommand.startsWith("TWZO")) {
            // 模拟关闭治疗头响应
            return trimmedCommand + "\r\n"; // 原样返回
        } else {
            // 默认响应
            return "OK\r\n";
        }
    }
    
    /**
     * 断开所有连接
     */
    public synchronized void disconnect() {
        disconnectLayer("upper");
        disconnectLayer("lower");
        logger.info("All serial connections disconnected");
    }
    
    /**
     * 获取连接信息
     */
    public String getConnectionInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Dual Layer Serial Communication:\n");
        
        for (String layer : new String[]{"upper", "lower"}) {
            SerialPort port = serialPorts.get(layer);
            boolean connected = isConnected(layer);
            if (port != null) {
                info.append(String.format("  %s: %s (%s) - %s\n", 
                    layer.toUpperCase(), 
                    port.getSystemPortName(), 
                    port.getPortDescription(),
                    connected ? "Connected" : "Disconnected"));
            } else {
                info.append(String.format("  %s: No port assigned - Disconnected\n", layer.toUpperCase()));
            }
        }
        
        return info.toString();
    }
    
    @PreDestroy
    public void cleanup() {
        disconnect();
        logger.info("Dual layer serial communication service cleaned up");
    }
} 