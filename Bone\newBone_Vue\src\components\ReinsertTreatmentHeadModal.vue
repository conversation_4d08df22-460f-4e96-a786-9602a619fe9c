<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="reinsert-modal" :style="{ backgroundImage: `url(${backgroundImage})` }">
        <!-- 关闭按钮 -->
        <img
          class="close-btn"
          :src="closeIcon"
          alt="关闭"
          @click="closeModal"
        />
        
        <!-- 槽位信息显示区域 -->
        <div class="slot-info-container">
          <div class="slot-info-text">
            {{ slotNumber }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue'

// 导入图片
import upperCompartmentImage from '@/assets/images/重新放置上仓治疗头.png'
import lowerCompartmentImage from '@/assets/images/重新放置下仓治疗头.png'
import closeIcon from '@/assets/images/交互界面/参数设置/×.png'

// Props
const props = defineProps<{
  visible: boolean
  compartmentType?: string // 仓位类型：'上仓(浅部)' 或 '下仓(深部)'
  slotNumber?: number // 槽位号
  headNumber?: number // 治疗头编号（可能是99）
}>()

// Emits
const emit = defineEmits<{
  close: []
  confirm: [] // 用户确认已重新插入
}>()

// 计算背景图片
const backgroundImage = computed(() => {
  if (!props.compartmentType) return upperCompartmentImage
  return props.compartmentType === '上仓(浅部)' ? upperCompartmentImage : lowerCompartmentImage
})

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const closeModal = () => {
  emit('close')
}

const confirmReinsert = () => {
  emit('confirm')
  emit('close')
}
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background: transparent;
  overflow: hidden;
  position: absolute;
  top: 147px;
  left: 658px;
}

/* 重新插入治疗头弹窗样式 */
.reinsert-modal {
  height: 752px;
  background-repeat: no-repeat;
  background-position: 0px 0px;
  background-size: 626px 769px;
  width: 626px;
  position: relative;
}

/* 关闭按钮样式 */
.close-btn {
  width: 60px;
  height: 60px;
  margin: 46px 0 10px 510px;
  cursor: pointer;
  position: absolute;
  transition: transform 0.2s ease;
}

.close-btn:hover {
  transform: scale(1.1);
}

/* 槽位信息定位容器（铺满以便内部绝对定位） */
.slot-info-container {
  position: absolute;
  inset: 0;
}

/* 将数字定位到背景图圆心并居中：
   说明：原实现通过 50% + margin 偏移导致两位数不居中。
   这里直接把文本的锚点放到圆心像素坐标，并用 translate(-50%, -50%) 居中。
   463px, 516px 来自现有布局(626x769背景下单数对齐的位置)
*/
.slot-info-text {
  position: absolute;
  left: 328px; /* 圆心X */
  top: 446px;  /* 圆心Y */
  transform: translate(-50%, -50%);
  color: #7E7B7B;
  font-size: 55px;
  font-family: 'Microsoft YaHei', sans-serif;
  font-weight: bold;
  line-height: 1;
  text-align: center;
  min-width: 0;
}
</style> 