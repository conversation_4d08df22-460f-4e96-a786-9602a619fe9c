<template>
  <div class="page flex-col">
    <div class="block_1 flex-col">
      <div class="box_1 flex-col">
        <div class="group_9 flex-row justify-between">
          <img
            class="image_1 button"
            referrerpolicy="no-referrer"
            src="../assets/images/treatmentsettingsview/img/ps0rbopt6gpo79v6n1nus8nyhtdkihjzd4vl8a5a6d1c-9d09-4ddd-a32d-e212ee18a676.png"
            @click="goBack"
          />
          <span class="text_1">档案管理</span>
        </div>
        <div class="group_10 flex-row">
          <div class="group_1 flex-row">
            <span class="text_2">就诊卡号</span>
            <img
              class="image_2"
              referrerpolicy="no-referrer"
              src="../assets/images/patientmangement/81cf662124a1846f6d5518140c3d6cf1.png"
            />
            <input v-model="searchMedicalId" class="text_3" type="text" :placeholder="inputFocusStates.medicalId ? '' : '请输入'" @focus="handleInputFocus('medicalId')" @blur="handleInputBlur('medicalId')" />
          </div>
          <div class="group_2 flex-row">
            <span class="text_4">姓名</span>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="../assets/images/patientmangement/a38792c610acbef960fb702285837259.png"
            />
            <input v-model="searchName" class="text_5" type="text" :placeholder="inputFocusStates.name ? '' : '请输入'" @focus="handleInputFocus('name')" @blur="handleInputBlur('name')" />
          </div>
          <div class="text-wrapper_1 flex-col button" @click="searchPatients">
            <span class="text_6">搜&nbsp;&nbsp;&nbsp;索</span>
          </div>
        </div>
        <div class="section_3 flex-col">
          <div class="table-header flex-row">
            <span class="header-item">就诊卡号</span>
            <span class="header-item">姓名</span>
            <span class="header-item">年龄</span>
            <span class="header-item">性别</span>
            <span class="header-item">治疗次数</span>
            <span class="header-item">建档日期</span>
            <span class="header-item">操作</span>
          </div>
          <img
            class="header-divider"
            referrerpolicy="no-referrer"
            src="../assets/images/patientmangement/3b4b13ceffce2e87fc3c501c067c49d4.png"
          />
          
          <template v-if="paginatedPatients.length > 0">
            <div v-for="(item, index) in paginatedPatients" :key="`${item.id}-${forceUpdateKey}`" class="patient-row-container">
              <div class="patient-row flex-row">
                <span class="row-item">{{ item.medicalRecordId }}</span>
                <span class="row-item">{{ item.name }}</span>
                <span class="row-item">{{ item.age }}</span>
                <span class="row-item">{{ item.sex === 1 ? '男' : item.sex === 0 ? '女' : '未知' }}</span>
                <span class="row-item">{{ item.treatmentCount }}</span>
                <span class="row-item">{{ formatDate(item.creationTimestamp) }}</span>
                <span class="row-item actions">
                  <button class="view-btn button" @click="viewPatient(item.medicalRecordId)">查看</button>
                  <button class="delete-btn button" @click="showDeleteDialog(item.medicalRecordId)">删除</button>
                </span>
              </div>
              <img
                class="row-divider"
                referrerpolicy="no-referrer"
                src="../assets/images/patientmangement/e86e74e7ccb59f2bf5a8099acc0b3e26.png"
              />
            </div>
          </template>
          <div v-else class="no-data">
            暂无患者数据
          </div>

          <div class="pagination-container">
            <div class="pagination-controls">
              <div class="pagination-btn prev-btn button"
                  @click="prevPage"
                  :class="{ disabled: currentPage === 1 }">
                <img src="../assets/images/patientmangement/pscfuqtlgklge35gqvwmjemf1ftzt5cw0k164a5578-5e3c-4723-bc66-875bb1108d45.png" />
              </div>
              <div class="page-info">
                第 {{ currentPage }} 页/共 {{ totalPages }} 页
              </div>
              <div class="pagination-btn next-btn button"
                  @click="nextPage"
                  :class="{ disabled: currentPage === totalPages }">
                <img src="../assets/images/patientmangement/psmqhsvt3d8c3z2wc722ghc6ga6sph03fs58fbf288-2ea0-4006-995a-1fb501817a56.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
     <div v-if="showDeleteConfirm" class="delete-dialog-overlay" @click.self="closeDeleteDialog">
      
        <div class="group_5 flex-col">
          <img
            class="image_10"
            referrerpolicy="no-referrer"
            src="../assets/images/patientmangement/3bda02c78dcc0910590c634f85617533.png"
            @click="closeDeleteDialog"
            style="cursor: pointer;"
          />
          <div class="group_3 flex-row">
            <img
              class="label_1"
              referrerpolicy="no-referrer"
              src="../assets/images/patientmangement/3dab4c07662a71f2bc4fc5a3dd82ef6e.png"
            />
            <div class="box_2 flex-col justify-between">
              <input 
                type="password" 
                v-model="deletePassword"
                class="password-input"
                @keyup.enter="confirmDelete"
                :placeholder="inputFocusStates.password ? '' : '请输入密码'"
                @focus="handleInputFocus('password')"
                @blur="handleInputBlur('password')"
              />
              <img
                class="image_12"
                referrerpolicy="no-referrer"
                src="../assets/images/patientmangement/ce6202914678efba2992f67c51ce89af.png"
              />
            </div>

          </div>
          <div class="group_4 flex-col">
            <div class="text-wrapper_5 flex-col confirm-btn button" @click="confirmDelete">
              <span class="text_11">确认</span>
            </div>
          </div>
        </div>
      </div>
    </div>

</template>

<script>
export default {
  data() {
    return {
      constants: {}
    };
  },
  methods: {}
};
</script>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { getRecords, deleteRecord } from '@/api';

const router = useRouter();

// 搜索条件
const searchName = ref('');
const searchMedicalId = ref('');

// 分页参数
const currentPage = ref(1);
const pageSize = ref(7);
const totalPatients = ref(0);
const forceUpdateKey = ref(0); // 添加强制更新key

// 患者数据列表
const patients = ref([]);

// 删除对话框相关
const showDeleteConfirm = ref(false);
const deletePassword = ref('');
const patientToDelete = ref(null);
const initialPassword = "123456"; // 初始密码设为123456

// 输入框焦点状态
const inputFocusStates = ref({
  medicalId: false,
  name: false,
  password: false
});

// 过滤后的患者数据
const filteredPatients = computed(() => {
  let result = [...patients.value];
  if (searchName.value && searchName.value.trim()) {
    result = result.filter(patient =>
      patient.name && patient.name.toLowerCase().includes(searchName.value.toLowerCase().trim())
    );
  }
  if (searchMedicalId.value && searchMedicalId.value.trim()) {
    result = result.filter(patient =>
      patient.medicalRecordId && patient.medicalRecordId.toString().includes(searchMedicalId.value.trim())
    );
  }
  return result;
});

// 当前页显示的患者数据
const paginatedPatients = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  return filteredPatients.value.slice(startIndex, startIndex + pageSize.value);
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredPatients.value.length / pageSize.value);
});

// 获取档案列表
const fetchPatients = async () => {
  try {
    // 获取所有档案数据，前端进行分页和搜索
    const response = await getRecords({
      page: 1,
      size: 1000, // 获取足够大的数据量
      search: undefined, // 前端自己处理搜索
      cardId: undefined
    });
    
    // 适配API文档中的records数据格式
    if (response.data && response.data.records && Array.isArray(response.data.records)) {
      let mappedRecords = response.data.records.map((record) => ({
        id: record.id, // 使用档案ID，确保唯一性
        patientId: record.patientId, // 保留患者ID用于跳转
        name: record.name,
        medicalCardId: record.cardId,
        medicalRecordId: record.id, // 使用真实的档案ID用于删除操作
        sex: record.gender === '男' ? 1 : record.gender === '女' ? 0 : 2, // 修复性别映射：男=1, 女=0, 其他=2
        age: record.age?.replace('岁', '') || '',
        contactInfo: record.phone || '',
        creationTimestamp: record.createdDate,
        treatmentSite: '其他部位',
        treatmentCount: record.totalTreatments || 0
      }));
      
      // 确保数据唯一性（以防后端返回重复数据）
      const uniqueRecords = mappedRecords.filter((record, index, arr) => 
        arr.findIndex(r => r.id === record.id) === index
      );
      
      patients.value = uniqueRecords;
      forceUpdateKey.value++; // 强制Vue重新渲染
      
      // 设置总数据量（用于显示）
      totalPatients.value = patients.value.length;
    } else {
      patients.value = [];
      totalPatients.value = 0;
    }
  } catch (error) {
    console.error('获取档案列表失败', error);
    MessagePlugin.error('获取档案列表失败，请稍后重试');
    patients.value = [];
    totalPatients.value = 0;
  }
};

// 搜索患者，重置到第一页
const searchPatients = () => {
  currentPage.value = 1;
  forceUpdateKey.value++; // 强制重新渲染
};

// 查看患者详情
const viewPatient = (cardId) => {
  // 从患者列表中找到对应的记录
  const record = patients.value.find(p => p.medicalRecordId === cardId);
  if (record && record.patientId) {
    router.push(`/patient/${record.patientId}`); // 使用patientId跳转到个人信息页面
  } else {
    MessagePlugin.error('无法找到患者信息');
  }
};

// 显示删除确认对话框
const showDeleteDialog = (medicalRecordId) => {
  patientToDelete.value = medicalRecordId;
  deletePassword.value = '';
  showDeleteConfirm.value = true;
};

// 关闭删除对话框
const closeDeleteDialog = () => {
  showDeleteConfirm.value = false;
  patientToDelete.value = null;
  deletePassword.value = '';
};

const confirmDelete = async () => {
  if (!deletePassword.value) {
    MessagePlugin.warning('请输入密码');
    return;
  }

  if (deletePassword.value !== initialPassword) {
    MessagePlugin.error('密码错误，请重试');
    return;
  }

  try {
    // 根据API文档，档案删除需要调用records接口并发送密码
    const response = await deleteRecord(patientToDelete.value, deletePassword.value);
    
    // 检查响应状态
    if (response.status === 200 || response.status === 204) {
      MessagePlugin.success('档案删除成功');
      closeDeleteDialog();
      fetchPatients(); // 重新获取档案列表以更新显示
    } else {
      MessagePlugin.error('删除档案失败，请稍后重试');
    }
  } catch (error) {
    console.error('删除档案失败', error);
    // 根据错误类型显示不同提示
    if (error.response) {
      if (error.response.status === 404) {
        MessagePlugin.error('档案不存在或已被删除');
      } else {
        MessagePlugin.error(`删除失败: ${error.response.data.message || '服务器错误'}`);
      }
    } else {
      MessagePlugin.error('网络错误，请检查连接');
    }
  }
};

// 上一页
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    forceUpdateKey.value++; // 强制重新渲染
  }
};

// 下一页
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    forceUpdateKey.value++; // 强制重新渲染
  }
};

// 格式化日期为 yyyy-MM-dd
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 返回主页
const goBack = () => {
  router.push('/home');
};

// 处理输入框聚焦和失焦
const handleInputFocus = (inputName) => {
  inputFocusStates.value[inputName] = true;
};

const handleInputBlur = (inputName) => {
  inputFocusStates.value[inputName] = false;
};

// 页面加载时获取患者列表
onMounted(() => {
  fetchPatients();
});
</script>

<style scoped>
/* 原有样式保持不变 */
.page {
  width: 1920px;
  height: 1080px;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: #fff;
}

.block_1 {
  position: relative;
  width: 1920px;
  height: 1080px;
  margin: 0;
  padding: 0;
  background: url(../assets/images/patientmangement/51da9ff83e9a4afabde39995a337d4f7.png) no-repeat;
  background-size: 100% 100%;
}

.box_1 {
  position: absolute;
  left: 0;
  top: 0;
  width: 1920px;
  height: 1080px;
  margin: 0;
  padding: 0;
  background: url(../assets/images/patientmangement/5ef5f27141c70c1e7e5a0f61a5b734f0.png) no-repeat;
  background-size: 100% 100%;
}

.group_9 {
  width: 1003px;
  height: 70px;
  margin: 20px 0 0 103px;
}

.image_1 {
  width: 151px;
  height: 61px;
  cursor: pointer;
}

.text_1 {
  width: 294px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41.67px;
  font-family: MicrosoftYaHei;
  text-align: center;
  white-space: nowrap;
  line-height: 49px;
  margin: 0 auto;
  position: absolute;
  top: 34px;
  left: 0;
  right: 0;
}

.group_10 {
  display: flex;
  align-items: center;
  width: 1585px;
  height: 91px;
  margin: 50px 0 0 142px;
}

.group_1 {
  display: flex;
  align-items: center;
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 474px;
  height: 66px;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
}

.text_2 {
  width: 153px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 33px;
  margin-left: 20px;
}

.image_2 {
  width: 1px;
  height: 43px;
  margin: 8px 0 0 26px;
}

.text_3 {
  width: 205px;
  height: 45px;
  font-size: 33px;
  font-family: MicrosoftYaHei;
  text-align: center;
  line-height: 25px;
  border: none;
  background: none;
  outline: none;
  color: rgba(89, 89, 89, 1);
  margin-left: 30px;
}

.text_3::placeholder {
  color: rgba(202, 201, 202, 0.7);
  font-family: MicrosoftYaHei;
  font-size: 33px;
  text-align: center;
}

.group_2 {
  display: flex;
  align-items: center;
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 346px;
  height: 66px;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin: 0 0 0 60px;
}

.text_4 {
  width: 100px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 33px;
  margin-left: 20px;
}

.image_3 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 20px;
}

.text_5 {
  width: 150px;
  height: 45px;
  font-size: 33px;
  font-family: MicrosoftYaHei;
  text-align: center;
  line-height: 25px;
  border: none;
  background: none;
  outline: none;
  color: rgba(89, 89, 89, 1);
  margin-left: 20px;
}

.text_5::placeholder {
  color: rgba(202, 201, 202, 0.7);
  font-family: MicrosoftYaHei;
  font-size: 33px;
  text-align: center;
}

.text-wrapper_1 {
  height: 91px;
  background: url(../assets/images/patientmangement/搜索2.png) 100% no-repeat;
  background-size: 100% 98%;
  display: flex;
  align-items: center;
  margin-left: 460px;
  margin-top: 12px;
  width: 227px;
  cursor: pointer;
}

.text_6 {
  width: 110px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 15px;
  margin-left: 60px;
  margin-top: 9px;
}

.section_3 {
  width: 1689px;
  height: 783px;
  background: url(../assets/images/patientmangement/f7ef7ec148e0f2b11bc0f46f49c5c8a0.png) 100% no-repeat;
  background-size: 100% 100%;
  margin: 0px 0 0 109px;
  position: relative;
}

.table-header {
  width: 1350px;
  height: 30px;
  margin: 47px 0 0 168px;
  display: flex;
  align-items: center;
}

.header-item {
  font-size: 25px;
  font-family: MicrosoftYaHei;
  text-align: center;
  white-space: nowrap;
  margin-top:80px;
  color: rgba(0, 0, 0, 1);
}

.header-item:nth-child(1) { width: 156px; }
.header-item:nth-child(2) { width: 71px; margin-left: 112px; }
.header-item:nth-child(3) { width: 74px; margin-left: 94px; }
.header-item:nth-child(4) { width: 73px; margin-left: 61px; }
.header-item:nth-child(5) { width: 157px; margin-left: 99px; }
.header-item:nth-child(6) { width: 155px; margin-left: 75px; }
.header-item:nth-child(7) { width: 75px; margin-left: 166px; }

.header-divider {
  width: 1476px;
  height: 1px;
  margin: 60px 0 0 116px;
}

.patient-row-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.patient-row {
  width: 1500px;
  height: 60px;
  margin: 0 0 0 168px;
  display: flex;
  align-items: center;
}

.row-item {
  font-size: 25px;
  font-family: MicrosoftYaHei;
  text-align: center;
  white-space: nowrap;
  color: #9A9A9A;
}

.row-item:nth-child(1) { width: 156px; }
.row-item:nth-child(2) { width: 71px; margin-left: 105px; }
.row-item:nth-child(3) { width: 74px; margin-left: 94px; }
.row-item:nth-child(4) { width: 73px; margin-left: 61px; }
.row-item:nth-child(5) { width: 157px; margin-left: 85px; }
.row-item:nth-child(6) { width: 155px; margin-left: 85px; }
.row-item.actions { 
  width: 200px; 
  margin-left: 99px; /* 调整为与表头相同的左边距 */
  display: flex; 
  gap: 10px; 
  justify-content: center; /* 使按钮居中 */
}


.row-divider {
  width: 1476px;
  height: 1px;
  margin: 10px 0 10px 116px;
}

.no-data {
  width: 100%;
  text-align: center;
  font-size: 28px;
  color: rgba(89, 89, 89, 1);
  margin-top: 50px;
}

/* 按钮样式 */
.view-btn {
  background-color: rgb(231, 241, 240);
  border: 2px solid rgb(166, 208, 203);
  padding: 5px 15px;
  border-radius: 6px;
  cursor: pointer;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  min-width: 70px;
}

.delete-btn {
  background-color: rgb(229, 229, 229);
  border: 2px solid rgb(221, 221, 221);
  padding: 5px 15px;
  border-radius: 6px;
  cursor: pointer;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  min-width: 70px;
}

.view-btn:hover, .delete-btn:hover {
  background-color: rgb(221, 231, 230);
  border-color: rgb(146, 188, 183);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.view-btn:active, .delete-btn:active {
  transform: translateY(1px);
}

/* 分页样式 */
.pagination-container {
  position: absolute;
  right: 60px;
  bottom: 71px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 6px 18px;
  width: 100%;
  height: 132%;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-info {
  margin: 0 18px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 19px;
  color: #333;
}

.pagination-btn {
  width: 50px;
  height: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(.disabled) {
  transform: scale(1.05);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn img {
  width: 46px;
  height: 46px;
}
.next-btn img {
  width: 46px;
  height: 46px;
}
.prev-btn img {
  width: 46px;
  height: 46px;
}

/* 删除确认弹窗样式 */
.delete-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-dialog-content {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 16px;
  width: 545px;
  height: 404px;
  border: 3px solid rgba(145, 201, 194, 1);
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.group_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 16px;
  width: 545px;
  height: 404px;
  border: 3px solid rgba(145, 201, 194, 1);
  margin: 550px auto;
}

.image_10 {
  width: 60px;
  height: 60px;
  margin: 20px 0 0 451px;
}

.group_3 {
  position: relative; /* 添加相对定位 */
  width: 416px;
  height: 69px;
  background: url(../assets/images/patientmangement/e98c37e1911abd0aa73f37747e79843d.png) -9px 0px no-repeat;
  background-size: 435px 91px;
  margin: 40px 0 0 64px;
  align-items: center; /* 垂直居中 */
  display: flex;
}

.label_1 {
  width: 43px;
  height: 42px;
  margin: 9px 0 0 37px;
}

.box_2 {
  width: 221px;
  margin: 0 10px; /* 调整间距 */
  display: flex;
  flex-direction: column;
}

.text_10 {
  width: 180px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(116, 116, 116, 1);
  font-size: 26px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 7px;
  margin-left: 60px;
}

.password-input {
  border: none;
  outline: none;
  background: transparent;
  transition: all 0.3s ease;
  width: 200px;
  height: 30px;
  overflow-wrap: break-word;
  color: rgba(116, 116, 116, 1);
  font-size: 26px;
  font-family: MicrosoftYaHei;
  text-align: left;
  margin-left: 26px;
  
}

.password-input:focus {
  border-bottom-color: rgba(145, 201, 194, 1);
}
.image_12 {
  margin-top: 10px;
}


.group_4 {
  height: 69px;
  background: url(../assets/images/patientmangement/933debd2590e08c413b5a6efa676f37e.png) -10px 0px no-repeat;
  background-size: 252px 92px;
  width: 232px;
  margin: 76px 0 70px 157px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.group_4:hover {
  transform: scale(1.05);
}

.text-wrapper_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 3px 0 0 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.text_11 {
  width: 93px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  transition: all 0.3s ease;
}

/* 弹窗淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮点击动画 */
@keyframes buttonClick {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.button-click {
  animation: buttonClick 0.3s ease;
}

/* 按钮动画效果 (与ProcessMangementView.vue保持一致) */
.button {
  transition: transform 0.1s ease;
}

.button:active {
  transform: scale(0.95);
}

/* 特殊处理返回按钮 */
.image_1.button:active {
  transform: scale(0.98);
}

/* 特殊处理搜索按钮 */
.text-wrapper_1.button:active {
  transform: scale(0.98);
}

/* 特殊处理分页按钮 */
.pagination-btn.button:active:not(.disabled) {
  transform: scale(0.95);
}

/* 特殊处理确认按钮 */
.text-wrapper_5.button:active {
  transform: scale(0.98);
}

/* 特殊处理查看和删除按钮 */
.view-btn.button:active,
.delete-btn.button:active {
  transform: scale(0.95);
}
</style>