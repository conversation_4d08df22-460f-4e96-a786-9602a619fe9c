package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.service.*;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 硬件测试控制器
 * 用于测试和调试硬件通信功能
 */
@RestController
@RequestMapping("/api/test/hardware")
@CrossOrigin(origins = "*")
public class HardwareTestController {

    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private HardwareCommunicationInterface hardwareCommunication;
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    @Autowired(required = false)
    private MockHardwareCommunicationAdapter mockAdapter;

    /**
     * 测试硬件连接状态
     */
    @GetMapping("/connection-status")
    public ApiResponse<Object> getConnectionStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("connected", hardwareCommunication.isConnected());
            status.put("communicationType", hardwareCommunication.getCommunicationType());
            
            return ApiResponse.success("硬件连接状态", status);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取连接状态失败: " + e.getMessage());
        }
    }

    /**
     * 测试治疗头同步
     */
    @PostMapping("/sync-treatment-heads")
    public ApiResponse<Object> testSyncTreatmentHeads() {
        try {
            List<TreatmentHeadInfo> heads = hardwareService.syncAllTreatmentHeads();
            
            Map<String, Object> result = new HashMap<>();
            result.put("syncedHeads", heads.size());
            result.put("heads", heads);
            
            return ApiResponse.success("治疗头同步成功", result);
        } catch (Exception e) {
            return ApiResponse.error(500, "同步治疗头失败: " + e.getMessage());
        }
    }

    /**
     * 测试治疗头指示灯控制
     */
    @PostMapping("/test-lights")
    public ApiResponse<Object> testLights(@RequestParam String headNumbers, @RequestParam boolean turnOn) {
        try {
            String[] numbers = headNumbers.split(",");
            List<Integer> headNumberList = new ArrayList<>();
            for (String number : numbers) {
                headNumberList.add(Integer.parseInt(number.trim()));
            }

            if (turnOn) {
                // 创建指示灯请求列表
                List<TreatmentHeadLightRequest> lightRequests = headNumberList.stream()
                    .map(headNumber -> new TreatmentHeadLightRequest(headNumber, 1)) // 默认颜色代码1
                    .collect(Collectors.toList());

                List<TreatmentHeadLightResponse> responses = hardwareService.setTreatmentHeadLights(lightRequests);
                boolean success = responses != null && !responses.isEmpty();

                String message = String.format("点亮治疗头 %s 指示灯%s", headNumbers, success ? "成功" : "失败");
                return success ? ApiResponse.success(message, responses) : ApiResponse.error(500, message);
            } else {
                List<Integer> turnedOff = hardwareService.turnOffTreatmentHeadLights(headNumberList);
                boolean success = turnedOff != null && !turnedOff.isEmpty();

                String message = String.format("关闭治疗头 %s 指示灯%s", headNumbers, success ? "成功" : "失败");
                return success ? ApiResponse.success(message, turnedOff) : ApiResponse.error(500, message);
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "控制指示灯失败: " + e.getMessage());
        }
    }

    /**
     * 测试治疗启动
     */
    @PostMapping("/start-treatment")
    public ApiResponse<Object> testStartTreatment(
            @RequestParam int headNumber,
            @RequestParam(defaultValue = "20") int duration,
            @RequestParam(defaultValue = "45") int intensity,
            @RequestParam(defaultValue = "1000") int frequency) {
        try {
            boolean success = hardwareService.startTreatment(headNumber, duration, intensity, frequency);
            
            String message = String.format("治疗头 %d 启动治疗%s (时长:%d分钟, 强度:%d, 频率:%dHz)",
                                          headNumber, success ? "成功" : "失败", duration, intensity, frequency);

            return success ? ApiResponse.success(message, success) : ApiResponse.error(500, message);
        } catch (Exception e) {
            return ApiResponse.error(500, "启动治疗失败: " + e.getMessage());
        }
    }

    /**
     * 测试治疗停止
     */
    @PostMapping("/stop-treatment")
    public ApiResponse<Object> testStopTreatment(@RequestParam int headNumber) {
        try {
            boolean success = hardwareService.stopTreatment(headNumber);
            
            String message = String.format("治疗头 %d 停止治疗%s", headNumber, success ? "成功" : "失败");

            return success ? ApiResponse.success(message, success) : ApiResponse.error(500, message);
        } catch (Exception e) {
            return ApiResponse.error(500, "停止治疗失败: " + e.getMessage());
        }
    }

    /**
     * 批量测试治疗流程
     */
    @PostMapping("/test-treatment-flow")
    public ApiResponse<Object> testTreatmentFlow(@RequestParam String headNumbers) {
        try {
            String[] numbers = headNumbers.split(",");
            Map<String, Object> results = new HashMap<>();
            
            for (String numberStr : numbers) {
                int headNumber = Integer.parseInt(numberStr.trim());
                Map<String, Object> headResult = new HashMap<>();
                
                try {
                    // 1. 点亮指示灯
                    List<TreatmentHeadLightRequest> lightRequests = Arrays.asList(
                        new TreatmentHeadLightRequest(headNumber, 1)
                    );
                    List<TreatmentHeadLightResponse> lightResponses = hardwareService.setTreatmentHeadLights(lightRequests);
                    boolean lightSuccess = lightResponses != null && !lightResponses.isEmpty();
                    headResult.put("lightOn", lightSuccess);

                    Thread.sleep(1000); // 等待1秒

                    // 2. 启动治疗
                    boolean startSuccess = hardwareService.startTreatment(headNumber, 5, 30, 1000);
                    headResult.put("treatmentStart", startSuccess);

                    Thread.sleep(2000); // 模拟治疗2秒

                    // 3. 停止治疗
                    boolean stopSuccess = hardwareService.stopTreatment(headNumber);
                    headResult.put("treatmentStop", stopSuccess);

                    // 4. 关闭指示灯
                    List<Integer> turnedOff = hardwareService.turnOffTreatmentHeadLights(Arrays.asList(headNumber));
                    boolean lightOffSuccess = turnedOff != null && !turnedOff.isEmpty();
                    headResult.put("lightOff", lightOffSuccess);
                    
                    headResult.put("overall", lightSuccess && startSuccess && stopSuccess && lightOffSuccess);
                    
                } catch (Exception e) {
                    headResult.put("error", e.getMessage());
                    headResult.put("overall", false);
                }
                
                results.put("head_" + headNumber, headResult);
            }
            
            return ApiResponse.success("批量治疗流程测试完成", results);
        } catch (Exception e) {
            return ApiResponse.error(500, "批量测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据库中的治疗头状态
     */
    @GetMapping("/database-heads")
    public ApiResponse<Object> getDatabaseHeads() {
        try {
            List<TreatmentHead> heads = treatmentHeadRepository.findAll();
            return ApiResponse.success("数据库治疗头数据", heads);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取数据库治疗头失败: " + e.getMessage());
        }
    }

    /**
     * 模拟硬件连接状态切换（仅在Mock模式下可用）
     */
    @PostMapping("/mock/set-connection")
    public ApiResponse<Object> setMockConnection(@RequestParam boolean connected) {
        if (mockAdapter == null) {
            return ApiResponse.error(400, "当前不是Mock模式，无法设置模拟连接状态");
        }
        
        try {
            mockAdapter.setConnected(connected);
            String message = "模拟硬件连接状态已设置为: " + (connected ? "已连接" : "已断开");
            return ApiResponse.success(message, connected);
        } catch (Exception e) {
            return ApiResponse.error(500, "设置模拟连接状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取模拟治疗头状态（仅在Mock模式下可用）
     */
    @GetMapping("/mock/head-state/{headNumber}")
    public ApiResponse<Object> getMockHeadState(@PathVariable int headNumber) {
        if (mockAdapter == null) {
            return ApiResponse.error(400, "当前不是Mock模式，无法获取模拟状态");
        }
        
        try {
            Object state = mockAdapter.getMockHeadState(headNumber);
            return ApiResponse.success("模拟治疗头状态", state);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取模拟状态失败: " + e.getMessage());
        }
    }

    /**
     * 重置所有治疗头到初始状态
     */
    @PostMapping("/reset-all-heads")
    public ApiResponse<Object> resetAllHeads() {
        try {
            // 关闭所有治疗头的治疗
            int successCount = 0;
            for (int i = 1; i <= 20; i++) {
                try {
                    hardwareService.stopTreatment(i);
                    successCount++;
                } catch (Exception e) {
                    // 忽略单个治疗头的错误
                }
            }
            
            // 关闭所有指示灯
            List<Integer> allHeads = new ArrayList<>();
            for (int i = 1; i <= 20; i++) {
                allHeads.add(i);
            }
            hardwareService.turnOffTreatmentHeadLights(allHeads);

            String message = String.format("已重置 %d 个治疗头到初始状态", successCount);
            return ApiResponse.success(message, successCount);
        } catch (Exception e) {
            return ApiResponse.error(500, "重置治疗头失败: " + e.getMessage());
        }
    }
}
