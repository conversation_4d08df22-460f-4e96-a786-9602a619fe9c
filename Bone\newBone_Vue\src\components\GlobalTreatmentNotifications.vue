<template>
  <div class="global-treatment-notifications">
    <div
      v-for="(notification, index) in notifications"
      :key="notification.id"
      class="notification-wrapper"
      :style="{
        left: `${54 + index * 130}px`,
        top: '100px',
        zIndex: notification.zIndex
      }"
      @mousedown="bringToFront(notification.id)"
      ref="notificationRefs"
    >
      <!-- 治疗完成通知 -->
      <div
        v-if="notification.type === 'TREATMENT_COMPLETED'"
        class="notification treatment-completed"
        :style="{
          backgroundImage: `url(${completedBgImage})`,
          transform: `translate(${notification.offsetX}px, ${notification.offsetY}px)`
        }"
        @mousedown="startDrag(notification.id, $event)"
      >
        <div class="close-btn" @click="closeNotification(notification.id)">
          <img :src="closeIcon" alt="关闭" />
        </div>
        <div class="content">
          <div class="patient-name">{{ notification.patientName }}</div>
          <div class="completion-text">全部治疗已完成！</div>
        </div>
      </div>

      <!-- 治疗头待取回通知 -->
      <div
        v-else-if="notification.type === 'PICKUP_REMINDER'"
        class="notification pickup-reminder"
        :style="{
          backgroundImage: `url(${pickupBgImage})`,
          transform: `translate(${notification.offsetX}px, ${notification.offsetY}px)`
        }"
        @mousedown="startDrag(notification.id, $event)"
      >
        <div class="close-btn" @click="closeNotification(notification.id)">
          <img :src="closeIcon" alt="关闭" />
        </div>
        <div class="content">
          <div class="pickup-text">{{ formatHeadNumbers(notification.headNumbers) }}治疗头待取回</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useGlobalTreatmentMonitorStore, NotificationType, type NotificationData } from '@/stores/globalTreatmentMonitor'

// 导入图片
import completedBgImage from '@/assets/images/交互界面/主页/提示某人.png'
import pickupBgImage from '@/assets/images/交互界面/主页/提示治疗头.png'
import closeIcon from '@/assets/images/交互界面/主页/提示×.png'

// 扩展通知数据接口
interface ExtendedNotificationData extends NotificationData {
  zIndex?: number
  offsetX?: number
  offsetY?: number
}

const route = useRoute()
const treatmentMonitorStore = useGlobalTreatmentMonitorStore()

// 状态
const notifications = ref<ExtendedNotificationData[]>([])
const notificationRefs = ref([])
let maxZIndex = 1000

// 拖拽状态
let dragState = {
  isDragging: false,
  notificationId: '',
  startX: 0,
  startY: 0,
  startOffsetX: 0,
  startOffsetY: 0
}

// 检查是否应该显示通知
const shouldShowNotifications = computed(() => {
  return treatmentMonitorStore.shouldShowNotifications(route.path)
})

// 格式化治疗头编号
const formatHeadNumbers = (headNumbers: number[] | undefined): string => {
  if (!headNumbers || headNumbers.length === 0) {
    return '该患者'
  }
  return headNumbers.join(',') + '号'
}

// 监听store中的通知变化
watch(
  () => treatmentMonitorStore.notifications,
  (newNotifications) => {
    if (!shouldShowNotifications.value) {
      console.log('当前页面不显示通知，清空本地通知')
      notifications.value = []
      return
    }

    // 添加新通知
    newNotifications.forEach(storeNotification => {
      const existingIndex = notifications.value.findIndex(n => n.id === storeNotification.id)
      
      if (existingIndex === -1) {
        // 新通知，添加扩展属性
        const extendedNotification: ExtendedNotificationData = {
          ...storeNotification,
          zIndex: ++maxZIndex,
          offsetX: 0,
          offsetY: 0
        }
        notifications.value.push(extendedNotification)
        console.log('添加新通知到显示列表:', extendedNotification)
      }
    })

    // 移除已删除的通知
    notifications.value = notifications.value.filter(localNotification =>
      newNotifications.some(storeNotification => storeNotification.id === localNotification.id)
    )
  },
  { deep: true, immediate: true }
)

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    if (!treatmentMonitorStore.shouldShowNotifications(newPath)) {
      console.log('切换到排除页面，清空通知显示')
      notifications.value = []
    }
  }
)

// 置顶通知
const bringToFront = (notificationId: string) => {
  const notification = notifications.value.find(n => n.id === notificationId)
  if (notification) {
    notification.zIndex = ++maxZIndex
  }
}

// 关闭通知
const closeNotification = (notificationId: string) => {
  console.log('关闭通知:', notificationId)
  treatmentMonitorStore.removeNotification(notificationId)
}

// 开始拖拽
const startDrag = (notificationId: string, event: MouseEvent) => {
  event.preventDefault()
  
  const notification = notifications.value.find(n => n.id === notificationId)
  if (!notification) return

  dragState = {
    isDragging: true,
    notificationId,
    startX: event.clientX,
    startY: event.clientY,
    startOffsetX: notification.offsetX || 0,
    startOffsetY: notification.offsetY || 0
  }

  // 置顶
  bringToFront(notificationId)

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

// 处理拖拽
const handleDrag = (event: MouseEvent) => {
  if (!dragState.isDragging) return

  const notification = notifications.value.find(n => n.id === dragState.notificationId)
  if (!notification) return

  const deltaX = event.clientX - dragState.startX
  const deltaY = event.clientY - dragState.startY

  notification.offsetX = dragState.startOffsetX + deltaX
  notification.offsetY = dragState.startOffsetY + deltaY
}

// 停止拖拽
const stopDrag = () => {
  dragState.isDragging = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 组件挂载时开始监听
onMounted(() => {
  console.log('GlobalTreatmentNotifications 组件已挂载')
  
  // 开始全局监听
  treatmentMonitorStore.startMonitoring(route.path)
})

// 组件卸载时清理
onUnmounted(() => {
  console.log('GlobalTreatmentNotifications 组件已卸载')
  
  // 清理拖拽事件监听
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  
  // 停止全局监听
  treatmentMonitorStore.stopMonitoring()
})
</script>

<style scoped>
.global-treatment-notifications {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 9999;
}

.notification-wrapper {
  position: absolute;
  pointer-events: auto;
}

.notification {
  width: 404px;
  height: 496px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  cursor: move;
  user-select: none;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  cursor: pointer;
  z-index: 10;
}

.close-btn img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #7C7979;
  font-family: 'MicrosoftYaHei', sans-serif;
}

.treatment-completed .patient-name {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.treatment-completed .completion-text {
  font-size: 20px;
}

.pickup-reminder .pickup-text {
  font-size: 20px;
  font-weight: bold;
}

/* 防止文本选择 */
.notification * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style>
