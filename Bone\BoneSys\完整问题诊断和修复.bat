@echo off
chcp 65001 > nul
echo ========================================
echo 完整问题诊断和修复脚本
echo ========================================
echo.

echo [步骤1] 检查数据库compartment_type字段...
mysql -u root -p bonesys -e "
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'bonesys' 
AND TABLE_NAME = 'treatment_heads' 
AND COLUMN_NAME = 'compartment_type';
"

if %errorlevel% neq 0 (
    echo ❌ 数据库连接失败，请检查MySQL服务
    pause
    exit /b 1
)
echo.

echo [步骤2] 检查治疗头数据的compartment_type设置...
mysql -u root -p bonesys -e "
SELECT 
    head_number,
    compartment_type,
    CASE 
        WHEN compartment_type IS NULL THEN '❌ 未设置'
        WHEN compartment_type = 'SHALLOW' THEN '✅ 浅部'
        WHEN compartment_type = 'DEEP' THEN '✅ 深部'
        ELSE '❓ 未知值'
    END as status
FROM treatment_heads 
ORDER BY head_number;
"
echo.

echo [步骤3] 如果compartment_type为NULL，执行修复...
set /p fix_compartment="是否需要修复compartment_type字段？(y/n): "
if /i "%fix_compartment%"=="y" (
    echo 正在修复compartment_type字段...
    mysql -u root -p bonesys -e "
    -- 如果字段不存在，先添加
    ALTER TABLE treatment_heads 
    ADD COLUMN IF NOT EXISTS compartment_type VARCHAR(10) NULL DEFAULT NULL 
    COMMENT '仓位类型: SHALLOW(浅部上层), DEEP(深部下层)' 
    AFTER slot_number;
    
    -- 更新数据
    UPDATE treatment_heads 
    SET compartment_type = 'SHALLOW' 
    WHERE head_number BETWEEN 1 AND 10 AND compartment_type IS NULL;
    
    UPDATE treatment_heads 
    SET compartment_type = 'DEEP' 
    WHERE head_number BETWEEN 11 AND 20 AND compartment_type IS NULL;
    "
    echo ✅ compartment_type字段修复完成
)
echo.

echo [步骤4] 测试后端API连接...
echo 测试健康检查：
curl -s -w "状态码: %%{http_code}\n" http://localhost:8080/api/health
echo.

echo 测试治疗头数据API：
curl -s -w "状态码: %%{http_code}\n" "http://localhost:8080/api/hardware/heads?page=1&size=3"
echo.

echo [步骤5] 测试治疗参数可用性检查API...
echo 测试浅部贴片：
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"TEST001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"手腕\",\"color\":\"#00FF00\",\"parameters\":{\"time\":\"10分钟\",\"intensity\":\"25\",\"frequency\":\"100\",\"depth\":\"浅部\",\"count\":2}}]}" ^
  -w "状态码: %%{http_code}\n"
echo.

echo 测试深部贴片：
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"TEST002\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"肩颈\",\"color\":\"#FF0000\",\"parameters\":{\"time\":\"15分钟\",\"intensity\":\"30\",\"frequency\":\"1000\",\"depth\":\"深部\",\"count\":3}}]}" ^
  -w "状态码: %%{http_code}\n"
echo.

echo [步骤6] 检查定时同步状态...
echo 测试同步状态API：
curl -s http://localhost:8080/api/hardware/treatment-heads/sync/status -w "状态码: %%{http_code}\n"
echo.

echo [步骤7] 手动触发一次同步...
echo 触发手动同步：
curl -s -X POST http://localhost:8080/api/hardware/treatment-heads/sync/trigger -w "状态码: %%{http_code}\n"
echo.

echo [步骤8] 验证修复结果...
mysql -u root -p bonesys -e "
SELECT 
    '浅部治疗头' as type,
    COUNT(*) as total,
    COUNT(CASE WHEN compartment_type = 'SHALLOW' THEN 1 END) as correct_type,
    COUNT(CASE WHEN battery_level >= 20 THEN 1 END) as good_battery
FROM treatment_heads 
WHERE head_number BETWEEN 1 AND 10

UNION ALL

SELECT 
    '深部治疗头' as type,
    COUNT(*) as total,
    COUNT(CASE WHEN compartment_type = 'DEEP' THEN 1 END) as correct_type,
    COUNT(CASE WHEN battery_level >= 20 THEN 1 END) as good_battery
FROM treatment_heads 
WHERE head_number BETWEEN 11 AND 20;
"
echo.

echo ========================================
echo 诊断和修复完成！
echo ========================================
echo.
echo 🔍 检查结果说明：
echo.
echo 1. **compartment_type字段**
echo    - 应该存在且不为NULL
echo    - 治疗头1-10应该是SHALLOW
echo    - 治疗头11-20应该是DEEP
echo.
echo 2. **API连接测试**
echo    - 状态码200表示成功
echo    - 状态码404表示路径错误
echo    - 状态码500表示服务器错误
echo.
echo 3. **浅部深部识别**
echo    - 浅部贴片应该匹配SHALLOW类型治疗头
echo    - 深部贴片应该匹配DEEP类型治疗头
echo.
echo 4. **定时同步**
echo    - 每10秒自动同步治疗头数据
echo    - 更新电量、槽号、状态信息
echo.
echo 🚨 如果仍有问题：
echo.
echo "网络连接问题："
echo "1. 确保后端在8080端口运行"
echo "2. 确保前端在5173端口运行"
echo "3. 检查防火墙设置"
echo.
echo "API返回错误："
echo "1. 检查后端日志"
echo "2. 确认数据库数据正确"
echo "3. 重启后端应用"
echo.
echo "前端代理问题："
echo "1. 检查vite.config.ts代理配置"
echo "2. 重启前端开发服务器"
echo "3. 清除浏览器缓存"
echo.
pause
