package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;

import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * WebSocket硬件通信服务
 * 替代原有的串口通信，使用WebSocket与硬件控制板通信
 *
 * 基于测试脚本websocket_test(1).txt的成功经验实现
 * 连接地址：ws://122.51.229.122:6123
 */
@Service
public class WebSocketHardwareService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketHardwareService.class);
    
    // WebSocket连接配置
    @Value("${hardware.websocket.url:ws://122.51.229.122:6123}")
    private String websocketUrl;
    
    @Value("${hardware.websocket.timeout:5000}")
    private int timeoutMs;
    
    @Value("${hardware.websocket.reconnect.enabled:true}")
    private boolean reconnectEnabled;
    
    @Value("${hardware.websocket.reconnect.interval:5000}")
    private int reconnectIntervalMs;
    
    // WebSocket连接状态
    private WebSocketClient webSocketClient;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicReference<String> lastResponse = new AtomicReference<>();
    private final AtomicReference<CompletableFuture<String>> pendingResponse = new AtomicReference<>();

    // 等待serial_data的标志
    private final AtomicBoolean waitingForSerialData = new AtomicBoolean(false);

    // 累积的serial_data数据（处理分片数据）
    private final StringBuilder accumulatedData = new StringBuilder();

    /**
     * 应用启动时自动连接到WebSocket服务器
     */
    @PostConstruct
    public void initialize() {
        try {
            logger.info("Initializing WebSocket hardware connection on application startup");
            connect();
            logger.info("WebSocket hardware connection initialization completed successfully");
        } catch (Exception e) {
            logger.warn("Failed to initialize WebSocket hardware connection on startup: {}", e.getMessage());
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 连接到WebSocket服务器
     */
    public void connect() throws SerialCommunicationException {
        try {
            if (isConnected.get() && webSocketClient != null && webSocketClient.isOpen()) {
                logger.debug("WebSocket already connected");
                return;
            }

            logger.info("Connecting to WebSocket hardware server: {}", websocketUrl);

            URI serverUri = new URI(websocketUrl);

            webSocketClient = new WebSocketClient(serverUri) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    logger.info("WebSocket connection opened: status={}", handshake.getHttpStatus());
                    isConnected.set(true);
                }

                @Override
                public void onMessage(String message) {
                    logger.debug("Received WebSocket message: {}", message);
                    handleMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    logger.info("WebSocket connection closed: code={}, reason={}, remote={}", code, reason, remote);
                    isConnected.set(false);

                    // 如果有等待中的响应，完成异常
                    CompletableFuture<String> future = pendingResponse.getAndSet(null);
                    if (future != null && !future.isDone()) {
                        future.completeExceptionally(new SerialCommunicationException("WebSocket connection closed"));
                    }
                }

                @Override
                public void onError(Exception ex) {
                    logger.error("WebSocket error occurred", ex);
                    isConnected.set(false);

                    // 如果有等待中的响应，完成异常
                    CompletableFuture<String> future = pendingResponse.getAndSet(null);
                    if (future != null && !future.isDone()) {
                        future.completeExceptionally(new SerialCommunicationException("WebSocket error", ex));
                    }
                }
            };

            // 连接到服务器
            boolean connected = webSocketClient.connectBlocking(timeoutMs, TimeUnit.MILLISECONDS);

            if (connected && webSocketClient.isOpen()) {
                logger.info("Successfully connected to WebSocket hardware server");
            } else {
                throw new SerialCommunicationException("Failed to establish WebSocket connection");
            }

        } catch (Exception e) {
            isConnected.set(false);
            logger.error("Failed to connect to WebSocket hardware server", e);
            throw new SerialCommunicationException("WebSocket connection failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * 断开WebSocket连接
     */
    public void disconnect() {
        try {
            if (webSocketClient != null && webSocketClient.isOpen()) {
                webSocketClient.close();
                logger.info("WebSocket connection closed");
            }
        } catch (Exception e) {
            logger.warn("Error closing WebSocket connection", e);
        } finally {
            isConnected.set(false);
            webSocketClient = null;
        }
    }
    
    /**
     * 发送命令到硬件控制板（默认发送到下层，保持向后兼容）
     *
     * @param command 要发送的命令
     * @return 硬件返回的响应
     * @throws SerialCommunicationException 通信异常
     */
    public synchronized String sendCommand(String command) throws SerialCommunicationException {
        return sendCommand(command, "lower");
    }

    /**
     * 发送命令到指定层级的硬件控制板
     *
     * @param command 要发送的命令
     * @param layer 层级：upper(上层治疗仓) 或 lower(下层治疗仓)
     * @return 硬件返回的响应
     * @throws SerialCommunicationException 通信异常
     */
    public synchronized String sendCommand(String command, String layer) throws SerialCommunicationException {
        if (!isConnected.get() || webSocketClient == null || !webSocketClient.isOpen()) {
            // 尝试重连
            if (reconnectEnabled) {
                logger.info("WebSocket not connected, attempting to reconnect...");
                connect();
            } else {
                throw new SerialCommunicationException("WebSocket is not connected");
            }
        }

        try {
            logger.debug("Sending WebSocket command to {}: {}", layer, command.trim());

            // 构建JSON消息
            String jsonMessage = buildJsonMessage(command, layer);

            // 创建等待响应的Future
            CompletableFuture<String> responseFuture = new CompletableFuture<>();
            pendingResponse.set(responseFuture);

            // 发送消息
            webSocketClient.send(jsonMessage);

            // 等待响应
            String response = responseFuture.get(timeoutMs, TimeUnit.MILLISECONDS);

            logger.debug("Received WebSocket response from {}: {}", layer, response.trim());
            return response;

        } catch (Exception e) {
            logger.error("Failed to send WebSocket command to {}: {}", layer, command.trim(), e);

            // 清理等待中的响应
            CompletableFuture<String> future = pendingResponse.getAndSet(null);
            if (future != null && !future.isDone()) {
                future.completeExceptionally(e);
            }

            throw new SerialCommunicationException("Failed to send WebSocket command to " + layer + ": " + command.trim(), e);
        }
    }
    
    // 已移除determineLayer方法，现在由调用者指定层级
    
    /**
     * 构建JSON消息格式
     * 基于测试脚本中成功的消息格式
     */
    private String buildJsonMessage(String command, String layer) {
        return String.format("{\"type\": \"serial_command\", \"layer\": \"%s\", \"command\": \"%s\"}", 
                           layer, command.replace("\r", "\\r").replace("\n", "\\n"));
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected.get() && webSocketClient != null && webSocketClient.isOpen();
    }

    /**
     * 重新连接
     */
    public void reconnect() throws SerialCommunicationException {
        logger.info("Reconnecting WebSocket hardware service...");
        disconnect();
        try {
            Thread.sleep(reconnectIntervalMs);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        connect();
    }

    /**
     * 处理WebSocket消息
     *
     * WebSocket会发送两种消息：
     * 1. serial_response - 命令确认（通常response字段为空）
     * 2. serial_data - 实际数据（data字段包含TRZI等响应）
     */
    private void handleMessage(String message) {
        try {
            logger.debug("Processing WebSocket message: {}", message);

            // 检查消息类型
            if (message.contains("\"type\": \"serial_response\"")) {
                // 检查是否直接包含响应数据
                String responseData = parseHardwareResponse(message);
                
                if (responseData != null && !responseData.trim().isEmpty()) {
                    // serial_response中直接包含数据，立即完成响应
                    logger.debug("Received serial_response with data: {}", responseData);
                    CompletableFuture<String> future = pendingResponse.getAndSet(null);
                    if (future != null && !future.isDone()) {
                        future.complete(responseData);
                        logger.debug("Completed response with serial_response data: {}", responseData);
                    }
                    lastResponse.set(responseData);
                    waitingForSerialData.set(false);
                    return;
                } else {
                    // 没有数据，等待serial_data
                    logger.debug("Received serial_response confirmation, waiting for serial_data");
                    waitingForSerialData.set(true);
                    synchronized (accumulatedData) {
                        accumulatedData.setLength(0);
                    }
                    return;
                }
            }

            if (message.contains("\"type\": \"serial_data\"")) {
                // 这是实际数据，可能是分片的
                String hardwareResponse = parseHardwareResponse(message);

                if (hardwareResponse != null && !hardwareResponse.isEmpty()) {
                    // 累积数据
                    synchronized (accumulatedData) {
                        accumulatedData.append(hardwareResponse);

                        // 检查是否是完整的响应（以\r\n结尾）
                        String currentData = accumulatedData.toString();
                        if (currentData.endsWith("\r\n") || currentData.endsWith("\n")) {
                            // 数据完整，完成响应
                            CompletableFuture<String> future = pendingResponse.getAndSet(null);
                            if (future != null && !future.isDone()) {
                                future.complete(currentData);
                                logger.debug("Completed response with accumulated serial_data: {}", currentData);
                            }

                            lastResponse.set(currentData);
                            waitingForSerialData.set(false);
                            accumulatedData.setLength(0); // 清空累积数据
                        } else {
                            // 数据不完整，继续等待
                            logger.debug("Accumulated partial serial_data: {}", currentData);
                        }
                    }
                }
                return;
            }

            // 处理其他类型的消息或旧格式
            String hardwareResponse = parseHardwareResponse(message);
            if (hardwareResponse != null && !hardwareResponse.isEmpty()) {
                CompletableFuture<String> future = pendingResponse.getAndSet(null);
                if (future != null && !future.isDone()) {
                    future.complete(hardwareResponse);
                }
                lastResponse.set(hardwareResponse);
            }

        } catch (Exception e) {
            logger.error("Error processing WebSocket message", e);

            // 如果解析失败，完成异常响应
            CompletableFuture<String> future = pendingResponse.getAndSet(null);
            if (future != null && !future.isDone()) {
                future.completeExceptionally(e);
            }
        }
    }
    
    /**
     * 解析硬件响应
     * 从WebSocket JSON消息中提取实际的硬件响应数据
     *
     * 处理两种消息类型：
     * 1. serial_response - 命令确认（通常response字段为空）
     * 2. serial_data - 实际数据（data字段包含TRZI等响应）
     */
    private String parseHardwareResponse(String jsonMessage) {
        try {
            logger.debug("Parsing WebSocket message: {}", jsonMessage);

            // 检查是否是serial_data类型（包含实际数据）
            if (jsonMessage.contains("\"type\": \"serial_data\"") && jsonMessage.contains("\"data\"")) {
                int startIndex = jsonMessage.indexOf("\"data\": \"") + 9; // 注意空格
                if (startIndex == 8) { // 如果没找到带空格的，尝试不带空格的
                    startIndex = jsonMessage.indexOf("\"data\":\"") + 8;
                }

                int endIndex = jsonMessage.indexOf("\"", startIndex);
                if (startIndex > 7 && endIndex > startIndex) {
                    String data = jsonMessage.substring(startIndex, endIndex);
                    // 处理转义字符
                    String parsedData = data.replace("\\r", "\r").replace("\\n", "\n");
                    logger.debug("Extracted serial_data: {}", parsedData);
                    return parsedData;
                } else {
                    logger.warn("Failed to extract data field from serial_data message: startIndex={}, endIndex={}", startIndex, endIndex);
                }
            }

            // 检查是否是serial_response类型（命令确认）
            if (jsonMessage.contains("\"type\": \"serial_response\"")) {
                // 检查response字段是否包含实际数据
                if (jsonMessage.contains("\"response\"")) {
                    // 处理带空格和不带空格的格式
                    int startIndex = jsonMessage.indexOf("\"response\": \"");
                    if (startIndex != -1) {
                        startIndex += 13; // "response": " 长度
                    } else {
                        startIndex = jsonMessage.indexOf("\"response\":\"");
                        if (startIndex != -1) {
                            startIndex += 12; // "response":" 长度
                        }
                    }
                    
                    if (startIndex > 0) {
                        int endIndex = jsonMessage.indexOf("\"", startIndex);
                        if (endIndex > startIndex) {
                            String response = jsonMessage.substring(startIndex, endIndex);
                            if (!response.trim().isEmpty()) {
                                // 处理转义字符
                                String parsedResponse = response.replace("\\r", "\r").replace("\\n", "\n");
                                logger.debug("Extracted serial_response data: {}", parsedResponse);
                                return parsedResponse;
                            }
                        }
                    }
                }

                // response字段为空或未找到，返回空字符串表示等待serial_data
                logger.debug("No data in serial_response, expecting serial_data");
                return "";
            }

            // 如果无法解析，返回原始消息
            logger.warn("Unable to parse WebSocket message format: {}", jsonMessage);
            return jsonMessage;

        } catch (Exception e) {
            logger.warn("Failed to parse hardware response, returning raw message", e);
            return jsonMessage;
        }
    }
}
