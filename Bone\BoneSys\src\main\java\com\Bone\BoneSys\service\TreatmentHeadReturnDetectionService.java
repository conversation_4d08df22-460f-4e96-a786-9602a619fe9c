package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.entity.TreatmentDetail;
import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.repository.TreatmentDetailRepository;
import com.Bone.BoneSys.repository.ProcessRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 治疗头归还检测服务
 * 通过硬件主板查询治疗头状态，自动检测治疗头归还并更新治疗详情状态
 */
@Service
public class TreatmentHeadReturnDetectionService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadReturnDetectionService.class);
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private ProcessRepository processRepository;

    @Autowired
    private RecordRepository recordRepository;
    
    /**
     * 定时检测治疗头归还状态
     * 每30秒执行一次硬件状态同步和归还检测
     */
    @Scheduled(fixedRate = 30000) // 30秒执行一次
    public void detectTreatmentHeadReturns() {
        try {
            logger.debug("开始检测治疗头归还状态...");
            
            // 1. 获取所有待归还的治疗详情
            List<TreatmentDetail> awaitingReturnDetails = treatmentDetailRepository
                .findByStatus(TreatmentDetailStatus.AWAITING_RETURN);
            
            if (awaitingReturnDetails.isEmpty()) {
                logger.debug("没有待归还的治疗详情，跳过检测");
                return;
            }
            
            // 2. 同步硬件状态，获取当前在治疗仓中的治疗头
            List<TreatmentHeadInfo> headsInCompartment = hardwareService.syncAllTreatmentHeads();
            Set<Integer> returnedHeadNumbers = headsInCompartment.stream()
                .map(TreatmentHeadInfo::getHeadNumber)
                .collect(Collectors.toSet());
            
            logger.debug("检测到 {} 个治疗头在治疗仓中: {}", 
                        returnedHeadNumbers.size(), returnedHeadNumbers);
            
            // 3. 检查每个待归还的治疗详情
            int returnedCount = 0;
            for (TreatmentDetail detail : awaitingReturnDetails) {
                Integer headNumber = detail.getHeadNumberUsed();
                
                if (headNumber != null && returnedHeadNumbers.contains(headNumber)) {
                    // 治疗头已归还到治疗仓，更新状态
                    logger.info("检测到治疗头 {} 已归还，更新治疗详情 {} 状态", 
                               headNumber, detail.getId());
                    
                    detail.setStatus(TreatmentDetailStatus.RETURNED);
                    treatmentDetailRepository.save(detail);
                    
                    // 检查并更新进程状态
                    checkAndCompleteProcessForReturned(detail.getProcess().getId());
                    
                    returnedCount++;
                }
            }
            
            if (returnedCount > 0) {
                logger.info("本次检测完成，共检测到 {} 个治疗头归还", returnedCount);
            } else {
                logger.debug("本次检测完成，没有检测到治疗头归还");
            }
            
        } catch (Exception e) {
            logger.error("治疗头归还检测过程中发生错误", e);
        }
    }
    
    /**
     * 手动触发治疗头归还检测
     * 用于立即检测归还状态，不等待定时任务
     */
    public int manualDetectReturns() {
        try {
            logger.info("手动触发治疗头归还检测...");
            
            // 获取所有待归还的治疗详情
            List<TreatmentDetail> awaitingReturnDetails = treatmentDetailRepository
                .findByStatus(TreatmentDetailStatus.AWAITING_RETURN);
            
            if (awaitingReturnDetails.isEmpty()) {
                logger.info("没有待归还的治疗详情");
                return 0;
            }
            
            // 同步硬件状态
            List<TreatmentHeadInfo> headsInCompartment = hardwareService.syncAllTreatmentHeads();
            Set<Integer> returnedHeadNumbers = headsInCompartment.stream()
                .map(TreatmentHeadInfo::getHeadNumber)
                .collect(Collectors.toSet());
            
            // 检查归还状态
            int returnedCount = 0;
            for (TreatmentDetail detail : awaitingReturnDetails) {
                Integer headNumber = detail.getHeadNumberUsed();
                
                if (headNumber != null && returnedHeadNumbers.contains(headNumber)) {
                    detail.setStatus(TreatmentDetailStatus.RETURNED);
                    treatmentDetailRepository.save(detail);
                    
                    checkAndCompleteProcessForReturned(detail.getProcess().getId());
                    
                    returnedCount++;
                    logger.info("手动检测：治疗头 {} 已归还", headNumber);
                }
            }
            
            logger.info("手动检测完成，共检测到 {} 个治疗头归还", returnedCount);
            return returnedCount;
            
        } catch (Exception e) {
            logger.error("手动治疗头归还检测失败", e);
            return 0;
        }
    }
    
    /**
     * 获取当前待归还的治疗头数量
     */
    public int getAwaitingReturnCount() {
        List<TreatmentDetail> awaitingDetails = treatmentDetailRepository.findByStatus(TreatmentDetailStatus.AWAITING_RETURN);
        return awaitingDetails.size();
    }

    /**
     * 获取指定进程的待归还治疗头列表
     */
    public List<Integer> getAwaitingReturnHeadNumbers(Long processId) {
        List<TreatmentDetail> allDetails = treatmentDetailRepository.findByProcessId(processId);

        return allDetails.stream()
            .filter(detail -> detail.getStatus() == TreatmentDetailStatus.AWAITING_RETURN)
            .map(TreatmentDetail::getHeadNumberUsed)
            .filter(headNumber -> headNumber != null)
            .collect(Collectors.toList());
    }

    /**
     * 检查并完成进程（当所有治疗详情都已归还时）
     */
    private void checkAndCompleteProcessForReturned(Long processId) {
        List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(processId);
        boolean allReturned = details.stream()
                .allMatch(detail -> detail.getStatus() == TreatmentDetailStatus.RETURNED);

        if (allReturned) {
            Optional<Process> processOpt = processRepository.findById(processId);
            if (processOpt.isPresent()) {
                Process process = processOpt.get();
                if (process.getStatus() == ProcessStatus.IN_PROGRESS ||
                    process.getStatus() == ProcessStatus.COMPLETED ||
                    process.getStatus() == ProcessStatus.CANCELLED) {

                    // 所有治疗头都已归还，完成进程并更新档案统计
                    process.setStatus(ProcessStatus.COMPLETED);
                    process.setEndTime(LocalDateTime.now());

                    // 更新档案完成次数
                    var record = process.getRecord();
                    record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);

                    processRepository.save(process);
                    recordRepository.save(record);

                    logger.info("进程 {} 已完成，所有治疗头都已归还，档案 {} 完成次数更新为 {}",
                               processId, record.getRecordNumber(), record.getSessionsCompletedCount());
                }
            }
        }
    }
}
