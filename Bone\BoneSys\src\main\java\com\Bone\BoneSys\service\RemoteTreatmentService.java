package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.dto.hardware.TreatmentParamsRequest;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.service.HardwareCommunicationInterface;
import com.Bone.BoneSys.websocket.NotificationWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 远端治疗服务
 * 实现远端治疗模式的完整流程：
 * 1. 治疗头充足性检查
 * 2. 推荐治疗头点亮
 * 3. 逐个发送治疗参数（不启动）
 * 4. 下载进度条显示
 * 5. 失败处理和重试逻辑
 */
@Service
public class RemoteTreatmentService {
    
    private static final Logger logger = LoggerFactory.getLogger(RemoteTreatmentService.class);
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private HardwareCommandParser commandParser;
    
    @Autowired
    private HardwareCommunicationInterface hardwareCommunication;
    
    @Autowired
    private NotificationWebSocketHandler notificationHandler;
    
    // 指令超时时间（毫秒）
    private static final long COMMAND_TIMEOUT_MS = 15000; // 15秒
    
    /**
     * 远端治疗请求数据
     */
    public static class RemoteTreatmentRequest {
        private List<Integer> recommendedHeadNumbers;
        private int duration; // 治疗时长（分钟）
        private int intensity; // 治疗强度
        private int frequency; // 频率（100Hz或1000Hz）
        
        // getters and setters
        public List<Integer> getRecommendedHeadNumbers() { return recommendedHeadNumbers; }
        public void setRecommendedHeadNumbers(List<Integer> recommendedHeadNumbers) { this.recommendedHeadNumbers = recommendedHeadNumbers; }
        public int getDuration() { return duration; }
        public void setDuration(int duration) { this.duration = duration; }
        public int getIntensity() { return intensity; }
        public void setIntensity(int intensity) { this.intensity = intensity; }
        public int getFrequency() { return frequency; }
        public void setFrequency(int frequency) { this.frequency = frequency; }
    }
    
    /**
     * 远端治疗响应数据
     */
    public static class RemoteTreatmentResponse {
        private boolean success;
        private int totalHeads;
        private int successfulHeads;
        private int failedHeads;
        private List<Integer> successfulHeadNumbers;
        private List<FailedHeadInfo> failedHeadInfos;
        private String message;
        
        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public int getTotalHeads() { return totalHeads; }
        public void setTotalHeads(int totalHeads) { this.totalHeads = totalHeads; }
        public int getSuccessfulHeads() { return successfulHeads; }
        public void setSuccessfulHeads(int successfulHeads) { this.successfulHeads = successfulHeads; }
        public int getFailedHeads() { return failedHeads; }
        public void setFailedHeads(int failedHeads) { this.failedHeads = failedHeads; }
        public List<Integer> getSuccessfulHeadNumbers() { return successfulHeadNumbers; }
        public void setSuccessfulHeadNumbers(List<Integer> successfulHeadNumbers) { this.successfulHeadNumbers = successfulHeadNumbers; }
        public List<FailedHeadInfo> getFailedHeadInfos() { return failedHeadInfos; }
        public void setFailedHeadInfos(List<FailedHeadInfo> failedHeadInfos) { this.failedHeadInfos = failedHeadInfos; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
    
    /**
     * 失败治疗头信息
     */
    public static class FailedHeadInfo {
        private int headNumber;
        private String compartmentType;
        private int slotNumber;
        private String reason;
        
        public FailedHeadInfo(int headNumber, String compartmentType, int slotNumber, String reason) {
            this.headNumber = headNumber;
            this.compartmentType = compartmentType;
            this.slotNumber = slotNumber;
            this.reason = reason;
        }
        
        // getters and setters
        public int getHeadNumber() { return headNumber; }
        public void setHeadNumber(int headNumber) { this.headNumber = headNumber; }
        public String getCompartmentType() { return compartmentType; }
        public void setCompartmentType(String compartmentType) { this.compartmentType = compartmentType; }
        public int getSlotNumber() { return slotNumber; }
        public void setSlotNumber(int slotNumber) { this.slotNumber = slotNumber; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
    
    /**
     * 检查治疗头充足性
     */
    public boolean checkTreatmentHeadAvailability(List<Integer> requiredHeadNumbers) {
        try {
            List<TreatmentHeadInfo> availableHeads = hardwareService.syncAllTreatmentHeads();
            
            // 检查每个需要的治疗头是否可用
            for (Integer headNumber : requiredHeadNumbers) {
                boolean found = availableHeads.stream()
                    .anyMatch(head -> head.getHeadNumber() == headNumber && 
                                    "CHARGED".equals(head.getStatus()) && 
                                    !"ABNORMAL".equals(head.getStatus()));
                
                if (!found) {
                    logger.warn("治疗头 {} 不可用", headNumber);
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("检查治疗头充足性失败", e);
            return false;
        }
    }
    
    /**
     * 点亮推荐治疗头指示灯
     */
    public boolean lightUpRecommendedHeads(List<Integer> headNumbers) {
        try {
            // 构建点亮指示灯指令
            String command = commandParser.buildLightUpTreatmentHeadsCommand(headNumbers);
            
            // 发送指令
            String response = hardwareCommunication.sendCommand(command);
            
            // 解析响应
            boolean success = commandParser.parseLightUpTreatmentHeadsResponse(response);
            
            if (success) {
                logger.info("成功点亮推荐治疗头指示灯: {}", headNumbers);
            } else {
                logger.warn("点亮推荐治疗头指示灯失败: {}", headNumbers);
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("点亮推荐治疗头指示灯失败", e);
            return false;
        }
    }
    
    /**
     * 执行远端治疗（仅发送参数，不启动）
     */
    public RemoteTreatmentResponse executeRemoteTreatment(RemoteTreatmentRequest request) {
        logger.info("开始执行远端治疗，治疗头数量: {}", request.getRecommendedHeadNumbers().size());
        
        RemoteTreatmentResponse response = new RemoteTreatmentResponse();
        response.setTotalHeads(request.getRecommendedHeadNumbers().size());
        response.setSuccessfulHeadNumbers(new ArrayList<>());
        response.setFailedHeadInfos(new ArrayList<>());
        
        // 1. 检查治疗头充足性
        if (!checkTreatmentHeadAvailability(request.getRecommendedHeadNumbers())) {
            response.setSuccess(false);
            response.setMessage("治疗头充足性检查失败");
            return response;
        }
        
        // 2. 点亮推荐治疗头指示灯
        if (!lightUpRecommendedHeads(request.getRecommendedHeadNumbers())) {
            response.setSuccess(false);
            response.setMessage("点亮治疗头指示灯失败");
            return response;
        }
        
        // 3. 逐个发送治疗参数（不启动）
        int totalHeads = request.getRecommendedHeadNumbers().size();
        int completedCount = 0;
        int failedCount = 0;
        
        // 发送初始进度（0%）
        sendDownloadProgress(totalHeads, 0, 0, 0, "开始下载治疗参数", new ArrayList<>());
        
        for (Integer headNumber : request.getRecommendedHeadNumbers()) {
            try {
                // 发送当前下载进度
                sendDownloadProgress(totalHeads, completedCount, failedCount, headNumber, 
                                   "正在向治疗头 " + headNumber + " 下载参数...", convertToFailedHeadInfoList(response.getFailedHeadInfos()));
                
                boolean success = sendTreatmentParamsToHead(headNumber, request.getDuration(), 
                                                          request.getIntensity(), request.getFrequency());
                
                if (success) {
                    response.getSuccessfulHeadNumbers().add(headNumber);
                    completedCount++;
                    logger.info("治疗头 {} 参数下载成功", headNumber);
                    
                    // 发送成功进度
                    sendDownloadProgress(totalHeads, completedCount, failedCount, headNumber, 
                                       "治疗头 " + headNumber + " 参数下载成功", convertToFailedHeadInfoList(response.getFailedHeadInfos()));
                } else {
                    addFailedHeadInfo(response, headNumber, "参数下载失败");
                    failedCount++;
                    logger.warn("治疗头 {} 参数下载失败", headNumber);
                    
                    // 发送失败进度
                    sendDownloadProgress(totalHeads, completedCount, failedCount, headNumber, 
                                       "治疗头 " + headNumber + " 参数下载失败", convertToFailedHeadInfoList(response.getFailedHeadInfos()));
                }
                
            } catch (Exception e) {
                addFailedHeadInfo(response, headNumber, "参数下载异常: " + e.getMessage());
                failedCount++;
                logger.error("治疗头 {} 参数下载异常", headNumber, e);
                
                // 发送异常进度
                sendDownloadProgress(totalHeads, completedCount, failedCount, headNumber, 
                                   "治疗头 " + headNumber + " 参数下载异常", convertToFailedHeadInfoList(response.getFailedHeadInfos()));
            }
        }
        
        // 发送最终完成进度
        sendDownloadProgress(totalHeads, completedCount, failedCount, 0, 
                           "所有治疗参数下载完成", convertToFailedHeadInfoList(response.getFailedHeadInfos()));
        
        // 4. 设置响应结果
        response.setSuccessfulHeads(response.getSuccessfulHeadNumbers().size());
        response.setFailedHeads(response.getFailedHeadInfos().size());
        response.setSuccess(response.getSuccessfulHeads() > 0);
        
        if (response.isSuccess()) {
            response.setMessage(String.format("远端治疗参数下载完成，成功: %d，失败: %d", 
                                             response.getSuccessfulHeads(), response.getFailedHeads()));
        } else {
            response.setMessage("远端治疗参数下载失败，所有治疗头都无法下载参数");
        }
        
        logger.info("远端治疗执行完成: 成功 {}, 失败 {}", 
                   response.getSuccessfulHeads(), response.getFailedHeads());
        
        return response;
    }
    
    /**
     * 为单个治疗头发送治疗参数（TWSDT指令）
     */
    private boolean sendTreatmentParamsToHead(int headNumber, int duration, int intensity, int frequency) {
        try {
            // 构建治疗参数请求
            TreatmentParamsRequest paramsRequest = new TreatmentParamsRequest();
            paramsRequest.setDuration(duration);
            paramsRequest.setIntensity(intensity);
            paramsRequest.setFrequency(frequency);
            paramsRequest.setHeadNumbers(List.of(headNumber));
            
            // 构建治疗参数指令（TWSDT）
            String command = commandParser.buildSendTreatmentParamsCommand(paramsRequest);
            
            // 发送指令并等待响应
            CompletableFuture<String> responseFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return hardwareCommunication.sendCommand(command);
                } catch (SerialCommunicationException e) {
                    throw new RuntimeException(e);
                }
            });
            
            // 等待响应，超时时间10秒
            String response = responseFuture.get(COMMAND_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            
            // 解析响应
            return commandParser.validateSendTreatmentParamsResponse(response, paramsRequest);
            
        } catch (Exception e) {
            logger.error("治疗头 {} 参数发送失败", headNumber, e);
            return false;
        }
    }
    
    /**
     * 添加失败治疗头信息
     */
    private void addFailedHeadInfo(RemoteTreatmentResponse response, int headNumber, String reason) {
        try {
            // 获取治疗头的仓位和槽位信息
            List<TreatmentHeadInfo> heads = hardwareService.syncAllTreatmentHeads();
            TreatmentHeadInfo headInfo = heads.stream()
                .filter(head -> head.getHeadNumber() == headNumber)
                .findFirst()
                .orElse(null);
            
            String compartmentType = "UNKNOWN";
            int slotNumber = 0;
            
            if (headInfo != null) {
                compartmentType = headInfo.getCompartmentType();
                slotNumber = headInfo.getSlotNumber();
            }
            
            FailedHeadInfo failedInfo = new FailedHeadInfo(headNumber, compartmentType, slotNumber, reason);
            response.getFailedHeadInfos().add(failedInfo);
            
        } catch (Exception e) {
            logger.error("添加失败治疗头信息时出错", e);
            FailedHeadInfo failedInfo = new FailedHeadInfo(headNumber, "UNKNOWN", 0, reason);
            response.getFailedHeadInfos().add(failedInfo);
        }
    }
    
    /**
     * 发送下载进度通知
     */
    private void sendDownloadProgress(int total, int completed, int failed, int current, String operation, 
                                    List<NotificationWebSocketHandler.FailedHeadInfo> failedHeads) {
        try {
            NotificationWebSocketHandler.DownloadProgressData progressData = new NotificationWebSocketHandler.DownloadProgressData();
            progressData.setTotal(total);
            progressData.setCompleted(completed);
            progressData.setFailed(failed);
            progressData.setCurrent(current);
            progressData.setCurrentOperation(operation);
            progressData.setFailedHeads(failedHeads);
            
            // 计算进度百分比
            if (total > 0) {
                progressData.setProgress((double)(completed + failed) / total * 100.0);
            } else {
                progressData.setProgress(0.0);
            }
            
            notificationHandler.sendDownloadProgressNotification(progressData);
            
        } catch (Exception e) {
            logger.error("发送下载进度通知失败", e);
        }
    }
    
    /**
     * 转换失败治疗头信息格式
     */
    private List<NotificationWebSocketHandler.FailedHeadInfo> convertToFailedHeadInfoList(List<FailedHeadInfo> originalList) {
        List<NotificationWebSocketHandler.FailedHeadInfo> convertedList = new ArrayList<>();
        
        for (FailedHeadInfo original : originalList) {
            NotificationWebSocketHandler.FailedHeadInfo converted = new NotificationWebSocketHandler.FailedHeadInfo();
            converted.setHeadNumber(original.getHeadNumber());
            converted.setCompartmentType(original.getCompartmentType());
            converted.setSlotNumber(original.getSlotNumber());
            converted.setReason(original.getReason());
            convertedList.add(converted);
        }
        
        return convertedList;
    }
}
