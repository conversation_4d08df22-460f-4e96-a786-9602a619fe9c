package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 串口硬件通信适配器
 * 将SerialCommunicationService适配到统一接口
 */
@Service
@ConditionalOnProperty(name = "hardware.communication.mode", havingValue = "serial")
public class SerialHardwareCommunicationAdapter implements HardwareCommunicationInterface {
    
    private static final Logger logger = LoggerFactory.getLogger(SerialHardwareCommunicationAdapter.class);
    
    @Autowired
    private DualLayerSerialCommunicationService dualLayerSerialService;
    
    @Override
    public String sendCommand(String command) throws SerialCommunicationException {
        logger.debug("Sending serial command: {}", command.trim());
        // 对于单个命令，默认发送到上层
        return dualLayerSerialService.sendCommand(command, "upper");
    }
    
    @Override
    public String sendCommand(String command, String layer) throws SerialCommunicationException {
        logger.debug("Sending serial command to {} layer: {}", layer, command.trim());
        return dualLayerSerialService.sendCommand(command, layer);
    }
    
    @Override
    public boolean isConnected() {
        return dualLayerSerialService.isAllConnected();
    }
    
    @Override
    public void connect() throws SerialCommunicationException {
        logger.info("Attempting to connect via dual layer serial communication...");
        // 重新初始化连接
        try {
            dualLayerSerialService.disconnect();
            dualLayerSerialService.initialize();
            logger.info("Dual layer serial communication connected successfully");
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to connect dual layer serial communication", e);
        }
    }
    
    @Override
    public void disconnect() {
        logger.info("Disconnecting dual layer serial communication...");
        dualLayerSerialService.disconnect();
        logger.info("Dual layer serial communication disconnected");
    }
    
    @Override
    public String getCommunicationType() {
        return "DUAL_LAYER_SERIAL\n" + dualLayerSerialService.getConnectionInfo();
    }
} 