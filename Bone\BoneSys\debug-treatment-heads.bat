@echo off
chcp 65001 > nul
echo ========================================
echo Treatment Heads Debug Script
echo ========================================
echo.

echo [1] Testing debug API...
echo Request: GET /api/treatment-parameters/debug-heads
curl -s http://localhost:8080/api/treatment-parameters/debug-heads | jq .
echo.

echo [2] Testing availability check API...
echo Request: POST /api/treatment-parameters/check-availability
curl -s -X POST http://localhost:8080/api/treatment-parameters/check-availability ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"DEBUG001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"debug\",\"color\":\"#FF0000\",\"parameters\":{\"time\":\"15min\",\"intensity\":\"30\",\"frequency\":\"1000\",\"depth\":\"深部\",\"count\":2}}]}" | jq .
echo.

echo [3] Checking database directly...
mysql -u root -p bonesys -e "
SELECT 
    head_number,
    compartment_type,
    realtime_status,
    battery_level,
    CASE 
        WHEN compartment_type = 'DEEP' AND battery_level >= 20 THEN 'AVAILABLE'
        WHEN compartment_type = 'SHALLOW' AND battery_level >= 20 THEN 'AVAILABLE'
        ELSE 'NOT_AVAILABLE'
    END as availability_status
FROM treatment_heads 
WHERE head_number BETWEEN 11 AND 20
ORDER BY head_number;
"
echo.

echo [4] Summary statistics...
mysql -u root -p bonesys -e "
SELECT 
    'Total heads' as category,
    COUNT(*) as count
FROM treatment_heads
UNION ALL
SELECT 
    'SHALLOW heads' as category,
    COUNT(*) as count
FROM treatment_heads 
WHERE compartment_type = 'SHALLOW'
UNION ALL
SELECT 
    'DEEP heads' as category,
    COUNT(*) as count
FROM treatment_heads 
WHERE compartment_type = 'DEEP'
UNION ALL
SELECT 
    'NULL compartment_type' as category,
    COUNT(*) as count
FROM treatment_heads 
WHERE compartment_type IS NULL
UNION ALL
SELECT 
    'DEEP available (battery>=20)' as category,
    COUNT(*) as count
FROM treatment_heads 
WHERE compartment_type = 'DEEP' AND battery_level >= 20;
"
echo.

echo ========================================
echo Debug Complete!
echo ========================================
echo.
echo Check the results above:
echo 1. Debug API should show all treatment heads with compartmentType
echo 2. Availability API should show correct deepAvailable count
echo 3. Database should show DEEP heads with battery_level >= 20
echo 4. Summary should show no NULL compartment_type entries
echo.
pause
