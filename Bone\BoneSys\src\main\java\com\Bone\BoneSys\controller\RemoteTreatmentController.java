package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.RemoteTreatmentService;
import com.Bone.BoneSys.service.RemoteTreatmentService.RemoteTreatmentRequest;
import com.Bone.BoneSys.service.RemoteTreatmentService.RemoteTreatmentResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 远端治疗控制器
 * 提供远端治疗模式的API接口
 */
@RestController
@RequestMapping("/api/treatment/remote")
@CrossOrigin(origins = "*")
public class RemoteTreatmentController {
    
    private static final Logger logger = LoggerFactory.getLogger(RemoteTreatmentController.class);
    
    @Autowired
    private RemoteTreatmentService remoteTreatmentService;
    
    /**
     * 检查治疗头充足性
     * POST /api/treatment/remote/check-availability
     */
    @PostMapping("/check-availability")
    public ApiResponse<Map<String, Object>> checkTreatmentHeadAvailability(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> requiredHeadNumbers = (List<Integer>) request.get("headNumbers");
            
            if (requiredHeadNumbers == null || requiredHeadNumbers.isEmpty()) {
                return ApiResponse.badRequest("治疗头编号列表不能为空");
            }
            
            boolean available = remoteTreatmentService.checkTreatmentHeadAvailability(requiredHeadNumbers);
            
            Map<String, Object> result = new HashMap<>();
            result.put("available", available);
            result.put("requiredHeadNumbers", requiredHeadNumbers);
            result.put("message", available ? "所有治疗头都可用" : "部分治疗头不可用");
            
            return ApiResponse.success("治疗头充足性检查完成", result);
            
        } catch (Exception e) {
            logger.error("检查治疗头充足性失败", e);
            return ApiResponse.error(500, "检查治疗头充足性失败: " + e.getMessage());
        }
    }
    
    /**
     * 点亮推荐治疗头指示灯
     * POST /api/treatment/remote/light-up-heads
     */
    @PostMapping("/light-up-heads")
    public ApiResponse<Map<String, Object>> lightUpRecommendedHeads(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> headNumbers = (List<Integer>) request.get("headNumbers");
            
            if (headNumbers == null || headNumbers.isEmpty()) {
                return ApiResponse.badRequest("治疗头编号列表不能为空");
            }
            
            boolean success = remoteTreatmentService.lightUpRecommendedHeads(headNumbers);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("headNumbers", headNumbers);
            result.put("message", success ? "治疗头指示灯点亮成功" : "治疗头指示灯点亮失败");
            
            return ApiResponse.success("治疗头指示灯操作完成", result);
            
        } catch (Exception e) {
            logger.error("点亮治疗头指示灯失败", e);
            return ApiResponse.error(500, "点亮治疗头指示灯失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行远端治疗（仅下载参数）
     * POST /api/treatment/remote/execute
     */
    @PostMapping("/execute")
    public ApiResponse<RemoteTreatmentResponse> executeRemoteTreatment(@RequestBody RemoteTreatmentRequest request) {
        try {
            // 验证请求参数
            if (request.getRecommendedHeadNumbers() == null || request.getRecommendedHeadNumbers().isEmpty()) {
                return ApiResponse.badRequest("治疗头编号列表不能为空");
            }
            
            if (request.getDuration() <= 0 || request.getDuration() > 60) {
                return ApiResponse.badRequest("治疗时长必须在1-60分钟之间");
            }
            
            if (request.getIntensity() < 1 || request.getIntensity() > 100) {
                return ApiResponse.badRequest("治疗强度必须在1-100之间");
            }
            
            if (request.getFrequency() != 100 && request.getFrequency() != 1000) {
                return ApiResponse.badRequest("频率必须是100Hz或1000Hz");
            }
            
            logger.info("开始执行远端治疗: 治疗头={}, 时长={}分钟, 强度={}, 频率={}Hz", 
                       request.getRecommendedHeadNumbers(), request.getDuration(), 
                       request.getIntensity(), request.getFrequency());
            
            RemoteTreatmentResponse response = remoteTreatmentService.executeRemoteTreatment(request);
            
            if (response.isSuccess()) {
                return ApiResponse.success("远端治疗参数下载成功", response);
            } else {
                return ApiResponse.error(400, response.getMessage(), response);
            }
            
        } catch (Exception e) {
            logger.error("执行远端治疗失败", e);
            return ApiResponse.error(500, "执行远端治疗失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取远端治疗配置信息
     * GET /api/treatment/remote/config
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getRemoteTreatmentConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("maxDuration", 60); // 最大治疗时长（分钟）
            config.put("minDuration", 1);  // 最小治疗时长（分钟）
            config.put("maxIntensity", 100); // 最大治疗强度
            config.put("minIntensity", 1);   // 最小治疗强度
            config.put("supportedFrequencies", List.of(100, 1000)); // 支持的频率
            config.put("commandTimeout", 10); // 指令超时时间（秒）
            config.put("maxTreatmentHeads", 20); // 最大治疗头数量
            config.put("description", "远端治疗模式配置参数（仅下载参数，不启动治疗）");
            
            return ApiResponse.success("远端治疗配置获取成功", config);
            
        } catch (Exception e) {
            logger.error("获取远端治疗配置失败", e);
            return ApiResponse.error(500, "获取远端治疗配置失败: " + e.getMessage());
        }
    }
}
