# FREEBONE医疗设备管理系统 - 系统架构和技术说明

**版本**: 1.0  
**更新时间**: 2025-07-28  
**技术栈**: Spring Boot + MySQL + 串口通信

---

## 🏗️ 系统架构概览

### 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面层     │    │   后端API层     │    │   硬件设备层     │
│                │    │                │    │                │
│  - 治疗头管理    │◄──►│  - REST API     │◄──►│  - 20个治疗头   │
│  - 档案管理      │    │  - 业务逻辑     │    │  - 串口通信     │
│  - 进程管理      │    │  - 数据访问     │    │  - 硬件控制板   │
│  - 参数设置      │    │  - 硬件通信     │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   数据存储层     │
                    │                │
                    │  - MySQL数据库  │
                    │  - 实时数据同步  │
                    │  - 数据持久化   │
                    └─────────────────┘
```

### 核心模块

#### 1. 控制器层 (Controller)
- **功能**: 处理HTTP请求，提供REST API接口
- **主要类**:
  - `HardwareController` - 硬件控制接口
  - `PatientController` - 患者信息管理
  - `RecordController` - 档案管理
  - `ProcessController` - 治疗进程管理
  - `TreatmentHeadSyncController` - 治疗头同步控制

#### 2. 服务层 (Service)
- **功能**: 业务逻辑处理，硬件通信管理
- **主要类**:
  - `HardwareService` - 硬件操作核心服务
  - `SerialCommunicationService` - 串口通信服务
  - `TreatmentHeadSyncService` - 治疗头自动同步
  - `HardwareSimulatorService` - 硬件模拟器
  - `TreatmentHeadRecommendationService` - 治疗头推荐算法

#### 3. 数据访问层 (Repository)
- **功能**: 数据库操作，数据持久化
- **主要接口**:
  - `TreatmentHeadRepository` - 治疗头数据访问
  - `PatientRepository` - 患者数据访问
  - `RecordRepository` - 档案数据访问
  - `ProcessRepository` - 进程数据访问

#### 4. 实体层 (Entity)
- **功能**: 数据模型定义，数据库映射
- **主要实体**:
  - `TreatmentHead` - 治疗头实体
  - `Patient` - 患者实体
  - `Record` - 档案实体
  - `Process` - 治疗进程实体

---

## 🔧 技术栈详解

### 后端技术

#### Spring Boot 3.x
- **Web框架**: Spring MVC
- **数据访问**: Spring Data JPA
- **安全框架**: Spring Security
- **配置管理**: Spring Boot Configuration
- **定时任务**: Spring Scheduling

#### 数据库技术
- **关系数据库**: MySQL 8.0+
- **ORM框架**: Hibernate
- **连接池**: HikariCP
- **数据迁移**: 自定义SQL脚本

#### 硬件通信
- **串口通信**: jSerialComm库
- **通信协议**: 自定义二进制协议
- **连接管理**: 自动重连和健康检查
- **模拟器**: 完整的硬件行为模拟

### 开发工具
- **构建工具**: Gradle
- **IDE支持**: IntelliJ IDEA, Eclipse
- **版本控制**: Git
- **文档工具**: Markdown

---

## 💾 数据库设计

### 核心数据表

#### 1. 治疗头表 (treatment_heads)
```sql
CREATE TABLE treatment_heads (
    head_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    head_number INT UNIQUE NOT NULL,           -- 治疗头编号(1-20)
    slot_number INT NOT NULL,                  -- 槽位编号
    light_color INT DEFAULT 0,                 -- 指示灯颜色
    realtime_status VARCHAR(20) DEFAULT 'CHARGING', -- 实时状态
    battery_level INT DEFAULT 100,             -- 电池电量
    total_usage_count INT DEFAULT 0,           -- 总使用次数
    total_usage_minutes INT DEFAULT 0,         -- 总使用时长
    max_usage_count INT DEFAULT 500            -- 最大使用次数
);
```

#### 2. 患者表 (patients)
```sql
CREATE TABLE patients (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    patient_card_id VARCHAR(50) UNIQUE NOT NULL, -- 就诊卡号
    name VARCHAR(100) NOT NULL,                   -- 患者姓名
    gender VARCHAR(10),                           -- 性别
    age VARCHAR(10),                              -- 年龄
    contact_info TEXT,                            -- 联系方式
    created_at DATETIME NOT NULL                  -- 创建时间
);
```

#### 3. 档案表 (records)
```sql
CREATE TABLE records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    record_number VARCHAR(50) UNIQUE NOT NULL,   -- 档案编号
    patient_id BIGINT NOT NULL,                  -- 患者ID
    diagnosis_description TEXT,                   -- 诊断描述
    sessions_completed_count INT DEFAULT 0,      -- 完成治疗次数
    created_at DATE NOT NULL,                    -- 创建日期
    FOREIGN KEY (patient_id) REFERENCES patients(id)
);
```

#### 4. 治疗进程表 (processes)
```sql
CREATE TABLE processes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    record_id BIGINT NOT NULL,                   -- 档案ID
    treatment_mode VARCHAR(20) NOT NULL,         -- 治疗模式
    status VARCHAR(20) DEFAULT 'IN_PROGRESS',    -- 进程状态
    start_time DATETIME,                         -- 开始时间
    end_time DATETIME,                           -- 结束时间
    FOREIGN KEY (record_id) REFERENCES records(id)
);
```

### 数据关系图

```
patients (1) ──── (N) records (1) ──── (N) processes (1) ──── (N) treatment_details
    │                                                                      │
    └─── (N) body_part_stats                                              │
                                                                          │
treatment_heads ──────────────────────────────────────────────────────────┘
```

---

## 🔄 硬件通信架构

### 通信层次结构

```
应用层 (Application Layer)
    │
    ├── HardwareService (硬件服务)
    │
业务层 (Business Layer)
    │
    ├── HardwareCommandParser (指令解析器)
    │
通信层 (Communication Layer)
    │
    ├── SerialCommunicationService (串口通信)
    │
    ├── HardwareSimulatorService (硬件模拟器)
    │
硬件层 (Hardware Layer)
    │
    └── 真实硬件设备 / 模拟器
```

### 通信协议

#### 指令格式
```
指令结构: [指令头][参数][终止符]
终止符: \r\n
编码: UTF-8
```

#### 主要指令集
| 指令 | 功能 | 请求格式 | 响应格式 |
|------|------|----------|----------|
| TRZI | 查询治疗头状态 | `TRZI\r\n` | `TRZI[数量][状态数据]\r\n` |
| TWSC | 设置指示灯 | `TWSC[数量][头号颜色...]\r\n` | `TWSC[数量][头号颜色...]\r\n` |
| TWS | 开始治疗 | `TWS[头号][时间][强度][频率]\r\n` | `TWS[头号][时间][强度][频率]\r\n` |
| TWZO | 停止治疗 | `TWZO[头号]\r\n` | `TWZO[头号]\r\n` |

### 硬件模拟器

#### 模拟功能
- ✅ **完整协议支持**: 所有硬件指令的完整模拟
- ✅ **状态管理**: 20个治疗头的独立状态管理
- ✅ **实时变化**: 电量、使用次数的动态变化
- ✅ **错误模拟**: 各种硬件异常情况的模拟

#### 模拟数据结构
```java
public class SimulatedTreatmentHead {
    private int headNumber;           // 治疗头编号
    private int batteryLevel;         // 电池电量 (0-100)
    private int usageCount;           // 使用次数
    private String status;            // 状态 (CHARGING/TREATING)
    private boolean isWorking;        // 是否工作中
    private int lightColor;           // 指示灯颜色
}
```

---

## ⚡ 实时同步机制

### 自动同步架构

```
定时器 (Scheduler)
    │
    ├── TreatmentHeadSyncService (同步服务)
    │   │
    │   ├── 查询硬件状态 (TRZI指令)
    │   ├── 解析响应数据
    │   ├── 更新数据库
    │   └── 性能监控
    │
    └── 数据库 (MySQL)
```

### 同步流程

1. **定时触发**: 每10秒自动执行一次同步
2. **硬件查询**: 发送TRZI指令查询所有治疗头状态
3. **数据解析**: 解析硬件返回的状态数据
4. **数据库更新**: 批量更新治疗头状态到数据库
5. **错误处理**: 自动重试和错误恢复
6. **性能监控**: 记录同步性能指标

### 同步配置
```properties
# 同步间隔 (毫秒)
treatment-head.sync.interval=10000

# 启动延迟 (毫秒)
treatment-head.sync.startup-delay=5000

# 超时时间 (毫秒)
treatment-head.sync.timeout=5000

# 重试配置
treatment-head.sync.retry-attempts=3
treatment-head.sync.retry-delay=2000
```

---

## 🔐 安全架构

### 认证和授权

#### JWT认证
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    // JWT配置
    // 认证过滤器
    // 权限控制
}
```

#### 跨域配置
```java
@Configuration
public class CorsConfig {
    // CORS策略配置
    // 允许的来源和方法
}
```

### 数据安全
- **SQL注入防护**: 使用JPA参数化查询
- **XSS防护**: 输入数据验证和转义
- **CSRF防护**: Spring Security CSRF保护
- **数据加密**: 敏感数据加密存储

---

## 📊 性能优化

### 数据库优化

#### 索引策略
```sql
-- 治疗头编号索引
CREATE INDEX idx_head_number ON treatment_heads(head_number);

-- 患者卡号索引
CREATE UNIQUE INDEX idx_patient_card ON patients(patient_card_id);

-- 档案编号索引
CREATE UNIQUE INDEX idx_record_number ON records(record_number);
```

#### 查询优化
- **分页查询**: 使用Spring Data分页功能
- **懒加载**: JPA关联数据懒加载
- **批量操作**: 批量插入和更新操作
- **连接池**: HikariCP高性能连接池

### 硬件通信优化

#### 连接管理
- **连接池**: 串口连接复用
- **健康检查**: 定期检查连接状态
- **自动重连**: 连接断开自动恢复
- **超时控制**: 合理的超时时间设置

#### 并发处理
- **异步通信**: CompletableFuture异步处理
- **线程安全**: 同步块保护共享资源
- **队列管理**: 指令队列避免冲突

---

## 🧪 测试架构

### 测试层次

#### 单元测试
```java
@SpringBootTest
class HardwareServiceTest {
    // 硬件服务单元测试
}
```

#### 集成测试
```java
@SpringBootTest
@AutoConfigureTestDatabase
class SystemIntegrationTest {
    // 系统集成测试
}
```

#### 硬件模拟测试
```java
@TestConfiguration
class HardwareSimulatorTestConfig {
    // 硬件模拟器测试配置
}
```

### 测试工具
- **JUnit 5**: 单元测试框架
- **Mockito**: Mock对象框架
- **TestContainers**: 数据库测试容器
- **Spring Boot Test**: 集成测试支持

---

## 📈 监控和日志

### 日志架构

#### 日志级别
- **DEBUG**: 详细调试信息
- **INFO**: 一般信息记录
- **WARN**: 警告信息
- **ERROR**: 错误信息

#### 日志分类
```java
// 硬件通信日志
private static final Logger hardwareLogger = 
    LoggerFactory.getLogger("HARDWARE");

// 同步服务日志
private static final Logger syncLogger = 
    LoggerFactory.getLogger("SYNC");

// 业务逻辑日志
private static final Logger businessLogger = 
    LoggerFactory.getLogger("BUSINESS");
```

### 性能监控

#### 关键指标
- **同步成功率**: 治疗头同步成功的百分比
- **响应时间**: API接口平均响应时间
- **硬件连接状态**: 串口连接的稳定性
- **数据库性能**: 查询执行时间

#### 监控工具
- **Spring Boot Actuator**: 应用健康检查
- **自定义指标**: 业务相关性能指标
- **日志分析**: 错误和性能日志分析

---

## 🚀 部署架构

### 部署模式

#### 单机部署
```
┌─────────────────────────────────┐
│         应用服务器               │
│  ┌─────────────┐ ┌─────────────┐ │
│  │ Spring Boot │ │   MySQL     │ │
│  │ Application │ │  Database   │ │
│  └─────────────┘ └─────────────┘ │
│         │                       │
│  ┌─────────────┐                │
│  │ 硬件设备     │                │
│  │ (串口连接)   │                │
│  └─────────────┘                │
└─────────────────────────────────┘
```

#### 分布式部署
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  前端服务器  │    │  应用服务器  │    │  数据库服务器│
│             │    │             │    │             │
│   Nginx     │◄──►│Spring Boot  │◄──►│   MySQL     │
│   静态资源   │    │   API       │    │   数据存储   │
└─────────────┘    └─────────────┘    └─────────────┘
                          │
                   ┌─────────────┐
                   │  硬件设备    │
                   │  (串口连接)  │
                   └─────────────┘
```

### 容器化部署

#### Docker配置
```dockerfile
FROM openjdk:17-jdk-slim
VOLUME /tmp
COPY build/libs/BoneSys-1.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      - SPRING_PROFILES_ACTIVE=prod
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: bonesys
      MYSQL_ROOT_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

---

## 🔮 扩展性设计

### 水平扩展
- **无状态设计**: 应用服务无状态，支持多实例部署
- **数据库分离**: 数据库独立部署，支持读写分离
- **负载均衡**: 支持多个应用实例的负载均衡

### 功能扩展
- **插件架构**: 支持功能模块的插件化扩展
- **API版本控制**: 支持API版本管理和向后兼容
- **配置外部化**: 支持外部配置管理

### 硬件扩展
- **多设备支持**: 支持不同类型的硬件设备
- **协议扩展**: 支持新的通信协议
- **设备管理**: 支持设备的动态添加和移除

---

**系统架构设计完整，技术栈成熟稳定，具备良好的扩展性和维护性！**