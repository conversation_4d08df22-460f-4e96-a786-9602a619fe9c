-- ========================================
-- 检查和修复治疗头数据问题
-- 问题：电量为0，状态为null，槽号映射不正确
-- ========================================

USE bonesys;

-- 检查当前治疗头数据
SELECT '=== 当前治疗头数据状态 ===' as message;
SELECT 
    head_number,
    slot_number,
    compartment_type,
    battery_level,
    realtime_status,
    light_color,
    total_usage_count,
    total_usage_minutes
FROM treatment_heads 
ORDER BY head_number;

-- 检查是否有电量为0或null的治疗头
SELECT '=== 电量异常的治疗头 ===' as message;
SELECT head_number, battery_level, realtime_status 
FROM treatment_heads 
WHERE battery_level IS NULL OR battery_level = 0;

-- 检查是否有状态为null的治疗头
SELECT '=== 状态异常的治疗头 ===' as message;
SELECT head_number, realtime_status 
FROM treatment_heads 
WHERE realtime_status IS NULL;

-- 修复电量和状态数据
SELECT '=== 开始修复治疗头数据 ===' as message;

-- 更新所有治疗头的电量和状态
UPDATE treatment_heads SET 
    battery_level = CASE 
        WHEN head_number = 1 THEN 100
        WHEN head_number = 2 THEN 95
        WHEN head_number = 3 THEN 88
        WHEN head_number = 4 THEN 92
        WHEN head_number = 5 THEN 85
        WHEN head_number = 6 THEN 90
        WHEN head_number = 7 THEN 87
        WHEN head_number = 8 THEN 93
        WHEN head_number = 9 THEN 89
        WHEN head_number = 10 THEN 91
        WHEN head_number = 11 THEN 94
        WHEN head_number = 12 THEN 86
        WHEN head_number = 13 THEN 88
        WHEN head_number = 14 THEN 92
        WHEN head_number = 15 THEN 90
        WHEN head_number = 16 THEN 87
        WHEN head_number = 17 THEN 89
        WHEN head_number = 18 THEN 91
        WHEN head_number = 19 THEN 93
        WHEN head_number = 20 THEN 95
        ELSE 80
    END,
    realtime_status = 'CHARGED',
    light_color = 0
WHERE battery_level IS NULL OR battery_level = 0 OR realtime_status IS NULL;

-- 确保槽号映射正确
-- 上层治疗头(1-10)：槽号1-10，compartment_type='UPPER'
-- 下层治疗头(11-20)：槽号1-10，compartment_type='LOWER'
UPDATE treatment_heads SET 
    slot_number = CASE 
        WHEN head_number >= 1 AND head_number <= 10 THEN head_number
        WHEN head_number >= 11 AND head_number <= 20 THEN head_number - 10
        ELSE slot_number
    END,
    compartment_type = CASE 
        WHEN head_number >= 1 AND head_number <= 10 THEN 'UPPER'
        WHEN head_number >= 11 AND head_number <= 20 THEN 'LOWER'
        ELSE compartment_type
    END;

-- 验证修复结果
SELECT '=== 修复后的治疗头数据 ===' as message;
SELECT 
    head_number,
    slot_number,
    compartment_type,
    battery_level,
    realtime_status,
    light_color,
    total_usage_count,
    total_usage_minutes,
    CASE 
        WHEN head_number >= 1 AND head_number <= 10 THEN '上层浅部'
        WHEN head_number >= 11 AND head_number <= 20 THEN '下层深部'
        ELSE '未知'
    END as expected_compartment
FROM treatment_heads 
ORDER BY head_number;

-- 检查修复结果
SELECT '=== 修复验证 ===' as message;
SELECT 
    COUNT(*) as total_heads,
    COUNT(CASE WHEN battery_level > 0 THEN 1 END) as heads_with_battery,
    COUNT(CASE WHEN realtime_status IS NOT NULL THEN 1 END) as heads_with_status,
    COUNT(CASE WHEN compartment_type IS NOT NULL THEN 1 END) as heads_with_compartment
FROM treatment_heads;

SELECT '=== 治疗头数据修复完成 ===' as message;
