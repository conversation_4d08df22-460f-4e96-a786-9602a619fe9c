{"name": "bone-vue", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.10.0", "pinia": "^2.0.26", "tdesign-vue-next": "^1.3.0", "vue": "^3.2.45", "vue-router": "^4.1.6"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@tsconfig/node18": "^18.2.4", "@types/node": "^18.11.12", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "npm-run-all": "^4.1.5", "typescript": "~4.7.4", "vite": "^4.0.0", "vue-tsc": "^1.0.12"}}