# 🚦 指示灯控制逻辑测试

## 测试目标

验证指示灯在不同场景下的正确控制行为，确保符合用户需求：
- ✅ 治疗头推荐时点亮指示灯
- ✅ 弹窗关闭时关闭指示灯
- ✅ 治疗进程启动后指示灯保持点亮
- ❌ 启动治疗进程成功后不发送关闭指示灯指令

## 测试场景

### 场景1：治疗头推荐时点亮指示灯 ✅
**触发条件：** 用户点击"生成推荐治疗头"按钮
**预期行为：**
1. 后端发送TRZI指令查询治疗头信息
2. 生成推荐治疗头配置
3. 后端发送TWSC指令点亮推荐治疗头指示灯
4. 前端显示治疗头选择弹窗

**验证方法：**
```bash
# 运行场景1测试
./gradlew test --tests IndicatorLightControlTest.testLightUpOnRecommendation
```

### 场景2：治疗头选择弹窗关闭时关闭指示灯 ✅
**触发条件：** 用户关闭治疗头选择弹窗（不确认选择）
**预期行为：**
1. 前端调用`closeTreatmentHeadModal()`函数
2. 发送TWSN指令关闭推荐治疗头指示灯
3. 弹窗关闭

**验证方法：**
```bash
# 运行场景2测试
./gradlew test --tests IndicatorLightControlTest.testTurnOffOnModalClose
```

### 场景3：治疗头数量不足弹窗关闭时关闭指示灯 ✅
**触发条件：** 治疗头数量不足，用户关闭提示弹窗
**预期行为：**
1. 前端调用`closeTreatmentHeadShortageModal()`函数
2. 发送TWSN指令关闭推荐治疗头指示灯
3. 弹窗关闭

**验证方法：**
```bash
# 运行场景3测试
./gradlew test --tests IndicatorLightControlTest.testTurnOffOnShortageModalClose
```

### 场景4：参数下载弹窗取消时关闭指示灯 ✅
**触发条件：** 用户取消参数下载或关闭下载弹窗
**预期行为：**
1. 前端调用`handleParameterDownloadCancel()`或`closeParameterDownloadingModal()`函数
2. 发送TWSN指令关闭推荐治疗头指示灯
3. 弹窗关闭

**验证方法：**
```bash
# 运行场景4测试
./gradlew test --tests IndicatorLightControlTest.testTurnOffOnDownloadCancel
```

### 场景5：治疗进程启动成功后指示灯保持点亮 ✅
**触发条件：** 用户确认治疗头选择，治疗进程启动成功
**预期行为：**
1. 后端发送TWZS（本地治疗）或TWSDT（远端治疗）指令
2. 治疗进程创建成功
3. 页面跳转到治疗进程页面
4. **重要：不发送TWSN指令关闭指示灯**
5. 指示灯保持点亮状态，显示治疗状态

**验证方法：**
```bash
# 运行场景5测试
./gradlew test --tests IndicatorLightControlTest.testKeepLightOnAfterTreatmentStart
```

## 完整流程测试

### 运行所有测试场景
```bash
# 运行完整的指示灯控制测试套件
./gradlew test --tests IndicatorLightControlTest

# 或者运行完整场景测试
./gradlew test --tests IndicatorLightControlTest.testCompleteIndicatorLightFlow
```

## 代码修改验证

### 已修改的关键函数

#### 1. `closeTreatmentHeadModal()` - 治疗头选择弹窗关闭
```javascript
const closeTreatmentHeadModal = async () => {
  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('用户取消选择，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
    }
  }
  
  showTreatmentHeadModal.value = false;
};
```

#### 2. `closeTreatmentHeadShortageModal()` - 治疗头数量不足弹窗关闭
```javascript
const closeTreatmentHeadShortageModal = async () => {
  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('治疗头数量不足弹窗关闭，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
    }
  }
  
  showTreatmentHeadShortageModal.value = false;
};
```

#### 3. `closeParameterDownloadingModal()` - 参数下载弹窗关闭
```javascript
const closeParameterDownloadingModal = async () => {
  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('参数下载弹窗关闭，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
    }
  }
  
  showParameterDownloadingModal.value = false;
};
```

#### 4. `handleParameterDownloadCancel()` - 参数下载取消
```javascript
const handleParameterDownloadCancel = async () => {
  console.log('用户取消了参数下载');
  
  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('参数下载取消，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
    }
  }
  
  MessagePlugin.info('已取消参数下载');
  closeParameterDownloadingModal();
};
```

#### 5. `confirmTreatmentHeadSelection()` - 治疗进程启动成功
```javascript
// 治疗进程启动成功后的处理
console.log('治疗进程启动成功:', response.data);

// 注意：启动治疗进程成功后无需发送关闭指示灯指令
// 指示灯会在治疗过程中保持点亮状态，直到治疗完成

MessagePlugin.success(`治疗进程启动成功，进程ID: ${processId}`);
showTreatmentHeadModal.value = false;
```

## 测试结果验证

### 预期测试结果
- [ ] **场景1测试通过** - 推荐时正确点亮指示灯
- [ ] **场景2测试通过** - 选择弹窗关闭时正确关闭指示灯
- [ ] **场景3测试通过** - 数量不足弹窗关闭时正确关闭指示灯
- [ ] **场景4测试通过** - 参数下载弹窗关闭时正确关闭指示灯
- [ ] **场景5测试通过** - 治疗启动后指示灯保持点亮
- [ ] **完整流程测试通过** - 整个流程的指示灯控制逻辑正确

### 关键验证点
1. **TWSC指令发送** - 推荐时正确发送点亮指令
2. **TWSN指令发送** - 弹窗关闭时正确发送关闭指令
3. **指令不发送** - 治疗启动成功后不发送关闭指令
4. **状态保持** - 治疗过程中指示灯保持点亮状态

## 故障排除

### 常见问题
1. **指示灯不点亮**
   - 检查TWSC指令格式
   - 验证治疗头编号和颜色代码
   - 确认WebSocket连接正常

2. **指示灯不关闭**
   - 检查TWSN指令格式
   - 验证治疗头编号列表
   - 确认函数调用逻辑

3. **治疗启动后指示灯意外关闭**
   - 检查`confirmTreatmentHeadSelection()`函数
   - 确认没有调用`turnOffTreatmentHeadLights()`
   - 验证页面跳转逻辑

## 测试完成确认

完成所有测试后，请确认：
- [ ] 所有测试用例通过
- [ ] 指示灯控制逻辑符合需求
- [ ] 前端代码修改正确
- [ ] 硬件指令发送正常
- [ ] 用户体验流畅

**测试负责人：** `________________`
**测试完成时间：** `____年__月__日 __:__`
