package com.Bone.BoneSys;

import com.Bone.BoneSys.service.SettingsService;
import com.Bone.BoneSys.service.SettingsService.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SettingsService单元测试
 * 测试系统设置服务的功能完整性
 */
@SpringBootTest
@TestPropertySource(properties = {
    "treatment.default.duration=20",
    "treatment.default.intensity=45",
    "treatment.default.frequency=1000",
    "treatment.max-usage-count=500",
    "treatment.low-battery-threshold=20",
    "serial.port.name=COM1",
    "serial.port.baud-rate=115200",
    "serial.port.timeout=5000",
    "hardware.simulator.enabled=true"
})
public class SettingsServiceTest {

    @Autowired
    private SettingsService settingsService;

    @Test
    void testGetSystemParameters_Success() {
        System.out.println("🧪 测试获取系统参数 - 成功场景");
        
        SystemParameters params = settingsService.getSystemParameters();
        
        // 验证基本系统信息
        assertNotNull(params, "系统参数不应为空");
        assertEquals("FREEBONE医疗系统", params.getSystemName(), "系统名称应正确");
        assertEquals("1.0.0", params.getSystemVersion(), "系统版本应正确");
        assertNotNull(params.getEnvironment(), "环境信息不应为空");
        
        System.out.println("✅ 基本系统信息验证通过");
        System.out.println("   系统名称: " + params.getSystemName());
        System.out.println("   系统版本: " + params.getSystemVersion());
        System.out.println("   运行环境: " + params.getEnvironment());
    }

    @Test
    void testGetSystemParameters_TreatmentParameters() {
        System.out.println("🧪 测试治疗参数配置");
        
        SystemParameters params = settingsService.getSystemParameters();
        TreatmentParameters treatmentParams = params.getTreatmentParameters();
        
        // 验证治疗参数
        assertNotNull(treatmentParams, "治疗参数不应为空");
        assertEquals(20, treatmentParams.getDefaultDuration(), "默认治疗时长应为20分钟");
        assertEquals(45, treatmentParams.getDefaultIntensity(), "默认治疗强度应为45档位");
        assertEquals(1000, treatmentParams.getDefaultFrequency(), "默认治疗频率应为1000Hz");
        assertEquals(500, treatmentParams.getMaxUsageCount(), "最大使用次数应为500");
        assertEquals(20, treatmentParams.getLowBatteryThreshold(), "低电量阈值应为20%");
        
        // 验证强度档位选项
        assertNotNull(treatmentParams.getIntensityOptions(), "强度档位选项不应为空");
        assertArrayEquals(new int[]{30, 45, 60}, treatmentParams.getIntensityOptions(), 
                         "强度档位应为30、45、60");
        
        // 验证频率选项
        assertNotNull(treatmentParams.getFrequencyOptions(), "频率选项不应为空");
        assertArrayEquals(new int[]{100, 1000}, treatmentParams.getFrequencyOptions(), 
                         "频率选项应为100Hz、1000Hz");
        
        // 验证时长范围
        assertNotNull(treatmentParams.getDurationRange(), "时长范围不应为空");
        assertEquals(5, treatmentParams.getDurationRange().getMin(), "最小时长应为5分钟");
        assertEquals(60, treatmentParams.getDurationRange().getMax(), "最大时长应为60分钟");
        
        System.out.println("✅ 治疗参数配置验证通过");
        System.out.println("   默认时长: " + treatmentParams.getDefaultDuration() + "分钟");
        System.out.println("   默认强度: " + treatmentParams.getDefaultIntensity() + "档位");
        System.out.println("   默认频率: " + treatmentParams.getDefaultFrequency() + "Hz");
    }

    @Test
    void testGetSystemParameters_DeviceConfiguration() {
        System.out.println("🧪 测试设备配置");
        
        SystemParameters params = settingsService.getSystemParameters();
        DeviceConfiguration deviceConfig = params.getDeviceConfiguration();
        
        // 验证设备配置
        assertNotNull(deviceConfig, "设备配置不应为空");
        assertEquals(20, deviceConfig.getTotalTreatmentHeads(), "治疗头总数应为20");
        
        // 验证浅部治疗头范围
        assertNotNull(deviceConfig.getShallowCompartmentRange(), "浅部治疗头范围不应为空");
        assertEquals(1, deviceConfig.getShallowCompartmentRange().getStart(), "浅部治疗头起始编号应为1");
        assertEquals(10, deviceConfig.getShallowCompartmentRange().getEnd(), "浅部治疗头结束编号应为10");
        
        // 验证深部治疗头范围
        assertNotNull(deviceConfig.getDeepCompartmentRange(), "深部治疗头范围不应为空");
        assertEquals(11, deviceConfig.getDeepCompartmentRange().getStart(), "深部治疗头起始编号应为11");
        assertEquals(20, deviceConfig.getDeepCompartmentRange().getEnd(), "深部治疗头结束编号应为20");
        
        // 验证指示灯颜色配置
        assertNotNull(deviceConfig.getLightColors(), "指示灯颜色配置不应为空");
        assertEquals("橙色", deviceConfig.getLightColors().get(1), "第1个颜色应为橙色");
        assertEquals("蓝色", deviceConfig.getLightColors().get(2), "第2个颜色应为蓝色");
        assertEquals("绿色", deviceConfig.getLightColors().get(3), "第3个颜色应为绿色");
        
        System.out.println("✅ 设备配置验证通过");
        System.out.println("   治疗头总数: " + deviceConfig.getTotalTreatmentHeads());
        System.out.println("   浅部范围: " + deviceConfig.getShallowCompartmentRange().getStart() + 
                          "-" + deviceConfig.getShallowCompartmentRange().getEnd());
        System.out.println("   深部范围: " + deviceConfig.getDeepCompartmentRange().getStart() + 
                          "-" + deviceConfig.getDeepCompartmentRange().getEnd());
    }

    @Test
    void testGetSystemParameters_SerialConfiguration() {
        System.out.println("🧪 测试串口配置");
        
        SystemParameters params = settingsService.getSystemParameters();
        SerialConfiguration serialConfig = params.getSerialConfiguration();
        
        // 验证串口配置
        assertNotNull(serialConfig, "串口配置不应为空");
        assertEquals("COM1", serialConfig.getPortName(), "端口名称应为COM1");
        assertEquals(115200, serialConfig.getBaudRate(), "波特率应为115200");
        assertEquals(5000, serialConfig.getTimeout(), "超时时间应为5000ms");
        assertEquals(5, serialConfig.getMaxRetryAttempts(), "最大重试次数应为5");
        assertEquals(2000L, serialConfig.getRetryDelay(), "重试延迟应为2000ms");
        
        System.out.println("✅ 串口配置验证通过");
        System.out.println("   端口名称: " + serialConfig.getPortName());
        System.out.println("   波特率: " + serialConfig.getBaudRate());
        System.out.println("   超时时间: " + serialConfig.getTimeout() + "ms");
    }

    @Test
    void testGetSystemParameters_HardwareConfiguration() {
        System.out.println("🧪 测试硬件配置");
        
        SystemParameters params = settingsService.getSystemParameters();
        HardwareConfiguration hardwareConfig = params.getHardwareConfiguration();
        
        // 验证硬件配置
        assertNotNull(hardwareConfig, "硬件配置不应为空");
        assertTrue(hardwareConfig.getSimulatorEnabled(), "模拟器应启用");
        assertEquals(60000L, hardwareConfig.getMetricsReportInterval(), "性能报告间隔应为60000ms");
        
        System.out.println("✅ 硬件配置验证通过");
        System.out.println("   模拟器启用: " + hardwareConfig.getSimulatorEnabled());
        System.out.println("   报告间隔: " + hardwareConfig.getMetricsReportInterval() + "ms");
    }

    @Test
    void testUpdateSystemParameters_TreatmentParameters() {
        System.out.println("🧪 测试更新治疗参数");
        
        // 创建更新请求
        SystemParametersUpdateRequest request = new SystemParametersUpdateRequest();
        TreatmentParameters treatmentParams = new TreatmentParameters();
        treatmentParams.setDefaultDuration(25);
        treatmentParams.setDefaultIntensity(60);
        treatmentParams.setDefaultFrequency(100);
        request.setTreatmentParameters(treatmentParams);
        
        // 执行更新（这里只测试不抛异常）
        assertDoesNotThrow(() -> settingsService.updateSystemParameters(request), 
                          "更新治疗参数不应抛出异常");
        
        System.out.println("✅ 治疗参数更新测试通过");
    }

    @Test
    void testUpdateSystemParameters_ValidationError() {
        System.out.println("🧪 测试参数验证错误");
        
        // 创建无效的更新请求
        SystemParametersUpdateRequest request = new SystemParametersUpdateRequest();
        TreatmentParameters treatmentParams = new TreatmentParameters();
        treatmentParams.setDefaultDuration(100); // 超出范围
        treatmentParams.setDefaultIntensity(80); // 无效档位
        request.setTreatmentParameters(treatmentParams);
        
        // 验证参数验证
        assertThrows(IllegalArgumentException.class, 
                    () -> settingsService.updateSystemParameters(request),
                    "无效参数应抛出IllegalArgumentException");
        
        System.out.println("✅ 参数验证错误测试通过");
    }

    @Test
    void testGetDeviceConfiguration() {
        System.out.println("🧪 测试获取设备配置信息");
        
        DeviceConfigurationInfo config = settingsService.getDeviceConfiguration();
        
        // 验证设备配置信息
        assertNotNull(config, "设备配置信息不应为空");
        assertEquals(20, config.getTotalTreatmentHeads(), "治疗头总数应为20");
        
        // 验证仓位配置
        assertNotNull(config.getCompartmentConfiguration(), "仓位配置不应为空");
        assertTrue(config.getCompartmentConfiguration().containsKey("shallow"), "应包含浅部配置");
        assertTrue(config.getCompartmentConfiguration().containsKey("deep"), "应包含深部配置");
        
        // 验证指示灯颜色配置
        assertNotNull(config.getLightColorConfiguration(), "指示灯颜色配置不应为空");
        assertEquals(3, config.getLightColorConfiguration().size(), "应有3种颜色配置");
        
        // 验证治疗模式
        assertNotNull(config.getTreatmentModes(), "治疗模式不应为空");
        assertTrue(config.getTreatmentModes().containsKey("ON_SITE"), "应包含现场治疗模式");
        assertTrue(config.getTreatmentModes().containsKey("TAKE_AWAY"), "应包含取走治疗模式");
        
        System.out.println("✅ 设备配置信息验证通过");
        System.out.println("   治疗头总数: " + config.getTotalTreatmentHeads());
        System.out.println("   颜色配置数: " + config.getLightColorConfiguration().size());
        System.out.println("   治疗模式数: " + config.getTreatmentModes().size());
    }

    @Test
    void testResetToDefaults() {
        System.out.println("🧪 测试重置为默认值");
        
        // 执行重置（这里只测试不抛异常）
        assertDoesNotThrow(() -> settingsService.resetToDefaults(), 
                          "重置为默认值不应抛出异常");
        
        System.out.println("✅ 重置为默认值测试通过");
    }

    @Test
    void testIntensityValidation() {
        System.out.println("🧪 测试强度档位验证");
        
        // 测试有效强度档位
        int[] validIntensities = {30, 45, 60};
        for (int intensity : validIntensities) {
            SystemParametersUpdateRequest request = new SystemParametersUpdateRequest();
            TreatmentParameters treatmentParams = new TreatmentParameters();
            treatmentParams.setDefaultIntensity(intensity);
            request.setTreatmentParameters(treatmentParams);
            
            assertDoesNotThrow(() -> settingsService.updateSystemParameters(request),
                              "强度档位" + intensity + "应该有效");
        }
        
        // 测试无效强度档位
        int[] invalidIntensities = {20, 40, 50, 70, 100};
        for (int intensity : invalidIntensities) {
            SystemParametersUpdateRequest request = new SystemParametersUpdateRequest();
            TreatmentParameters treatmentParams = new TreatmentParameters();
            treatmentParams.setDefaultIntensity(intensity);
            request.setTreatmentParameters(treatmentParams);
            
            assertThrows(IllegalArgumentException.class,
                        () -> settingsService.updateSystemParameters(request),
                        "强度档位" + intensity + "应该无效");
        }
        
        System.out.println("✅ 强度档位验证测试通过");
    }
}
