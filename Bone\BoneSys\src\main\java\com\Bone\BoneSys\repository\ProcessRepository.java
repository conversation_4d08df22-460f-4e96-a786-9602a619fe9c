package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 治疗进程数据访问接口
 */
@Repository
public interface ProcessRepository extends JpaRepository<Process, Long> {
    
    /**
     * 根据档案ID查找进程
     */
    List<Process> findByRecordId(Long recordId);
    
    /**
     * 根据状态查找进程
     */
    List<Process> findByStatus(ProcessStatus status);
    
    /**
     * 根据治疗模式查找进程
     */
    List<Process> findByTreatmentMode(TreatmentMode treatmentMode);
    
    /**
     * 查找正在进行的进程
     */
    List<Process> findByStatusOrderByStartTimeDesc(ProcessStatus status);
    
    /**
     * 根据患者信息查询进程
     */
    @Query("SELECT p FROM Process p JOIN p.record r JOIN r.patient pt WHERE " +
           "(:patientCardId IS NULL OR pt.patientCardId LIKE %:patientCardId%) AND " +
           "(:patientName IS NULL OR pt.name LIKE %:patientName%) AND " +
           "(:status IS NULL OR p.status = :status)")
    Page<Process> findByPatientInfoAndStatus(
            @Param("patientCardId") String patientCardId,
            @Param("patientName") String patientName,
            @Param("status") ProcessStatus status,
            Pageable pageable);
    
    /**
     * 根据时间范围查询进程
     */
    Page<Process> findByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 查找超时的进程（开始时间超过指定小时数且仍在进行中）
     */
    @Query("SELECT p FROM Process p WHERE p.status = 'IN_PROGRESS' AND p.startTime < :timeoutThreshold")
    List<Process> findTimeoutProcesses(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);
    
    /**
     * 统计各状态的进程数量
     */
    @Query("SELECT p.status, COUNT(p) FROM Process p GROUP BY p.status")
    List<Object[]> countByStatus();
    
    /**
     * 统计各治疗模式的进程数量
     */
    @Query("SELECT p.treatmentMode, COUNT(p) FROM Process p GROUP BY p.treatmentMode")
    List<Object[]> countByTreatmentMode();
    
    /**
     * 获取患者的最新进程
     */
    @Query("SELECT p FROM Process p JOIN p.record r WHERE r.patient.id = :patientId ORDER BY p.startTime DESC")
    List<Process> findLatestProcessesByPatientId(@Param("patientId") Long patientId, Pageable pageable);

    /**
     * 获取最近的5个治疗进程
     */
    List<Process> findTop5ByOrderByStartTimeDesc();

    /**
     * 统计今日新增进程数量
     */
    @Query("SELECT COUNT(p) FROM Process p WHERE DATE(p.startTime) = CURRENT_DATE")
    Long countTodayProcesses();

    /**
     * 根据患者就诊卡号模糊查询进程
     */
    @Query("SELECT p FROM Process p JOIN p.record r JOIN r.patient pt WHERE pt.patientCardId LIKE %:cardId%")
    Page<Process> findByRecordPatientPatientCardIdContaining(@Param("cardId") String cardId, Pageable pageable);

    /**
     * 根据患者姓名模糊查询进程
     */
    @Query("SELECT p FROM Process p JOIN p.record r JOIN r.patient pt WHERE pt.name LIKE %:name%")
    Page<Process> findByRecordPatientNameContaining(@Param("name") String name, Pageable pageable);

    /**
     * 根据状态查询进程
     */
    Page<Process> findByStatus(ProcessStatus status, Pageable pageable);
}