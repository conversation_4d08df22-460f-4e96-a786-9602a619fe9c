-- ========================================
-- 手动修复治疗状态问题
-- 可以直接在数据库管理工具中执行
-- ========================================

USE bonesys;

-- 1. 查看当前问题状态
SELECT '=== 当前问题状态 ===' as message;

SELECT 
    td.id,
    td.process_id,
    td.body_part,
    td.status,
    td.duration,
    TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) as elapsed_minutes,
    p.treatment_mode,
    CASE 
        WHEN p.treatment_mode = 'ON_SITE' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration 
        THEN 'SHOULD_BE_COMPLETED'
        WHEN p.treatment_mode = 'TAKE_AWAY' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15)
        THEN 'SHOULD_BE_AWAITING_RETURN'
        ELSE 'OK'
    END as should_be
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE p.status = 'IN_PROGRESS'
ORDER BY td.process_id, td.id;

-- 2. 修复本地治疗过期状态
SELECT '=== 修复本地治疗过期状态 ===' as message;

UPDATE treatment_details td
JOIN processes p ON td.process_id = p.id
SET td.status = 'COMPLETED'
WHERE td.status = 'TREATING'
  AND p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'ON_SITE'
  AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration;

-- 3. 修复远端治疗过期状态
SELECT '=== 修复远端治疗过期状态 ===' as message;

UPDATE treatment_details td
JOIN processes p ON td.process_id = p.id
SET td.status = 'AWAITING_RETURN'
WHERE td.status = 'TREATING'
  AND p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'TAKE_AWAY'
  AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15);

-- 4. 删除重复的治疗详情（保留ID最小的）
SELECT '=== 删除重复的治疗详情 ===' as message;

-- 先查看重复项
SELECT 
    process_id,
    body_part,
    head_number_used,
    COUNT(*) as count,
    GROUP_CONCAT(id ORDER BY id) as detail_ids
FROM treatment_details
WHERE process_id IN (
    SELECT id FROM processes WHERE status = 'IN_PROGRESS'
)
GROUP BY process_id, body_part, head_number_used
HAVING COUNT(*) > 1;

-- 删除重复项（保留ID最小的）
DELETE td1 FROM treatment_details td1
INNER JOIN treatment_details td2 
WHERE td1.process_id = td2.process_id
  AND td1.body_part = td2.body_part
  AND td1.head_number_used = td2.head_number_used
  AND td1.id > td2.id
  AND td1.process_id IN (
    SELECT id FROM processes WHERE status = 'IN_PROGRESS'
  );

-- 5. 检查并更新进程状态
SELECT '=== 更新进程状态 ===' as message;

-- 本地治疗进程完成
UPDATE processes p
SET p.status = 'COMPLETED', p.end_time = NOW()
WHERE p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'ON_SITE'
  AND NOT EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id 
    AND td.status NOT IN ('COMPLETED', 'RETURNED', 'TERMINATED')
  )
  AND EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id 
    AND td.status IN ('COMPLETED', 'RETURNED')
  );

-- 远端治疗进程完成
UPDATE processes p
SET p.status = 'COMPLETED', p.end_time = NOW()
WHERE p.status = 'IN_PROGRESS'
  AND p.treatment_mode = 'TAKE_AWAY'
  AND NOT EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id 
    AND td.status != 'RETURNED'
  )
  AND EXISTS (
    SELECT 1 FROM treatment_details td 
    WHERE td.process_id = p.id
  );

-- 6. 更新档案完成次数
SELECT '=== 更新档案完成次数 ===' as message;

UPDATE records r
SET sessions_completed_count = (
    SELECT COUNT(*)
    FROM processes p
    WHERE p.record_id = r.id
    AND p.status = 'COMPLETED'
)
WHERE EXISTS (
    SELECT 1 FROM processes p
    WHERE p.record_id = r.id
    AND p.status = 'COMPLETED'
);

-- 7. 显示修复后的状态
SELECT '=== 修复后状态 ===' as message;

SELECT 
    p.id as process_id,
    p.status as process_status,
    p.treatment_mode,
    COUNT(td.id) as total_details,
    SUM(CASE WHEN td.status = 'TREATING' THEN 1 ELSE 0 END) as treating,
    SUM(CASE WHEN td.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN td.status = 'AWAITING_RETURN' THEN 1 ELSE 0 END) as awaiting_return,
    SUM(CASE WHEN td.status = 'TERMINATED' THEN 1 ELSE 0 END) as terminated
FROM processes p
LEFT JOIN treatment_details td ON p.id = td.process_id
WHERE p.id >= 28
GROUP BY p.id, p.status, p.treatment_mode
ORDER BY p.id DESC;

-- 8. 显示当前活跃的治疗详情
SELECT '=== 当前活跃治疗详情 ===' as message;

SELECT 
    td.id,
    td.process_id,
    td.body_part,
    td.status,
    td.duration,
    TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) as elapsed_minutes,
    p.treatment_mode
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE td.status IN ('TREATING', 'AWAITING_RETURN')
ORDER BY td.process_id, td.id;

SELECT '=== 修复完成 ===' as message;
