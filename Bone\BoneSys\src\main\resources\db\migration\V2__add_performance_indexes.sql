-- 为新建档案页面数据显示优化添加数据库索引
-- 这些索引将显著提高多表关联查询的性能

-- 为records表添加复合索引，优化按患者ID和创建时间查询
CREATE INDEX IF NOT EXISTS idx_records_patient_created 
ON records(patient_id, created_at DESC);

-- 为body_part_stats表添加复合索引，优化按档案ID和部位查询
CREATE INDEX IF NOT EXISTS idx_body_part_stats_record 
ON body_part_stats(record_id, body_part);

-- 为patients表添加搜索索引，优化姓名和就诊卡号搜索
CREATE INDEX IF NOT EXISTS idx_patients_search_name 
ON patients(name);

CREATE INDEX IF NOT EXISTS idx_patients_search_card_id 
ON patients(patient_card_id);

-- 为patients表添加创建时间索引，优化排序查询
CREATE INDEX IF NOT EXISTS idx_patients_created_at 
ON patients(created_at DESC);

-- 为body_part_stats表添加患者相关的复合索引
-- 通过record_id关联到患者，优化患者部位统计查询
CREATE INDEX IF NOT EXISTS idx_body_part_stats_patient_lookup 
ON body_part_stats(record_id, total_usage_count DESC);

-- 为records表添加档案编号索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_records_record_number 
ON records(record_number);

-- 添加部位统计的使用次数索引，优化统计查询
CREATE INDEX IF NOT EXISTS idx_body_part_stats_usage_count 
ON body_part_stats(total_usage_count DESC);

-- 添加部位名称索引，优化按部位分组查询
CREATE INDEX IF NOT EXISTS idx_body_part_stats_body_part 
ON body_part_stats(body_part);