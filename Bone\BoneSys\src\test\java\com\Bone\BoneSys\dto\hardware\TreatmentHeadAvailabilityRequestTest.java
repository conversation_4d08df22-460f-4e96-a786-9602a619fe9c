package com.Bone.BoneSys.dto.hardware;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

/**
 * TreatmentHeadAvailabilityRequest 单元测试
 * 测试需求计算核心算法的正确性
 */
public class TreatmentHeadAvailabilityRequestTest {
    
    private TreatmentHeadAvailabilityRequest request;
    
    @BeforeEach
    void setUp() {
        request = new TreatmentHeadAvailabilityRequest();
        request.setTreatmentMode("ON_SITE");
    }
    
    @Test
    void testCalculateTotalRequiredCount_NewFormat() {
        // 测试新格式的总需求计算
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("颈部", "DEEP", 1),
            new BodyPartPatchRequest("肩部", "SHALLOW", 1)
        );
        request.setBodyPartPatches(patches);
        
        assertEquals(4, request.calculateTotalRequiredCount());
    }
    
    @Test
    void testGetShallowPatchCount_NewFormat() {
        // 测试浅部贴片需求计算
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("颈部", "DEEP", 1),
            new BodyPartPatchRequest("肩部", "SHALLOW", 1)
        );
        request.setBodyPartPatches(patches);
        
        assertEquals(3, request.getShallowPatchCount()); // 2 + 1 = 3
    }
    
    @Test
    void testGetDeepPatchCount_NewFormat() {
        // 测试深部贴片需求计算
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("颈部", "DEEP", 1),
            new BodyPartPatchRequest("肩部", "SHALLOW", 1)
        );
        request.setBodyPartPatches(patches);
        
        assertEquals(1, request.getDeepPatchCount()); // 只有颈部1个深部
    }
    
    @Test
    void testCalculateTotalRequiredCount_LegacyFormat() {
        // 测试旧格式的向后兼容性
        request.setRequiredCount(3);
        request.setPatchType("SHALLOW");
        
        assertEquals(3, request.calculateTotalRequiredCount());
    }
    
    @Test
    void testGetShallowPatchCount_LegacyFormat() {
        // 测试旧格式的浅部需求计算
        request.setRequiredCount(3);
        request.setPatchType("SHALLOW");
        
        assertEquals(3, request.getShallowPatchCount());
        assertEquals(0, request.getDeepPatchCount());
    }
    
    @Test
    void testGetDeepPatchCount_LegacyFormat() {
        // 测试旧格式的深部需求计算
        request.setRequiredCount(2);
        request.setPatchType("DEEP");
        
        assertEquals(0, request.getShallowPatchCount());
        assertEquals(2, request.getDeepPatchCount());
    }
    
    @Test
    void testIsValid_ValidNewFormat() {
        // 测试有效的新格式数据
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("颈部", "DEEP", 1)
        );
        request.setBodyPartPatches(patches);
        
        assertTrue(request.isValid());
        assertNull(request.getValidationError());
    }
    
    @Test
    void testIsValid_InvalidTreatmentMode() {
        // 测试无效的治疗模式
        request.setTreatmentMode("INVALID_MODE");
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2)
        );
        request.setBodyPartPatches(patches);
        
        assertFalse(request.isValid());
        assertEquals("治疗模式必须为 ON_SITE 或 TAKE_AWAY", request.getValidationError());
    }
    
    @Test
    void testIsValid_EmptyTreatmentMode() {
        // 测试空的治疗模式
        request.setTreatmentMode("");
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2)
        );
        request.setBodyPartPatches(patches);
        
        assertFalse(request.isValid());
        assertEquals("治疗模式不能为空", request.getValidationError());
    }
    
    @Test
    void testIsValid_InvalidPatchCount() {
        // 测试无效的贴片数量
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 5) // 超出范围
        );
        request.setBodyPartPatches(patches);
        
        assertFalse(request.isValid());
        assertEquals("第1个贴片需求的贴片数量必须在1-4之间", request.getValidationError());
    }
    
    @Test
    void testIsValid_InvalidPatchType() {
        // 测试无效的贴片类型
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "INVALID_TYPE", 2)
        );
        request.setBodyPartPatches(patches);
        
        assertFalse(request.isValid());
        assertEquals("第1个贴片需求的贴片类型必须为 SHALLOW 或 DEEP", request.getValidationError());
    }
    
    @Test
    void testIsValid_EmptyBodyPart() {
        // 测试空的身体部位
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("", "SHALLOW", 2)
        );
        request.setBodyPartPatches(patches);
        
        assertFalse(request.isValid());
        assertEquals("第1个贴片需求的身体部位不能为空", request.getValidationError());
    }
    
    @Test
    void testGetAllBodyParts_NewFormat() {
        // 测试获取所有身体部位
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("颈部", "DEEP", 1),
            new BodyPartPatchRequest("腰部", "DEEP", 1) // 重复部位
        );
        request.setBodyPartPatches(patches);
        
        List<String> bodyParts = request.getAllBodyParts();
        assertEquals(2, bodyParts.size()); // 去重后应该只有2个
        assertTrue(bodyParts.contains("腰部"));
        assertTrue(bodyParts.contains("颈部"));
    }
    
    @Test
    void testHasPatchType() {
        // 测试检查是否有指定类型的贴片需求
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("颈部", "DEEP", 1)
        );
        request.setBodyPartPatches(patches);
        
        assertTrue(request.hasPatchType("SHALLOW"));
        assertTrue(request.hasPatchType("DEEP"));
        assertFalse(request.hasPatchType("INVALID"));
    }
    
    @Test
    void testEmptyPatchList() {
        // 测试空的贴片列表
        request.setBodyPartPatches(Arrays.asList());
        
        assertEquals(0, request.calculateTotalRequiredCount());
        assertEquals(0, request.getShallowPatchCount());
        assertEquals(0, request.getDeepPatchCount());
    }
    
    @Test
    void testOnlyShallowPatches() {
        // 测试只有浅部贴片的情况
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 2),
            new BodyPartPatchRequest("肩部", "SHALLOW", 1)
        );
        request.setBodyPartPatches(patches);
        
        assertEquals(3, request.calculateTotalRequiredCount());
        assertEquals(3, request.getShallowPatchCount());
        assertEquals(0, request.getDeepPatchCount());
    }
    
    @Test
    void testOnlyDeepPatches() {
        // 测试只有深部贴片的情况
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("颈部", "DEEP", 1),
            new BodyPartPatchRequest("背部", "DEEP", 2)
        );
        request.setBodyPartPatches(patches);
        
        assertEquals(3, request.calculateTotalRequiredCount());
        assertEquals(0, request.getShallowPatchCount());
        assertEquals(3, request.getDeepPatchCount());
    }
    
    @Test
    void testMaxPatchCount() {
        // 测试最大贴片数量
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 4), // 最大值
            new BodyPartPatchRequest("颈部", "DEEP", 4)     // 最大值
        );
        request.setBodyPartPatches(patches);
        
        assertTrue(request.isValid());
        assertEquals(8, request.calculateTotalRequiredCount());
        assertEquals(4, request.getShallowPatchCount());
        assertEquals(4, request.getDeepPatchCount());
    }
    
    @Test
    void testMinPatchCount() {
        // 测试最小贴片数量
        List<BodyPartPatchRequest> patches = Arrays.asList(
            new BodyPartPatchRequest("腰部", "SHALLOW", 1), // 最小值
            new BodyPartPatchRequest("颈部", "DEEP", 1)     // 最小值
        );
        request.setBodyPartPatches(patches);
        
        assertTrue(request.isValid());
        assertEquals(2, request.calculateTotalRequiredCount());
        assertEquals(1, request.getShallowPatchCount());
        assertEquals(1, request.getDeepPatchCount());
    }
}