package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.exception.TreatmentHeadNotTakenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 硬件指令解析器
 * 负责构建发送给硬件的指令和解析硬件返回的响应
 * 
 * 支持的6种硬件指令：
 * 1. TRZI - 查询所有治疗头数据
 * 2. TWSC - 点亮推荐治疗头指示灯
 * 3. TWSN - 关闭推荐治疗头指示灯
 * 4. TWSDT - 向治疗头发送治疗参数（串口）
 * 5. TWZS - 向治疗头发送治疗参数并工作
 * 6. TWZO - 关闭治疗头
 */
@Service
public class HardwareCommandParser {
    
    private static final Logger logger = LoggerFactory.getLogger(HardwareCommandParser.class);
    
    @Autowired(required = false) // 当硬件模拟器未启用时可能为null
    private HardwareSimulatorService hardwareSimulator;
    
    // 指令结束符
    private static final String COMMAND_TERMINATOR = "\r\n";
    
    /**
     * 1. 构建查询所有治疗头数据指令
     * 发送格式：TRZI\r\n
     */
    public String buildQueryAllTreatmentHeadsCommand() {
        String command = "TRZI" + COMMAND_TERMINATOR;
        logger.debug("Built query all treatment heads command: {}", command.replace("\r\n", "\\r\\n"));
        return command;
    }


    
    /**
     * 解析查询所有治疗头数据的响应
     * 响应格式：TRZI+治疗头数量(2 char)+((治疗头编号(2char)+治疗头电量(2char或者3char)+槽编号(2char))*治疗头数量\r\n
     * 注意：根据最新硬件指令文档，没有使用次数字段，电量字段长度可变（00-99为2字符，100为3字符）
     */
    public List<TreatmentHeadInfo> parseQueryAllTreatmentHeadsResponse(String response) throws SerialCommunicationException {
        try {
            logger.debug("Parsing TRZI response: {}", response);
            
            if (!response.startsWith("TRZI")) {
                throw new SerialCommunicationException("Invalid response format for TRZI command: " + response);
            }
            
            // 移除TRZI前缀和结束符
            String data = response.substring(4);
            // 移除所有可能的结束符
            data = data.replace("\r\n", "").replace("\r", "").replace("\n", "").trim();
            logger.debug("Data after removing prefix and terminator: {}", data);
            
            // 解析治疗头数量（2位字符）
            if (data.length() < 2) {
                throw new SerialCommunicationException("Response too short to contain head count. Data: " + data);
            }
            
            int headCount = Integer.parseInt(data.substring(0, 2));
            logger.debug("Parsed head count: {}", headCount);
            
            List<TreatmentHeadInfo> headInfoList = new ArrayList<>();
            String headsData = data.substring(2);
            logger.debug("Heads data: {}, length: {}", headsData, headsData.length());
            
            // 根据最新硬件指令文档，TRZI数据格式为：
            // 治疗头编号(2char) + 治疗头电量(2char或3char) + 槽编号(2char)
            // 电量字段长度可变：00-99为2字符，100为3字符

            logger.debug("Analyzing TRZI data format - HeadCount: {}, TotalDataLength: {}", headCount, headsData.length());
            logger.debug("Raw heads data: {}", headsData);

            // 由于电量字段长度可变，我们需要逐个解析治疗头数据
            // 不能简单地按固定长度分割
            
            // 解析每个治疗头的数据
            // 格式：治疗头编号(2char) + 治疗头电量(2char或3char) + 槽编号(2char)
            int currentIndex = 0;
            for (int i = 0; i < headCount; i++) {
                if (currentIndex >= headsData.length()) {
                    logger.warn("Not enough data for treatment head {}, skipping remaining heads", i + 1);
                    break;
                }

                // 解析治疗头编号（固定2字符）
                if (currentIndex + 2 > headsData.length()) {
                    logger.warn("Insufficient data for head number at position {}", currentIndex);
                    break;
                }
                int headNumber = Integer.parseInt(headsData.substring(currentIndex, currentIndex + 2));
                currentIndex += 2;

                // 检查是否为异常治疗头（编号99表示连接异常）
                boolean isAbnormalHead = (headNumber == 99);

                // 解析电量或失败次数（2字符或3字符）
                // 对于异常治疗头（编号99），此字段表示失败次数；对于正常治疗头，此字段表示电量
                int batteryLevel;
                int failureCount = 0;

                if (isAbnormalHead) {
                    // 异常治疗头：电量字段实际是失败次数（固定2字符）
                    if (currentIndex + 2 > headsData.length()) {
                        logger.warn("Insufficient data for failure count at position {}", currentIndex);
                        break;
                    }
                    String failureStr = headsData.substring(currentIndex, currentIndex + 2);
                    failureCount = Integer.parseInt(failureStr);
                    batteryLevel = 0; // 异常治疗头电量设为0
                    currentIndex += 2;
                    logger.debug("Detected abnormal head {} with failure count: {}", headNumber, failureCount);
                } else {
                    // 正常治疗头：解析电量（2字符或3字符）
                    // 策略：先检查是否是"100"，如果不是则使用2字符
                    if (currentIndex + 3 <= headsData.length()) {
                        String potentialBattery3 = headsData.substring(currentIndex, currentIndex + 3);
                        if (potentialBattery3.equals("100")) {
                            // 确实是100%电量
                            batteryLevel = 100;
                            currentIndex += 3;
                            logger.debug("Detected 100% battery (3 chars) at position {}", currentIndex - 3);
                        } else {
                            // 不是100，使用2字符电量
                            if (currentIndex + 2 > headsData.length()) {
                                logger.warn("Insufficient data for battery level at position {}", currentIndex);
                                break;
                            }
                            String batteryStr2 = headsData.substring(currentIndex, currentIndex + 2);
                            batteryLevel = Integer.parseInt(batteryStr2);
                            currentIndex += 2;
                            logger.debug("Detected {}% battery (2 chars) at position {}", batteryLevel, currentIndex - 2);
                        }
                    } else if (currentIndex + 2 <= headsData.length()) {
                        // 只能使用2字符电量
                        String batteryStr2 = headsData.substring(currentIndex, currentIndex + 2);
                        batteryLevel = Integer.parseInt(batteryStr2);
                        currentIndex += 2;
                        logger.debug("Detected {}% battery (2 chars, end of data) at position {}", batteryLevel, currentIndex - 2);
                    } else {
                        logger.warn("Insufficient data for battery level at position {}", currentIndex);
                        break;
                    }
                }

                // 解析槽编号（固定2字符）
                if (currentIndex + 2 > headsData.length()) {
                    logger.warn("Insufficient data for slot number at position {}", currentIndex);
                    break;
                }
                int originalSlotNumber = Integer.parseInt(headsData.substring(currentIndex, currentIndex + 2));
                currentIndex += 2;

                // 转换槽号逻辑：上下层都是1-10
                int slotNumber;
                String compartmentType;

                if (isAbnormalHead) {
                    // 异常治疗头：需要根据槽号确定仓位类型
                    if (originalSlotNumber >= 1 && originalSlotNumber <= 10) {
                        compartmentType = "上仓(浅部)";
                        slotNumber = originalSlotNumber;
                    } else if (originalSlotNumber >= 11 && originalSlotNumber <= 20) {
                        compartmentType = "下仓(深部)";
                        slotNumber = originalSlotNumber - 10; // 转换为1-10
                    } else {
                        compartmentType = "UNKNOWN";
                        slotNumber = originalSlotNumber;
                    }
                    logger.debug("Abnormal head detected at slot {} ({})", slotNumber, compartmentType);
                } else if (headNumber >= 1 && headNumber <= 10) {
                    // 浅部治疗头：槽号保持1-10
                    compartmentType = "上仓(浅部)";
                    slotNumber = originalSlotNumber;
                } else if (headNumber >= 11 && headNumber <= 20) {
                    // 深部治疗头：槽号转换为1-10
                    compartmentType = "下仓(深部)";
                    slotNumber = originalSlotNumber > 10 ? originalSlotNumber - 10 : originalSlotNumber;
                } else {
                    compartmentType = "UNKNOWN";
                    slotNumber = originalSlotNumber;
                }

                // 使用次数设为0（TRZI响应中没有此字段）
                int usageCount = 0;

                // 确定治疗头状态
                String status;
                if (isAbnormalHead) {
                    status = "ABNORMAL";
                    // 对于异常治疗头，需要确定实际的治疗头编号
                    // 根据槽号推断实际的治疗头编号
                    if ("上仓(浅部)".equals(compartmentType)) {
                        headNumber = slotNumber; // 上层：槽号1-10对应治疗头1-10
                    } else if ("下仓(深部)".equals(compartmentType)) {
                        headNumber = slotNumber + 10; // 下层：槽号1-10对应治疗头11-20
                    }
                    logger.warn("Abnormal head detected: actualHeadNumber={}, slot={}, compartment={}, failureCount={}",
                               headNumber, slotNumber, compartmentType, failureCount);
                } else {
                    status = "CHARGED";
                }

                logger.debug("Parsed head {}: number={}, battery={}%, originalSlot={}, mappedSlot={}, compartment={}, usageCount={}, status={}, failureCount={}",
                           i + 1, headNumber, batteryLevel, originalSlotNumber, slotNumber, compartmentType, usageCount, status, failureCount);

                TreatmentHeadInfo headInfo = new TreatmentHeadInfo(headNumber, batteryLevel, status, compartmentType);
                headInfo.setUsageCount(usageCount);
                headInfo.setSlotNumber(slotNumber);

                // 为异常治疗头设置失败次数
                if (isAbnormalHead) {
                    headInfo.setFailureCount(failureCount);
                }
                
                // 🚨 医疗安全修复：检查硬件模拟器的工作状态
                if (hardwareSimulator != null) {
                    HardwareSimulatorService.SimulatedTreatmentHead simulatedHead = 
                        hardwareSimulator.getSimulatedHead(headNumber);
                    if (simulatedHead != null && simulatedHead.isWorking) {
                        // 如果治疗头正在工作，设置为TREATING状态
                        headInfo.setStatus("TREATING");
                        logger.debug("Head {} is currently working, status set to TREATING", headNumber);
                    }
                }
                
                headInfoList.add(headInfo);
                
                logger.debug("Parsed head info: {}", headInfo);
            }
            
            return headInfoList;
            
        } catch (NumberFormatException e) {
            throw new SerialCommunicationException("Failed to parse numeric values in response: " + response, e);
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TRZI response: " + response, e);
        }
    }


    
    /**
     * 2. 构建点亮推荐治疗头指示灯指令
     * 发送格式：TWSC+治疗头数量(2 char)+(治疗头编号+颜色 3char)*治疗头数量\r\n
     */
    public String buildLightUpCommand(List<TreatmentHeadLightRequest> lightRequests) {
        StringBuilder command = new StringBuilder("TWSC");
        
        // 添加治疗头数量（2位字符，不足补0）
        command.append(String.format("%02d", lightRequests.size()));
        
        // 添加每个治疗头的编号和颜色
        for (TreatmentHeadLightRequest request : lightRequests) {
            command.append(String.format("%02d%d", request.getHeadNumber(), request.getColorCode()));
        }
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built light up command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 解析点亮推荐治疗头指示灯的响应
     * 响应格式：TWSC+治疗头数量(2 char)+(治疗头编号(2char)+颜色(1char))*治疗头数量\r\n
     */
    public List<TreatmentHeadLightResponse> parseLightUpResponse(String response) throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWSC")) {
                throw new SerialCommunicationException("Invalid response format for TWSC command: " + response);
            }
            
            String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
            int headCount = Integer.parseInt(data.substring(0, 2));
            
            List<TreatmentHeadLightResponse> responses = new ArrayList<>();
            String headsData = data.substring(2);
            
            // 每个治疗头响应数据长度：2(编号) + 1(颜色) = 3字符 (符合实际硬件协议)
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 3;
                String headData = headsData.substring(startIndex, startIndex + 3);
                
                int headNumber = Integer.parseInt(headData.substring(0, 2));
                int colorCode = Integer.parseInt(headData.substring(2, 3));
                // 硬件协议中TWSC响应不包含槽号，使用治疗头编号作为槽号
                int slotNumber = headNumber;
                
                TreatmentHeadLightResponse lightResponse = new TreatmentHeadLightResponse(headNumber, colorCode, slotNumber);
                responses.add(lightResponse);
            }
            
            return responses;
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TWSC response: " + response, e);
        }
    }
    
    /**
     * 3. 构建关闭推荐治疗头指示灯指令
     * 发送格式：TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n
     */
    public String buildTurnOffLightCommand(List<Integer> headNumbers) {
        StringBuilder command = new StringBuilder("TWSN");
        
        // 添加治疗头数量
        command.append(String.format("%02d", headNumbers.size()));
        
        // 添加每个治疗头编号
        for (Integer headNumber : headNumbers) {
            command.append(String.format("%02d", headNumber));
        }
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built turn off light command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 解析关闭推荐治疗头指示灯的响应
     * 响应格式：TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n
     */
    public List<Integer> parseTurnOffLightResponse(String response) throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWSN")) {
                throw new SerialCommunicationException("Invalid response format for TWSN command: " + response);
            }
            
            String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
            int headCount = Integer.parseInt(data.substring(0, 2));
            
            List<Integer> headNumbers = new ArrayList<>();
            String headsData = data.substring(2);
            
            // 每个治疗头编号：2字符
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 2;
                int headNumber = Integer.parseInt(headsData.substring(startIndex, startIndex + 2));
                headNumbers.add(headNumber);
            }
            
            return headNumbers;
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TWSN response: " + response, e);
        }
    }
    
    /**
     * 4. 构建发送治疗参数指令（不工作）
     * 发送格式：TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n
     */
    public String buildSendTreatmentParamsCommand(TreatmentParamsRequest request) {
        StringBuilder command = new StringBuilder("TWSDT");
        
        // 设置时间（2字符）
        command.append(String.format("%02d", request.getDuration()));
        
        // 设置声强（3字符）
        command.append(String.format("%03d", request.getIntensity()));
        
        // 固定字符F
        command.append("F");
        
        // 重复周期标记（1字符）：0=100Hz, 1=1000Hz
        command.append(request.getFrequency() == 1000 ? "1" : "0");
        
        // 固定字符ID
        command.append("ID");
        
        // 治疗头数量
        command.append(String.format("%02d", request.getHeadNumbers().size()));
        
        // 治疗头编号列表
        for (Integer headNumber : request.getHeadNumbers()) {
            command.append(String.format("%02d", headNumber));
        }
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built send treatment params command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 5. 构建发送治疗参数并工作指令
     * 发送格式：TWZS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char)+重复周期标记(1 char)\r\n
     */
    public String buildStartTreatmentCommand(int headNumber, int duration, int intensity, int frequency) {
        StringBuilder command = new StringBuilder("TWZS");
        
        // 治疗头编号
        command.append(String.format("%02d", headNumber));
        
        // 设置时间
        command.append(String.format("%02d", duration));
        
        // 设置声强
        command.append(String.format("%03d", intensity));

        // 重复周期标记（TWZS指令不包含F字符）
        command.append(frequency == 1000 ? "1" : "0");
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built start treatment command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 6. 构建关闭治疗头指令
     * 发送格式：TWZO+治疗头编号(2char)+\r\n
     */
    public String buildStopTreatmentCommand(int headNumber) {
        String command = "TWZO" + String.format("%02d", headNumber) + COMMAND_TERMINATOR;
        logger.debug("Built stop treatment command: {}", command.replace("\r\n", "\\r\\n"));
        return command;
    }
    
    /**
     * 验证开始治疗指令的响应
     * 响应格式：TWZS+治疗头编号(2char)+设置时间(2 char) +(设置声强3char) +重复周期标记（1 char)\r\n
     */
    public boolean validateStartTreatmentResponse(String response, int headNumber, int duration, int intensity, int frequency) 
            throws SerialCommunicationException {
        try {
            // 检查硬件错误响应
            if (response.contains("zlt no take")) {
                throw new TreatmentHeadNotTakenException(headNumber);
            }
            if (response.contains("no take") || response.contains("error") || response.contains("fail")) {
                throw new SerialCommunicationException("治疗头" + headNumber + "启动失败，硬件响应：" + response);
            }
            
            if (!response.startsWith("TWZS")) {
                throw new SerialCommunicationException("Invalid response format for TWZS command: " + response);
            }
            
            String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
            
            // 解析响应数据
            int responseHeadNumber = Integer.parseInt(data.substring(0, 2));
            int responseDuration = Integer.parseInt(data.substring(2, 4));
            int responseIntensity = Integer.parseInt(data.substring(4, 7));
            // TWZS格式没有F字符，直接读取频率标记
            int responseFrequencyFlag = Integer.parseInt(data.substring(7, 8));
            int responseFrequency = responseFrequencyFlag == 1 ? 1000 : 100;
            
            // 验证参数是否匹配
            boolean matches = responseHeadNumber == headNumber &&
                            responseDuration == duration &&
                            responseIntensity == intensity &&
                            responseFrequency == frequency;
            
            if (matches) {
                logger.debug("Start treatment response validated successfully for head {}", headNumber);
                return true;
            } else {
                logger.warn("Start treatment response parameters mismatch. Expected: head={}, duration={}, intensity={}, frequency={}. Got: head={}, duration={}, intensity={}, frequency={}", 
                           headNumber, duration, intensity, frequency,
                           responseHeadNumber, responseDuration, responseIntensity, responseFrequency);
                return false;
            }
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to validate TWZS response: " + response, e);
        }
    }

    /**
     * 解析关闭治疗头指令的响应
     * 响应格式：TWZO+治疗头编号(2char)+\r\n
     */
    public int parseStopTreatmentResponse(String response) throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWZO")) {
                throw new SerialCommunicationException("Invalid response format for TWZO command: " + response);
            }
            
            String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
            return Integer.parseInt(data);
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TWZO response: " + response, e);
        }
    }
    
    /**
     * 解析发送治疗参数指令的响应
     * 响应格式：TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n
     */
    public boolean validateSendTreatmentParamsResponse(String response, TreatmentParamsRequest originalRequest) 
            throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWSDT")) {
                throw new SerialCommunicationException("Invalid response format for TWSDT command: " + response);
            }
            
            String data = response.substring(5).replace(COMMAND_TERMINATOR, "");
            
            // 解析响应中的参数
            int duration = Integer.parseInt(data.substring(0, 2));
            int intensity = Integer.parseInt(data.substring(2, 5));
            String frequencyFlag = data.substring(6, 7);
            int headCount = Integer.parseInt(data.substring(9, 11));
            
            // 验证参数是否匹配
            boolean durationMatch = duration == originalRequest.getDuration();
            boolean intensityMatch = intensity == originalRequest.getIntensity();
            boolean frequencyMatch = frequencyFlag.equals(originalRequest.getFrequency() == 1000 ? "1" : "0");
            boolean headCountMatch = headCount == originalRequest.getHeadNumbers().size();
            
            if (!durationMatch || !intensityMatch || !frequencyMatch || !headCountMatch) {
                logger.warn("TWSDT response parameters mismatch. Duration: {}/{}, Intensity: {}/{}, Frequency: {}/{}, HeadCount: {}/{}", 
                    duration, originalRequest.getDuration(),
                    intensity, originalRequest.getIntensity(),
                    frequencyFlag, (originalRequest.getFrequency() == 1000 ? "1" : "0"),
                    headCount, originalRequest.getHeadNumbers().size());
                return false;
            }
            
            // 验证治疗头编号
            String headsData = data.substring(11);
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 2;
                int responseHeadNumber = Integer.parseInt(headsData.substring(startIndex, startIndex + 2));
                if (!originalRequest.getHeadNumbers().contains(responseHeadNumber)) {
                    logger.warn("TWSDT response contains unexpected head number: {}", responseHeadNumber);
                    return false;
                }
            }
            
            logger.debug("TWSDT response validation successful");
            return true;
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to validate TWSDT response: " + response, e);
        }
    }
    

    
    /**
     * 验证响应格式是否正确
     */
    public boolean isValidResponse(String response, String expectedPrefix) {
        return response != null && 
               response.startsWith(expectedPrefix) && 
               response.endsWith(COMMAND_TERMINATOR);
    }
    
    /**
     * 验证指令格式
     */
    public boolean validateCommandFormat(String command) {
        if (command == null || !command.endsWith(COMMAND_TERMINATOR)) {
            return false;
        }
        
        String cmd = command.replace(COMMAND_TERMINATOR, "");
        
        // 验证各种指令格式
        if (cmd.equals("TRZI")) {
            return true;
        } else if (cmd.startsWith("TWSC") && cmd.length() >= 6) {
            return validateTWSCFormat(cmd);
        } else if (cmd.startsWith("TWSN") && cmd.length() >= 6) {
            return validateTWSNFormat(cmd);
        } else if (cmd.startsWith("TWSDT") && cmd.length() >= 13) {
            return validateTWSDTFormat(cmd);
        } else if (cmd.startsWith("TWZS") && cmd.length() == 11) {
            return validateTWZSFormat(cmd);
        } else if (cmd.startsWith("TWZO") && cmd.length() == 6) {
            return validateTWZOFormat(cmd);
        }
        
        return false;
    }
    
    private boolean validateTWSCFormat(String cmd) {
        try {
            int headCount = Integer.parseInt(cmd.substring(4, 6));
            // TWSC发送格式：TWSC + 数量(2) + (头号(2) + 颜色(1)) * 数量 = 6 + headCount * 3
            return cmd.length() == 6 + headCount * 3;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private boolean validateTWSNFormat(String cmd) {
        try {
            int headCount = Integer.parseInt(cmd.substring(4, 6));
            return cmd.length() == 6 + headCount * 2;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private boolean validateTWSDTFormat(String cmd) {
        try {
            // TWSDT + 时间(2) + 声强(3) + F + 频率(1) + ID + 数量(2) + 编号列表
            if (!cmd.substring(9, 10).equals("F") || !cmd.substring(11, 13).equals("ID")) {
                return false;
            }
            int headCount = Integer.parseInt(cmd.substring(13, 15));
            return cmd.length() == 15 + headCount * 2;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private boolean validateTWZSFormat(String cmd) {
        try {
            // TWZS + 编号(2) + 时间(2) + 声强(3) + 频率(1)
            // 验证长度和数字格式
            Integer.parseInt(cmd.substring(4, 6));  // 治疗头编号
            Integer.parseInt(cmd.substring(6, 8));  // 时间
            Integer.parseInt(cmd.substring(8, 11)); // 声强
            int frequency = Integer.parseInt(cmd.substring(11, 12)); // 频率标记
            return frequency == 0 || frequency == 1;
        } catch (Exception e) {
            return false;
        }
    }
    
    private boolean validateTWZOFormat(String cmd) {
        try {
            Integer.parseInt(cmd.substring(4, 6));
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 构建点亮治疗头指示灯指令（TWSC）
     */
    public String buildLightUpTreatmentHeadsCommand(List<Integer> headNumbers) {
        StringBuilder command = new StringBuilder("TWSC");

        // 添加治疗头数量
        command.append(String.format("%02d", headNumbers.size()));

        // 添加治疗头编号和颜色（默认颜色1）
        for (Integer headNumber : headNumbers) {
            command.append(String.format("%02d1", headNumber));
        }

        command.append(COMMAND_TERMINATOR);
        logger.debug("构建点亮治疗头指示灯指令: {}", command.toString());
        return command.toString();
    }

    /**
     * 解析点亮治疗头指示灯响应（TWSC）
     */
    public boolean parseLightUpTreatmentHeadsResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            logger.warn("TWSC响应为空");
            return false;
        }

        // TWSC响应格式：TWSC+数量(2位)+治疗头编号列表
        if (response.startsWith("TWSC")) {
            try {
                String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
                int headCount = Integer.parseInt(data.substring(0, 2));

                logger.info("治疗头指示灯点亮成功，数量: {}", headCount);
                return true;

            } catch (Exception e) {
                logger.error("解析TWSC响应失败: {}", response, e);
                return false;
            }
        }

        logger.warn("无效的TWSC响应格式: {}", response);
        return false;
    }



    /**
     * 解析启动治疗响应（TWZS）- 本地治疗模式专用
     */
    public boolean parseLocalStartTreatmentResponse(String response, int headNumber) {
        if (response == null || response.trim().isEmpty()) {
            logger.warn("TWZS响应为空");
            return false;
        }

        // 处理特殊的硬件错误响应
        String cleanResponse = response.trim().replace(COMMAND_TERMINATOR, "");
        
        // 检查治疗头未取出的情况
        if ("zlt no take".equals(cleanResponse)) {
            logger.warn("治疗头 {} 启动失败：治疗头未从充电仓取出", headNumber);
            throw new TreatmentHeadNotTakenException(headNumber, "治疗头 " + headNumber + " 未从充电仓取出，请先取出治疗头并贴到治疗部位");
        }
        
        // 检查其他常见的硬件错误响应
        if ("error".equalsIgnoreCase(cleanResponse) || "fail".equalsIgnoreCase(cleanResponse)) {
            logger.warn("治疗头 {} 启动失败：硬件返回错误 - {}", headNumber, cleanResponse);
            return false;
        }

        // TWZS响应格式：TWZS+治疗头编号(2位)+时长(2位)+强度(3位)+频率(1位)
        if (response.startsWith("TWZS")) {
            try {
                String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
                int responseHeadNumber = Integer.parseInt(data.substring(0, 2));

                if (responseHeadNumber == headNumber) {
                    logger.info("治疗头 {} 启动成功", headNumber);
                    return true;
                } else {
                    logger.warn("治疗头 {} 启动失败，响应治疗头编号不匹配: {}", headNumber, responseHeadNumber);
                    return false;
                }
            } catch (Exception e) {
                logger.error("解析TWZS响应失败: {}", response, e);
                return false;
            }
        }

        logger.warn("无效的TWZS响应格式: {}", response);
        return false;
    }
}