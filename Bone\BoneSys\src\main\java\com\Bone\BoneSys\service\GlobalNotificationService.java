package com.Bone.BoneSys.service;

import com.Bone.BoneSys.websocket.NotificationWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 全局弹窗通知服务
 * 负责处理4种全局弹窗通知：
 * 1. 全部治疗已完成
 * 2. 治疗头待取回
 * 3. 重新插入治疗头（失败1-3次）
 * 4. 治疗头复位（失败第4次）
 */
@Service
public class GlobalNotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalNotificationService.class);
    
    @Autowired
    private NotificationWebSocketHandler notificationHandler;
    
    /**
     * 发送全部治疗完成通知
     * @param patientName 患者姓名
     */
    public void sendAllTreatmentCompletedNotification(String patientName) {
        try {
            notificationHandler.sendTreatmentCompletedNotification(patientName);
            logger.info("已发送全部治疗完成通知: 患者={}", patientName);
        } catch (Exception e) {
            logger.error("发送全部治疗完成通知失败: 患者={}", patientName, e);
        }
    }
    
    /**
     * 发送治疗头待取回通知
     * @param headNumbers 待取回的治疗头编号列表
     */
    public void sendTreatmentHeadsPickupNotification(List<Integer> headNumbers) {
        try {
            notificationHandler.sendPickupReminderNotification(headNumbers);
            logger.info("已发送治疗头待取回通知: 治疗头={}", headNumbers);
        } catch (Exception e) {
            logger.error("发送治疗头待取回通知失败: 治疗头={}", headNumbers, e);
        }
    }
    
    /**
     * 发送重新插入治疗头通知（失败1-3次）
     * @param headNumber 治疗头编号
     * @param compartmentType 仓位类型（上仓/下仓）
     * @param slotNumber 槽号
     * @param failureCount 失败次数
     */
    public void sendReinsertTreatmentHeadNotification(int headNumber, String compartmentType, int slotNumber, int failureCount) {
        try {
            // 确保失败次数在1-3范围内
            if (failureCount >= 1 && failureCount <= 3) {
                notificationHandler.sendReinsertHeadNotification(headNumber, compartmentType, slotNumber);
                logger.info("已发送重新插入治疗头通知: 治疗头={}, 仓位={}, 槽号={}, 失败次数={}", 
                           headNumber, compartmentType, slotNumber, failureCount);
            } else {
                logger.warn("失败次数不在1-3范围内，不发送重新插入通知: 治疗头={}, 失败次数={}", 
                           headNumber, failureCount);
            }
        } catch (Exception e) {
            logger.error("发送重新插入治疗头通知失败: 治疗头={}, 仓位={}, 槽号={}", 
                        headNumber, compartmentType, slotNumber, e);
        }
    }
    
    /**
     * 发送治疗头复位通知（失败第4次）
     * @param headNumber 治疗头编号
     * @param compartmentType 仓位类型（上仓/下仓）
     * @param slotNumber 槽号
     * @param failureCount 失败次数
     */
    public void sendTreatmentHeadResetNotification(int headNumber, String compartmentType, int slotNumber, int failureCount) {
        try {
            // 确保失败次数为4次
            if (failureCount >= 4) {
                notificationHandler.sendResetHeadNotification(headNumber, compartmentType, slotNumber, failureCount);
                logger.info("已发送治疗头复位通知: 治疗头={}, 仓位={}, 槽号={}, 失败次数={}", 
                           headNumber, compartmentType, slotNumber, failureCount);
            } else {
                logger.warn("失败次数未达到4次，不发送复位通知: 治疗头={}, 失败次数={}", 
                           headNumber, failureCount);
            }
        } catch (Exception e) {
            logger.error("发送治疗头复位通知失败: 治疗头={}, 仓位={}, 槽号={}", 
                        headNumber, compartmentType, slotNumber, e);
        }
    }
}
