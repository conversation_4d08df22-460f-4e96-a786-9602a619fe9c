﻿<template>
  <div class="page flex-col">
    <div class="box_1 flex-col">
      <div class="section_1 flex-col">
        <div class="block_1 flex-col">
          <div class="section_2 flex-row justify-between">
            <img
              class="image_1 button"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentsettingsview/img/ps0rbopt6gpo79v6n1nus8nyhtdkihjzd4vl8a5a6d1c-9d09-4ddd-a32d-e212ee18a676.png"
              @click="goBack"
            />
            <span class="text_1">进程管理</span>
          </div>
          <div class="section_3 flex-row">
            <div class="group_1 flex-row">
              <span class="text_2">就诊卡号</span>
              <img
                class="image_2"
                referrerpolicy="no-referrer"
                src="../assets/images/processmangement/647757640efef33ea9167683b629e504.png"
              />
              <input type="text" v-model="searchID" class="text_3 transparent-input" :placeholder="inputFocusStates.id ? '' : '请输入'" @focus="handleInputFocus('id')" @blur="handleInputBlur('id')" />
            </div>
            <div class="group_2 flex-row">
              <span class="text_4">姓名</span>
              <img
                class="image_3"
                referrerpolicy="no-referrer"
                src="../assets/images/processmangement/c3949e032f55a049710437c0ab73ee73.png"
              />
              <input type="text" v-model="searchName" class="text_5 transparent-input" :placeholder="inputFocusStates.name ? '' : '请输入'" @focus="handleInputFocus('name')" @blur="handleInputBlur('name')" />
            </div>
            <div class="group_3 flex-row">
              <span class="text_6">状态</span>
              <img
                class="image_4"
                referrerpolicy="no-referrer"
                src="../assets/images/processmangement/ed9db5a0ff654b9b666cfac563575982.png"
              />
              <input type="text" v-model="searchState" class="text_7 transparent-input" :placeholder="inputFocusStates.state ? '' : '请输入'" @focus="handleInputFocus('state')" @blur="handleInputBlur('state')" />
            </div>
            <div class="text-wrapper_1 flex-col button" @click="handleSearch"><span class="text_8">搜索</span></div>
          </div>
          <div class="section_4 flex-col">
            <div class="text-wrapper_2 flex-row">
              <span class="text_9">就诊卡号</span>
              <span class="text_10">姓名</span>
              <span class="text_11">治疗部位</span>
              <span class="text_12">状态</span>
              <span class="text_13">操作</span>
            </div>
            <img
              class="image_5"
              referrerpolicy="no-referrer"
              src="../assets/images/processmangement/8283715a094fd731c3e7177461579534.png"
            />
            
            <template v-for="(patient, index) in paginatedPatients" :key="patient.id">
              <!-- 数据行 -->
              <div class="block_2 flex-row" v-if="index % 3 === 0">
                <span class="text_14">{{ formatMedicalId(patient.medicalRecordId) }}</span>
                <span class="text_15">{{ patient.name }}</span>
                <span class="text_16">{{ patient.bodyPart }}</span>
                
                <!-- 状态显示 -->
                <div class="image-text_1 flex-row justify-between" v-if="patient.status === '正在治疗'">
                  <img
                    class="label_1"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/66d0a38e45785c4c0951702e49af5b25.png"
                  />
                  <span class="text-group_1">治疗中</span>
                </div>
                <div class="image-text_1 flex-row justify-between" v-else-if="patient.status === '治疗完成'">
                  <img
                    class="label_1"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/c8926018d9fc39c3e262267879c19568.png"
                  />
                  <span class="text-group_1">已完成</span>
                </div>
                <div class="image-text_1 flex-row justify-between" v-else-if="patient.status === '待取回'">
                  <img
                    class="label_3"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/6d6134f668ee1ecd671411ea49f3cfa0.png"
                  />
                  <span class="text_27">待取回</span>
                </div>
                
                <div class="action-buttons">
                  <div class="text-wrapper_3 flex-col button" @click="viewPatient(patient.processId)">
                    <span class="text_17">查看</span>
                  </div>
                  <div class="text-wrapper_4 flex-col button" @click="markAsCompleted(patient.processId.toString())">
                    <span class="text_18">结束</span>
                  </div>
                </div>
              </div>
              
              <div class="block_3 flex-row" v-else-if="index % 3 === 1">
                <span class="text_19">{{ formatMedicalId(patient.medicalRecordId) }}</span>
                <span class="text_20">{{ patient.name }}</span>
                <span class="text_21">{{ patient.bodyPart }}</span>
                
                <!-- 状态显示 -->
                <div class="image-text_2 flex-row justify-between" v-if="patient.status === '正在治疗'">
                  <img
                    class="label_2"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/66d0a38e45785c4c0951702e49af5b25.png"
                  />
                  <span class="text-group_2">治疗中</span>
                </div>
                <div class="image-text_2 flex-row justify-between" v-else-if="patient.status === '治疗完成'">
                  <img
                    class="label_2"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/c8926018d9fc39c3e262267879c19568.png"
                  />
                  <span class="text-group_2">已完成</span>
                </div>
                <div class="image-text_2 flex-row justify-between" v-else-if="patient.status === '待取回'">
                  <img
                    class="label_3"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/6d6134f668ee1ecd671411ea49f3cfa0.png"
                  />
                  <span class="text_27">待取回</span>
                </div>
                
                <div class="action-buttons">
                  <div class="text-wrapper_5 flex-col button" @click="viewPatient(patient.processId)">
                    <span class="text_22">查看</span>
                  </div>
                  <div class="text-wrapper_6 flex-col button" @click="markAsCompleted(patient.processId.toString())">
                    <span class="text_23">结束</span>
                  </div>
                </div>
              </div>
              
              <div class="block_4 flex-row" v-else-if="index % 3 === 2">
                <span class="text_24">{{ formatMedicalId(patient.medicalRecordId) }}</span>
                <span class="text_25">{{ patient.name }}</span>
                <span class="text_26">{{ patient.bodyPart }}</span>
                
                <!-- 状态显示 -->
                <div class="image-text_2 flex-row justify-between" v-if="patient.status === '正在治疗'">
                  <img
                    class="label_2"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/66d0a38e45785c4c0951702e49af5b25.png"
                  />
                  <span class="text-group_2">治疗中</span>
                </div>
                <div class="image-text_2 flex-row justify-between" v-else-if="patient.status === '治疗完成'">
                  <img
                    class="label_2"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/c8926018d9fc39c3e262267879c19568.png"
                  />
                  <span class="text-group_2">已完成</span>
                </div>
                <div class="image-text_2 flex-row justify-between" v-else-if="patient.status === '待取回'">
                  <img
                    class="label_3"
                    referrerpolicy="no-referrer"
                    src="../assets/images/processmangement/6d6134f668ee1ecd671411ea49f3cfa0.png"
                  />
                  <span class="text_27">待取回</span>
                </div>
                
                <div class="action-buttons">
                  <div class="text-wrapper_7 flex-col button" @click="viewPatient(patient.processId)">
                    <span class="text_28">查看</span>
                  </div>
                  <div class="text-wrapper_8 flex-col button" @click="markAsCompleted(patient.processId.toString())">
                    <span class="text_29">结束</span>
                  </div>
                </div>
              </div>
              
              <!-- 分隔线 -->
              <img
                class="image_6"
                referrerpolicy="no-referrer"
                src="../assets/images/processmangement/982a67b4528f51c2235c82efcb565a2d.png"
                v-if="index % 3 === 0 "
              />
              
              <div class="image-wrapper_1 flex-col" v-else-if="index % 3 === 1 ">
                <img
                  class="image_7"
                  referrerpolicy="no-referrer"
                  src="../assets/images/processmangement/09cb16424dcaca2bea00ab358119d7b7.png"
                />
              </div>
              
              <div class="image-wrapper_2 flex-col" v-else-if="index % 3 === 2 ">
                <img
                  class="image_8"
                  referrerpolicy="no-referrer"
                  src="../assets/images/processmangement/5eebade1d3239e28ed2aaebef2410045.png"
                />
              </div>
            </template>
            
            <!-- 无数据时显示 -->
            <div class="image-wrapper_3 flex-col" v-if="paginatedPatients.length === 0">
              <img
                class="image_9"
                referrerpolicy="no-referrer"
                src="../assets/images/processmangement/c44673d78f8b955235350e1161c506d5.png"
              />
            </div>
          </div>
          
          <!-- 分页控件 -->
          <div class="pagination-container" v-if="totalPages > 1">
            <div class="pagination-controls">
              <div class="pagination-btn prev-btn button"
                   @click="prevPage"
                   :class="{ disabled: currentPage === 1 }">
                <img src="../assets/images/patientmangement/pscfuqtlgklge35gqvwmjemf1ftzt5cw0k164a5578-5e3c-4723-bc66-875bb1108d45.png" />
              </div>
              <div class="page-info">
                第 {{ currentPage }} 页/共 {{ totalPages }} 页
              </div>
              <div class="pagination-btn next-btn button"
                   @click="nextPage"
                   :class="{ disabled: currentPage === totalPages }">
                <img src="../assets/images/patientmangement/psmqhsvt3d8c3z2wc722ghc6ga6sph03fs58fbf288-2ea0-4006-995a-1fb501817a56.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import http from '@/utils/axios';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { terminateProcess } from '@/api/treatmentProcess';

const router = useRouter();

// 数据状态
const searchName = ref('');
const searchID = ref('');
const searchState = ref('');
const patients = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(7); 
const searchLoading = ref(false);

// 输入框焦点状态
const inputFocusStates = ref({
  id: false,
  name: false,
  state: false
});

// 状态映射函数
const getStatusCode = (status: string): number => {
  const statusMap: Record<string, number> = {
    'TREATING': 2,
    'COMPLETED': 3,
    'CANCELLED': 4, // 待取回
    'AWAITING_RETURN': 4, // 待取回
    'UNKNOWN': 1
  };
  return statusMap[status] || 1;
};

// 状态文本映射
const getStatusText = (status: string): string => {
  const statusTextMap: Record<string, string> = {
    'TREATING': '正在治疗',
    'COMPLETED': '治疗完成',
    'CANCELLED': '待取回',
    'AWAITING_RETURN': '待取回',
    'UNKNOWN': '未知状态'
  };
  return statusTextMap[status] || '未知状态';
};

// 获取进程数据
const fetchPatients = async () => {
  try {
    searchLoading.value = true;
    // 使用正确的进程管理API接口
    const response = await http.get('/processes', {
      params: {
        page: 1,
        size: 50, // 获取足够多的数据用于前端分页
        cardId: undefined,
        patientName: undefined,
        status: undefined
      }
    });
    
    // 适配API文档中的processes数据格式
    if (response.data && response.data.processes && Array.isArray(response.data.processes)) {
      patients.value = response.data.processes.map((process: any) => ({
        id: process.processId,
        medicalRecordId: process.cardId,
        name: process.patientName, // 修复：使用name字段与模板保持一致
        bodyPart: process.bodyPart,
        status: getStatusText(process.status), // 使用中文状态
        statusCode: getStatusCode(process.status),
        // 保存原始状态和processId用于查看功能
        originalStatus: process.status,
        processId: process.processId
      }));
    } else {
      patients.value = [];
    }
  } catch (error) {
    console.error('获取进程列表失败:', error);
    MessagePlugin.error('获取进程列表失败，请稍后重试');
    patients.value = [];
  } finally {
    searchLoading.value = false;
  }
};

// 格式化病历号显示（完整显示原始编号，不补位）
const formatMedicalId = (id: string) => {
  if (!id) return '';
  // 只移除非数字字符，保持原始格式
  return id.toString().replace(/\D/g, '');
};

// 格式化状态
const formatStatus = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待处理', 
    2: '治疗中', 
    3: '已完成', 
    4: '待取回',
    5: '中止',
    6: '已收回'
  };
  return statusMap[status] || '未知状态';
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
};

// 过滤患者数据（显示所有有效状态的进程）
const filteredPatients = computed(() => {
  let result = [...patients.value];

  // 显示所有有效状态的患者（治疗中、已完成、待取回）
  result = result.filter(p =>
    p.status === '正在治疗' ||
    p.status === '治疗完成' ||
    p.status === '待取回' ||
    p.originalStatus === 'TREATING' ||
    p.originalStatus === 'COMPLETED' ||
    p.originalStatus === 'AWAITING_RETURN'
  );
  
  if (searchID.value) {
    result = result.filter(p => 
      p.medicalRecordId && 
      formatMedicalId(p.medicalRecordId).includes(searchID.value)
    );
  }
  
  if (searchName.value) {
    const lowerKeyword = searchName.value.toLowerCase();
    result = result.filter(item => 
      item.name && item.name.toLowerCase().includes(lowerKeyword)
    );
  }
  
  if (searchState.value) {
    result = result.filter(p => 
      p.status === searchState.value
    );
  }
  
  return result;
});

// 分页数据
const paginatedPatients = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredPatients.value.slice(start, end);
});

// 总页数
const totalPages = computed(() => {
  return Math.max(Math.ceil(filteredPatients.value.length / pageSize.value), 1);
});

// 上一页
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

// 下一页
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 查看患者详情 - 根据治疗模式跳转到对应的治疗进程页面
const viewPatient = async (processId: number) => {
  try {
    // 先获取进程实时数据以确定治疗模式
    const response = await http.get(`/processes/${processId}/realtime`);
    
    if (response.data && response.data.treatmentMode) {
      const treatmentMode = response.data.treatmentMode;
      
      // 根据治疗模式跳转到对应页面
      if (treatmentMode === 'ON_SITE') {
        // 本地治疗 - 跳转到 TreatmentProcessView1
        router.push(`/treatment-process/${processId}`);
      } else {
        // 取走治疗 - 跳转到 TreatmentProcessView2
        router.push(`/treatment-process-takeaway/${processId}`);
      }
    } else {
      MessagePlugin.error('无法获取治疗模式信息');
    }
  } catch (error) {
    console.error('获取治疗模式失败:', error);
    MessagePlugin.error('获取治疗进程信息失败');
  }
};

// 返回首页
const goBack = () => {
  router.push('/home');
};

// 处理输入框聚焦和失焦
const handleInputFocus = (inputName: string) => {
  inputFocusStates.value[inputName as keyof typeof inputFocusStates.value] = true;
};

const handleInputBlur = (inputName: string) => {
  inputFocusStates.value[inputName as keyof typeof inputFocusStates.value] = false;
};

// 中止进程
const markAsCompleted = (processId: string) => {
  const confirmDialog = DialogPlugin.confirm({
    header: '确认操作',
    body: '确定要中止该进程吗？完成后将从列表中移除',
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        // 使用正确的进程终止接口
        await terminateProcess(parseInt(processId));
        MessagePlugin.success('进程中止成功');
        
        // 从本地列表中移除该进程
        patients.value = patients.value.filter(patient => patient.processId.toString() !== processId);
        
        // 如果当前页没有数据且不是第一页，则返回上一页
        if (paginatedPatients.value.length === 0 && currentPage.value > 1) {
          currentPage.value--;
        }
      } catch (error) {
        console.error('中止进程失败:', error);
        MessagePlugin.error('中止进程失败');
      } finally {
        confirmDialog.destroy();
      }
    },
    onClose: () => {
      confirmDialog.destroy();
    }
  });
};

// 初始化加载数据
onMounted(() => {
  fetchPatients();
});
</script>

<style scoped lang="css">
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1073px;
  background: url(../assets/images/processmangement/6fb3e10f3e96c7288259592a9ab58684.png)
    0px -7px no-repeat;
  background-size: 1916px 1080px;
  margin-left: 4px;
  width: 1916px;
}

.section_1 {
  height: 1080px;
  background: url(../assets/images/processmangement/b111809ee101ba601c84c4c92f0294ca.png)
    0px -7px no-repeat;
  background-size: 1916px 1087px;
  width: 1916px;
}

.block_1 {
  width: 1920px;
  height: 1080px;
  background: url(../assets/images/processmangement/86810806097762d56f754980fac2b438.png)
    0px -6px no-repeat;
  background-size: 1920px 1086px;
  margin-left: -4px;
}

.section_2 {
  width: 1003px;
  height: 70px;
  margin: 13px 0 0 107px;
}

.image_1 {

  width: 151px;
  height: 61px;
  cursor: pointer;
}

.text_1 {
  width: 294px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41.67px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 9px;
}

.section_3 {
  width: 1585px;
  height: 91px;
  margin: 53px 0 0 146px;
}

.group_1 {
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 474px;
  height: 66px;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin-top: 9px;
}

.text_2 {
  width: 153px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 36px;
}

.image_2 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 30px;
}

.text_3 {
  width: 95px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1); /* 输入文字改为黑色 */
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 75px 0 84px;
  background: transparent;
  border: none;
  outline: none;
}

.text_3::placeholder {
  color: rgba(202, 201, 202, 1); /* placeholder保持灰色 */
}

.group_2 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 346px;
  height: 66px;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 9px 0 0 58px;
}

.text_4 {
  width: 70px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 37px;
}

.image_3 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 27px;
}

.text_5 {
  width: 95px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1); /* 输入文字改为黑色 */
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 59px 0 57px;
  background: transparent;
  border: none;
  outline: none;
}

.text_5::placeholder {
  color: rgba(202, 201, 202, 1); /* placeholder保持灰色 */
}

.group_3 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 346px;
  height: 66px;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 10px 0 0 72px;
}

.text_6 {
  width: 74px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 24px;
}

.image_4 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 27px;
}

.text_7 {
  width: 95px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1); /* 输入文字改为黑色 */
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 74px 0 51px;
  background: transparent;
  border: none;
  outline: none;
}

.text_7::placeholder {
  color: rgba(202, 201, 202, 1); /* placeholder保持灰色 */
}

.text-wrapper_1 {
  height: 91px;
  background: url(../assets/images/processmangement/a4bebd0b7c83d15be147f3b345ef791d.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 62px;
  width: 227px;
  cursor: pointer;
}

.text_8 {
  width: 80px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 15px 0 0 65px;
}

.section_4 {
  width: 1689px;
  height: 783px;
  background: url(../assets/images/processmangement/79c3ec967a296bb1f063e3795e500ab9.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 22px 0 48px 113px;
  position: relative;
}

.text-wrapper_2 {
  width: 1368px;
  height: 34px;
  margin: 47px 0 0 168px;
}

.text_9 {
  width: 156px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text_10 {
  width: 100px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 120px;
}

.text_11 {
  width: 150px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 130px;
}

.text_12 {
  width: 150px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 120px;
}

.text_13 {
  width: 150px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 205px;
}

.image_5 {
  width: 1476px;
  height: 1px;
  margin: 16px 0 0 116px;
}

.block_2 {
  width: 1414px;
  height: 47px;
  margin: 12px 0 0 171px;
  position: relative;
}

.text_14 {
  width: 156px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 8px;
  font-family: 'Courier New', monospace;
}

.text_15 {
  width: 100px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 120px;
}

.text_16 {
  width: 150px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 130px;
}

.image-text_1 {
  width: 150px;
  height: 38px;
  margin: 6px 0 0 120px;
}

.label_1 {
  width: 39px;
  height: 37px;
  margin-top: 1px;
}

.text-group_1 {
  width: 157px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text-wrapper_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  width: 89px;
  cursor: pointer;
  position: absolute;
  right: 109px;
  top: 0;
}

.text_17 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0px 0 0 14px;
  letter-spacing: 6px
}

.text-wrapper_4 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  width: 89px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
}

.text_18 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0px 0 0 14px;
  letter-spacing: 6px
}

.image_6 {
  width: 1476px;
  height: 1px;
  margin: 11px 0 0 116px;
}

.block_3 {
  width: 1414px;
  height: 48px;
  margin: 12px 0 0 171px;
  position: relative;
}

.text_19 {
  width: 156px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 11px;
  font-family: 'Courier New', monospace;
}

.text_20 {
  width: 100px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 120px;
}

.text_21 {
  width: 150px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 130px;
}

.image-text_2 {
  width: 150px;
  height: 39px;
  margin: 9px 0 0 120px;
}

.label_2 {
  width: 38px;
  height: 38px;
  margin-top: 1px;
}

.text-group_2 {
  width: 157px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text-wrapper_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  width: 89px;
  cursor: pointer;
  position: absolute;
  right: 109px;
  top: 0;
}

.text_22 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0px 0 0 14px;
  letter-spacing: 6px
}

.text-wrapper_6 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  width: 89px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
}

.text_23 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0px 0 0 14px;
  letter-spacing: 6px
}

.image-wrapper_1 {
  height: 1px;
  background: url(../assets/images/processmangement/982c824b98c17706cb7e17a9100be65c.png)
    0px 0px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 10px 0 0 116px;
}

.image_7 {
  width: 1476px;
  height: 1px;
}

.block_4 {
  width: 1414px;
  height: 47px;
  margin: 12px 0 0 171px;
  position: relative;
}

.text_24 {
  width: 156px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
  font-family: 'Courier New', monospace;
}

.text_25 {
  width: 100px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 120px;
}

.text_26 {
  width: 150px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 7px 0 0 130px;
}

.label_3 {
  width: 46px;
  height: 46px;
  margin: 0px 0 0 0px;
}

.text_27 {
  width: 113px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;

}

.text-wrapper_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  width: 89px;
  cursor: pointer;
  position: absolute;
  right: 109px;
  top: 0;
}

.text_28 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0px 0 0 14px;
  letter-spacing: 6px
}

.text-wrapper_8 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  width: 89px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
}

.text_29 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0px 0 0 14px;
  letter-spacing: 6px
}

.image-wrapper_2 {
  height: 1px;
  background: url(../assets/images/processmangement/3c5d899b5feda3dc325b832e42bab2a8.png)
    0px 0px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 11px 0 0 116px;
}

.image_8 {
  width: 1476px;
  height: 1px;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 57px;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 260px;
  margin: 308px 0 107px 1366px;
}

.image_9 {
  width: 235px;
  height: 38px;
  margin: 9px 0 0 10px;
}

.transparent-input {
  background: transparent;
  border: none;
  outline: none;
  font-size: 25px;
  color: rgba(89, 89, 89, 1); /* 输入文字改为黑色 */
  font-family: MicrosoftYaHei;
}

.transparent-input::placeholder {
  color: rgba(202, 201, 202, 1); /* placeholder保持灰色 */
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

/* 操作按钮容器 */
.action-buttons {
  position: absolute;
  right: 113px;
  top: 0;
  height: 100%;
  display: flex;
  width: 150px;
  margin-left: 0px;
}

/* 分页控件样式 */
.pagination-container {
  position: absolute;
  right: 200px;
  bottom: 120px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 6px 18px;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-info {
  margin: 0 18px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 19px;
  color: #333;
}

.pagination-btn {
  width: 50px;
  height: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(.disabled) {
  transform: scale(1.05);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn img {
  width: 46px;
  height: 46px;
}

/* 按钮动画效果 */
.button {
  transition: transform 0.1s ease;
}

.button:active {
  transform: scale(0.95);
}

/* 特殊处理返回按钮 */
.image_1.button:active {
  transform: scale(0.98);
}
</style>
