
### 1. 登录页面 (`新版-登录页面.jpg`)

* **所需数据**:
    * 登录凭证 (Token)。
* **说明**:
    页面加载时，不需要从后端获取任何数据。当用户输入密码点击登录，后端验证成功后，需要返回一个加密的字符串（即Token）。前端会保存这个Token，并在之后访问其他页面时携带它，作为用户已登录的身份证明。

---

### 2. 档案管理页面 (`新版-档案管理.png`)

* **所需数据**:
    * 一个包含多位患者档案信息的列表。
    * 对于列表中的**每一位患者**，需要提供：`患者ID`、`就诊卡号`、`姓名`、`年龄`、`性别`、`累计治疗次数` 和 `建档日期`。
    * 分页信息，包括 `当前是第几页`、`总共有多少页` 和 `总共有多少条档案`。
* **说明**:
    这些数据用于填充页面中央的患者档案表格。分页信息则用来生成右下角的页码导航，确保用户可以方便地浏览所有患者的档案。当用户使用搜索功能时，后端需要根据搜索条件返回筛选后的列表。

---

### 3. 档案管理 - 确定删除弹窗 (`新版-档案管理-确定删除弹窗.png`)

* **所需数据**:
    * 操作成功或失败的提示信息。
* **说明**:
    显示这个弹窗本身是前端的行为，**不需要后端提供数据**。当用户在弹窗里输入密码并点击“确认”后，后端在处理完删除请求后，只需返回一个简单的结果，例如“删除成功”或“密码错误”，前端根据这个结果给用户相应的提示。

---

### 4. 个人信息页面 (`新版-个人信息.png`)

* **所需数据**:
    * **患者基本信息**: `头像图片地址`、`姓名`、`编号`、`就诊卡号`、`性别`、`年龄` 和 `联系电话`。
    * **治疗时间统计**: 一个按身体部位（如肩颈部、腰背部等）分类的累计治疗时长列表，以及一个总的累计治疗时长。
    * **治疗历史记录**: 一个可以翻页的列表，列表中的**每一条记录**需要包含 `治疗日期`、`治疗部位`、`有效声强`、`使用的治疗头编号` 和 `本次治疗时长`。
    * **分页信息**: 用于治疗历史记录列表的翻页。
* **说明**:
    后端需要提供指定患者的所有详细信息，用于一次性渲染整个页面。这些数据组合起来，为用户提供了一个关于该患者的完整视图，包括其个人资料、治疗概况和详细的治疗历史。

---

### 5. 个人信息 - 诊断详细弹窗 (`新版-个人信息-诊断详细弹窗.png`)

* **所需数据**:
    * 一个包含该患者所有诊断记录的列表。
    * 对于列表中的**每一条诊断记录**，需要提供 `诊断日期`、`诊断医生姓名` 和 `详细的诊断内容描述文字`。
* **说明**:
    当用户在个人信息页面点击“诊断详情”按钮时，后端需要根据患者ID，查询并返回其所有的历史诊断信息，用于在这个弹窗中展示。

---

### 6. 参数设置页面 (`新版-参数设置.jpg`)

* **所需数据**:
    * **当前参数配置**: 一个列表，包含所有身体部位（肩颈部、腰背部等）当前保存的默认参数值，例如每个部位对应的 `治疗时间`、`强度`、`脉冲频率` 和 `治疗贴片类型`。
    * **可用参数选项**: 用于配置的下拉菜单的所有可选项。例如，一个包含所有可选强度值的列表（如 "30mW/C", "45mW/C"），一个包含所有可选频率值的列表等。
* **说明**:
    后端需要提供这两类数据。“当前参数配置”用于页面加载时，将已保存的设置填充到对应的输入框中。“可用参数选项”则用于动态生成各个下拉菜单，让用户可以进行修改和配置。

---

### 7. & 8. 参数设置 - 治疗头选择弹窗 (本地治疗与取走治疗)

* **所需数据**:
    * 所有治疗头的实时状态列表，通常按“上仓”和“下仓”分组。
    * 对于**每一个治疗头**，需要提供其 `仓位编号`、`类型`（深部或浅部）和 `当前状态`（例如：充电中、充电完成、治疗中、不可用等）。
* **说明**:
    此数据用于在弹窗中实时、可视化地展示20个治疗头的状态。前端会根据不同的状态，用不同的颜色或图标来渲染每个治疗头，从而清晰地告知用户哪些是当前可用的。

---

### 9. 参数设置 - 治疗头不足弹窗 (`新版-参数设置-治疗头不足弹窗.jpg`)

* **所需数据**:
    * 一个表示错误的特定业务代码（如 `1002`）和对应的文字提示信息（如“可用治疗头数量不足”）。
* **说明**:
    这个弹窗的出现是由后端返回的一个**错误状态**触发的。它本身不需要内容数据。当后端检查发现可用治疗头不够时，就返回一个特定的错误信号，前端接收到这个信号后，就显示这个预设好的提示弹窗。

---

### 10. 参数设置 - 参数下载弹窗 (`新版-参数设置-参数下载弹窗.jpg`)

* **所需数据**:
    * **无需后端提供任何数据**来显示此弹窗。
* **说明**:
    这个弹窗是一个“正在处理中”的动画提示，纯粹是前端的UI状态。它在前端向后端发送“下载参数”的请求之后、到正式收到后端的处理结果（成功或失败）之前显示。它的显示与否由前端自身的程序逻辑控制，不依赖于后端的数据。


### 1. 主界面 (`新版-主界面.png`)

* **所需数据**:
    * 可用的治疗头总数，以及系统中的治疗头总数。
* **说明**:
    此主界面相对简洁，主要提供导航功能。唯一可能需要从后端动态获取的数据是右上角的“20/20”状态指示，它代表了当前设备（治疗头）的可用情况，因此后端需要提供“当前可用设备数”和“设备总数”。导航按钮（新建档案、档案管理、进程管理）为固定元素，无需后端数据。

---

### 2. 新建档案 (`新版-新建档案.png`)

* **所需数据**:
    * 一个包含患者或预约信息的列表。
    * 对于列表中的**每一条记录**，需要提供：`就诊卡号`、`姓名`、`年龄`、`性别`、`就诊时间`、`治疗部位` 和 `次数`。
    * 分页信息，包括 `当前页码` 和 `总页数`。
* **说明**:
    尽管页面名为“新建档案”，但其内容是显示一个记录列表。这可能是为了方便用户在新建档案前，先搜索并确认患者是否已存在。后端需要提供此列表数据用于填充表格。用户可以从列表中选择一项“进入档案”，或点击“新建”按钮弹出信息录入窗口。

---

### 3. 新建档案 - 患者信息弹窗 (`新版-新建档案-患者信息弹窗.png`)

* **所需数据**:
    * （可选）由系统预先生成的 `患者编号`。
* **说明**:
    显示这个弹窗本身是前端行为，弹窗内的表单（姓名、年龄、电话等）都由用户填写，因此**几乎不需要后端提供数据**。唯一可能需要的是，在弹窗加载时，后端可以预先生成一个唯一的“编号”并返回给前端填入，以避免重复。当用户点击“确认创建”后，会将表单中的所有信息发送给后端进行保存。

---

### 4. 进程管理 (`新版-进程管理.png`)

* **所需数据**:
    * 一个包含所有治疗进程的列表。
    * 对于列表中的**每一条进程**，需要提供：`就诊卡号`、`患者姓名`、`治疗部位` 和 `当前状态`（例如：治疗完成、正在治疗、待取回等）。
    * 分页信息，用于列表的翻页。
    * （可选）一个包含所有可用状态的列表（如 "治疗完成", "正在治疗"），用于填充搜索区域的“状态”下拉菜单。
* **说明**:
    此页面用于集中展示和管理系统里所有的治疗进程。后端需要提供一个可根据就诊卡号、姓名、状态进行筛选和分页的进程列表。

---

### 5. 治疗头管理 (`新版-治疗头管理.png`)

* **所需数据**:
    * 一个包含所有治疗头详细信息的列表。
    * 对于列表中的**每一个治疗头**，需要提供：`编号`、`文字状态`（如“充电结束”）、`图标状态`（用于显示前面的对勾、充电、治疗中等图标）、`使用次数`、`累计使用时间`、`当前电池电量百分比` 和 `状态指示灯颜色`（用于显示最右侧的绿、橙、红灯）。
    * 分页信息。
* **说明**:
    此页面用于监控每一个治疗头的详细状态。后端需要提供全面的信息，以便管理者能够清晰地了解每个设备的使用情况、电量和健康状况。

---

### 6. 治疗进程 - 本地治疗 (`新版-治疗进程-本地治疗.png`)

* **所需数据**:
    * 当前治疗的`患者姓名`。
    * 一个包含当前正在进行的各个治疗部位的列表。
    * 对于**每一个治疗部位**，需要提供：`部位名称`、`剩余治疗时间` 和 `治疗声强`。
* **说明**:
    此页面用于实时监控“本地治疗”过程。后（或通过硬件）端需要持续提供“剩余治疗时间”的动态更新数据，直到治疗结束。

---

### 7. 治疗进程 - 取走治疗 (`新版-治疗进程-取走治疗.png`)

* **所需数据**:
    * 当前治疗的`患者姓名`。
    * 一个包含当前正在进行的各个治疗部位的列表。
    * 对于**每一个治疗部位**，需要提供：`部位名称`、`已经使用的时间` 和 `治疗声强`。
* **说明**:
    此页面与“本地治疗”类似，但用于监控“取走治疗”的场景。其核心动态数据是“使用时间”，需要后端持续更新，以反映设备离线后已经工作了多久。

---

### 8. 治疗进程 - 治疗完成/异常结束弹窗

* **文件**: `新版-治疗进程-治疗完成弹窗.png` / `新版-治疗进程-治疗头异常结束弹窗.png`
* **所需数据**:
    * 一个代表事件发生的信号或特定错误码。
* **说明**:
    这两个弹窗都是由特定**事件触发**的，而不是由数据请求来显示的。
    * **治疗完成**：当后端检测到所有治疗都结束后，会通知前端，前端随即显示这个“已完成”的提示。
    * **异常结束**：当后端检测到硬件通信故障（如蓝牙连接失败）时，会向前端发送一个特定的错误信号（如错误码`1001`），前端收到后显示对应的错误提示信息。

---

### 9. 设置页面 (`新版-设置页面.jpg`)

* **所需数据**:
    * 一个包含当前各项系统设置值的集合。
    * 具体需要：`当前音量大小`、`当前息屏时间`（如20min）、`当前语言`（中文或ENGLISH）和`当前提醒设置的时间`（如10min, 15min, 20min）。
* **说明**:
    当用户打开设置页面时，后端需要提供所有设置项的当前值，用于正确显示控件的状态（如音量条的位置、高亮的语言选项等）。密码设置部分通常不显示原密码，只提供重置或修改功能。


### 1. 主界面 - 治疗完成弹窗 (`新版-主界面-治疗完成弹窗.png`)

* **所需数据**:
    * 事件类型，用于标识这是一个“治疗完成”的通知。
    * 完成治疗的`患者姓名`。
* **说明**:
    这个弹窗是一个实时的系统通知，它不是在页面加载时请求的，而是由后端在某个治疗进程结束后**主动推送**给前端的。这通常通过WebSocket等技术实现。当后端检测到有患者（例如“易烊千玺”）完成了全部治疗，就会向前端发送一个包含患者姓名的“治疗完成”事件，前端接收到后便会显示这个弹窗以提醒操作人员。

---

### 2. 主界面 - 取回提醒弹窗 (`新版-主界面-取回提醒弹窗.png`)

* **所需数据**:
    * 事件类型，用于标识这是一个“待取回提醒”。
    * 一个需要被取回的`治疗头编号列表`。
* **说明**:
    与上一个类似，这也是一个由后端主动推送的实时事件通知。当系统检测到有“取走治疗”模式的设备已完成治疗且需要被归还时，后端会触发一个提醒事件。该事件需要包含所有待取回设备的具体编号（如 "1, 2, 3"），前端收到这个事件后，会将这些编号整合到提示信息中并显示这个弹窗，通知操作人员及时回收设备。