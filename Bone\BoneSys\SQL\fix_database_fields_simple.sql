-- ========================================
-- 修复数据库字段名不匹配问题（简化版）
-- 问题1：后端代码期望 user_password_hash，但数据库中是 users_password_hash
-- 问题2：后端代码期望 compartment_type，但数据库中是 compartment
-- 解决：重命名字段以匹配后端代码
-- ========================================

USE bonesys;

-- 显示当前表结构
SELECT '=== 当前用户表结构 ===' as message;
SHOW COLUMNS FROM users;

SELECT '=== 当前治疗头表结构 ===' as message;
SHOW COLUMNS FROM treatment_heads;

-- 修复用户表字段名
-- 如果出现错误说字段不存在，说明已经修复过了
SELECT '=== 开始修复用户表字段名 ===' as message;
ALTER TABLE `users` 
CHANGE COLUMN `users_password_hash` `user_password_hash` VARCHAR(50) NOT NULL DEFAULT '123456' COMMENT '用户密码（明文存储）';

SELECT '=== 用户表字段修复完成 ===' as message;

-- 修复治疗头表字段名
-- 如果出现错误说字段不存在，说明已经修复过了
SELECT '=== 开始修复治疗头表字段名 ===' as message;
ALTER TABLE `treatment_heads` 
CHANGE COLUMN `compartment` `compartment_type` VARCHAR(10) NULL DEFAULT NULL COMMENT '仓位 (UPPER=上层浅部, LOWER=下层深部)';

SELECT '=== 治疗头表字段修复完成 ===' as message;

-- 验证修复结果
SELECT '=== 修复后的用户表结构 ===' as message;
SHOW COLUMNS FROM users;

SELECT '=== 修复后的治疗头表结构 ===' as message;
SHOW COLUMNS FROM treatment_heads;

-- 验证数据
SELECT '=== 验证用户数据 ===' as message;
SELECT id, username, user_password_hash FROM users;

SELECT '=== 验证治疗头数据 ===' as message;
SELECT head_number, slot_number, compartment_type, realtime_status FROM treatment_heads ORDER BY head_number LIMIT 5;

SELECT '=== 所有字段修复完成！===' as message;
