package com.Bone.BoneSys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 增强的候选患者项目DTO
 * 包含完整的患者信息，包括档案和部位统计数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnhancedCandidateItem {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 就诊卡号
     */
    private String cardId;
    
    /**
     * 患者姓名
     */
    private String name;
    
    /**
     * 年龄
     */
    private String age;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 就诊时间（最新档案的建档日期）
     */
    private String visitTime;
    
    /**
     * 治疗部位（逗号分隔）
     */
    private String treatmentParts;
    
    /**
     * 总治疗次数
     */
    private Integer totalSessions;
    
    /**
     * 构造函数，用于设置默认值
     */
    public EnhancedCandidateItem(Long patientId, String cardId, String name, String age, String gender) {
        this.patientId = patientId;
        this.cardId = formatCardId(cardId);
        this.name = formatName(name);
        this.age = formatAge(age);
        this.gender = formatGender(gender);
        this.visitTime = "无档案";
        this.treatmentParts = "待确定";
        this.totalSessions = 0;
    }
    
    /**
     * 格式化就诊卡号
     */
    public static String formatCardId(String cardId) {
        if (cardId == null || cardId.trim().isEmpty()) {
            return "未知";
        }
        return cardId.trim();
    }
    
    /**
     * 格式化患者姓名
     */
    public static String formatName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "未知患者";
        }
        return name.trim();
    }
    
    /**
     * 格式化年龄
     */
    public static String formatAge(String age) {
        if (age == null || age.trim().isEmpty()) {
            return "未知";
        }
        String trimmedAge = age.trim();
        return trimmedAge.endsWith("岁") ? trimmedAge : trimmedAge + "岁";
    }
    
    /**
     * 格式化性别
     */
    public static String formatGender(String gender) {
        if (gender == null || gender.trim().isEmpty()) {
            return "未知";
        }
        return gender.trim();
    }
    
    /**
     * 格式化就诊时间
     */
    public static String formatVisitTime(LocalDate date) {
        if (date == null) {
            return "无档案";
        }
        return date.format(DATE_FORMATTER);
    }
    
    /**
     * 格式化治疗部位列表
     */
    public static String formatTreatmentParts(List<String> parts) {
        if (parts == null || parts.isEmpty()) {
            return "待确定";
        }
        
        // 过滤空值并去重
        List<String> validParts = parts.stream()
                .filter(part -> part != null && !part.trim().isEmpty())
                .map(String::trim)
                .distinct()
                .toList();
        
        if (validParts.isEmpty()) {
            return "待确定";
        }
        
        return String.join(", ", validParts);
    }
    
    /**
     * 格式化治疗次数
     */
    public static Integer formatTotalSessions(Integer sessions) {
        return sessions != null && sessions >= 0 ? sessions : 0;
    }
    
    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        return patientId != null && 
               cardId != null && !cardId.trim().isEmpty() &&
               name != null && !name.trim().isEmpty();
    }
    
    /**
     * 获取显示用的简短描述
     */
    public String getDisplaySummary() {
        return String.format("%s (%s) - %s %s", 
                name, cardId, age, gender);
    }
    
    /**
     * 检查是否有档案记录
     */
    public boolean hasRecords() {
        return !"无档案".equals(visitTime);
    }
    
    /**
     * 检查是否有治疗记录
     */
    public boolean hasTreatments() {
        return totalSessions != null && totalSessions > 0;
    }
    
    /**
     * 获取治疗部位数量
     */
    public int getTreatmentPartsCount() {
        if ("待确定".equals(treatmentParts) || treatmentParts == null) {
            return 0;
        }
        return treatmentParts.split(",").length;
    }
}