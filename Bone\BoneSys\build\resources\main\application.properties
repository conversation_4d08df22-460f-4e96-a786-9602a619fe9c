# 应用基础配置
spring.application.name=BoneSys
server.port=8080

# 数据库配置
spring.datasource.url=************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=111111

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.connection.characterEncoding=UTF-8
spring.jpa.properties.hibernate.connection.useUnicode=true
spring.jpa.properties.hibernate.connection.CharSet=UTF-8

# 日志配置
logging.level.com.Bone.BoneSys=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# JWT配置 - 使用256位安全密钥（Base64编码）
jwt.secret=pS+u0pXAanWF3S7OwEy8ickxMJDOLXMaQpj8lcMZbDw=
jwt.expiration=86400000

# 串口通信配置
# Windows: COM1, COM2, COM3...
# Linux: /dev/ttyUSB0, /dev/ttyUSB1, /dev/ttysWK2, /dev/ttysWK3...
serial.port.name=/dev/ttysWK2
serial.port.auto-detect=true
serial.port.baud-rate=115200
serial.port.data-bits=8
serial.port.stop-bits=1
serial.port.parity=0
serial.port.timeout=5000

# 双层串口配置
serial.port.upper=/dev/ttysWK2
serial.port.lower=/dev/ttysWK3

# 硬件通信模式配置
# 可选值: websocket (远程硬件) 或 serial (串口直连)
hardware.communication.mode=websocket

# 串口通信详细配置
hardware.serial.enabled=true
hardware.serial.dev-mode=false
hardware.serial.upper-port=/dev/ttysWK2
hardware.serial.upper-baud-rate=115200
hardware.serial.upper-data-bits=8
hardware.serial.upper-stop-bits=1
hardware.serial.upper-parity=NONE
hardware.serial.lower-port=/dev/ttysWK3
hardware.serial.lower-baud-rate=115200
hardware.serial.lower-data-bits=8
hardware.serial.lower-stop-bits=1
hardware.serial.lower-parity=NONE
hardware.serial.connection-timeout=5000
hardware.serial.read-timeout=3000
hardware.serial.write-timeout=3000
hardware.serial.auto-reconnect=true
hardware.serial.max-retry-attempts=3

# 硬件模拟器配置（开发和测试环境使用）
hardware.simulator.enabled=false

# WebSocket硬件通信配置
hardware.websocket.url=ws://122.51.229.122:6123
hardware.websocket.timeout=30000
hardware.websocket.reconnect.enabled=true
hardware.websocket.reconnect.interval=2000
hardware.websocket.reconnect.max-attempts=1

# 治疗头自动同步配置
treatment-head.sync.enabled=true
treatment-head.sync.interval=10000
treatment-head.sync.startup-delay=5000
treatment-head.sync.timeout=5000
treatment-head.sync.retry-attempts=3
treatment-head.sync.retry-delay=2000

# 连接管理配置
serial.connection.health-check.interval=30000
serial.connection.max-retry-attempts=5
serial.connection.retry-delay=2000

# 性能监控配置
hardware.performance.metrics-report.interval=60000
hardware.performance.cleanup.interval=10000

# 跨域配置
cors.allowed-origins=http://localhost:3000,http://localhost:8081,http://localhost:5173
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*

# 字符编码配置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
