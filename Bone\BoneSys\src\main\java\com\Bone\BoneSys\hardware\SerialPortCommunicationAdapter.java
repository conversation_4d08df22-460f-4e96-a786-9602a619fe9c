package com.Bone.BoneSys.hardware;

import com.Bone.BoneSys.config.SerialPortConfiguration;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.service.HardwareCommunicationInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 串口通信适配器
 * 实现与上下层治疗仓的串口通信
 */
@Component
@ConditionalOnProperty(name = "hardware.communication.mode", havingValue = "serial")
public class SerialPortCommunicationAdapter implements HardwareCommunicationInterface {
    
    private static final Logger logger = LoggerFactory.getLogger(SerialPortCommunicationAdapter.class);
    
    @Autowired
    private SerialPortConfiguration serialConfig;
    
    // 串口连接管理
    private final ConcurrentHashMap<String, SerialPortConnection> connections = new ConcurrentHashMap<>();
    
    // 连接状态
    private volatile boolean isInitialized = false;
    
    /**
     * 串口连接封装类
     */
    private static class SerialPortConnection {
        private final String portName;
        private final int baudRate;
        private boolean isConnected;
        private long lastUsedTime;
        
        public SerialPortConnection(String portName, int baudRate) {
            this.portName = portName;
            this.baudRate = baudRate;
            this.isConnected = false;
            this.lastUsedTime = System.currentTimeMillis();
        }
        
        public String getPortName() { return portName; }
        public int getBaudRate() { return baudRate; }
        public boolean isConnected() { return isConnected; }
        public void setConnected(boolean connected) { this.isConnected = connected; }
        public long getLastUsedTime() { return lastUsedTime; }
        public void updateLastUsedTime() { this.lastUsedTime = System.currentTimeMillis(); }
    }
    
    @PostConstruct
    public void initialize() {
        try {
            logger.info("初始化串口通信适配器...");
            
            // 验证配置
            if (!serialConfig.isConfigurationValid()) {
                throw new SerialCommunicationException("串口配置无效");
            }
            
            // 初始化串口连接
            initializeSerialConnections();
            
            isInitialized = true;
            logger.info("串口通信适配器初始化完成: {}", serialConfig.getConfigurationSummary());
            
        } catch (Exception e) {
            logger.error("串口通信适配器初始化失败", e);
            throw new RuntimeException("串口通信适配器初始化失败", e);
        }
    }
    
    @PreDestroy
    public void cleanup() {
        logger.info("清理串口通信适配器...");
        
        // 关闭所有串口连接
        for (SerialPortConnection connection : connections.values()) {
            try {
                closeSerialConnection(connection);
            } catch (Exception e) {
                logger.warn("关闭串口连接失败: {}", connection.getPortName(), e);
            }
        }
        
        connections.clear();
        isInitialized = false;
        logger.info("串口通信适配器清理完成");
    }
    
    @Override
    public String sendCommand(String command) throws SerialCommunicationException {
        if (!isInitialized) {
            throw new SerialCommunicationException("串口通信适配器未初始化");
        }
        
        // 根据指令类型确定使用的串口
        String portName = determinePortForCommand(command);
        
        try {
            // 获取或创建串口连接
            SerialPortConnection connection = getOrCreateConnection(portName);
            
            // 发送指令并接收响应
            return sendCommandToPort(connection, command);
            
        } catch (Exception e) {
            logger.error("发送串口指令失败: command={}, port={}", command, portName, e);
            throw new SerialCommunicationException("发送串口指令失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isConnected() {
        if (!isInitialized) {
            return false;
        }
        
        // 检查所有串口连接状态
        return connections.values().stream().anyMatch(SerialPortConnection::isConnected);
    }
    
    @Override
    public void disconnect() {
        logger.info("断开所有串口连接...");
        
        for (SerialPortConnection connection : connections.values()) {
            try {
                closeSerialConnection(connection);
            } catch (Exception e) {
                logger.warn("断开串口连接失败: {}", connection.getPortName(), e);
            }
        }
    }
    
    @Override
    public String sendCommand(String command, String layer) throws SerialCommunicationException {
        if (!isInitialized) {
            throw new SerialCommunicationException("串口通信适配器未初始化");
        }

        // 根据层级确定使用的串口
        String portName = "upper".equals(layer) ? serialConfig.getUpperPort() : serialConfig.getLowerPort();

        try {
            // 获取或创建串口连接
            SerialPortConnection connection = getOrCreateConnection(portName);

            // 发送指令并接收响应
            return sendCommandToPort(connection, command);

        } catch (Exception e) {
            logger.error("发送串口指令失败: command={}, layer={}, port={}", command, layer, portName, e);
            throw new SerialCommunicationException("发送串口指令失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void connect() throws SerialCommunicationException {
        if (!isInitialized) {
            initialize();
        } else {
            // 重新连接
            disconnect();
            try {
                Thread.sleep(1000); // 等待1秒后重连
                initializeSerialConnections();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new SerialCommunicationException("重连过程被中断", e);
            }
        }
    }

    @Override
    public String getCommunicationType() {
        return "SERIAL_PORT: " + serialConfig.getConfigurationSummary();
    }
    
    /**
     * 初始化串口连接
     */
    private void initializeSerialConnections() throws SerialCommunicationException {
        try {
            // 初始化上层治疗仓串口
            SerialPortConnection upperConnection = new SerialPortConnection(
                serialConfig.getUpperPort(), serialConfig.getUpperBaudRate());
            connectSerialPort(upperConnection);
            connections.put("upper", upperConnection);
            
            // 初始化下层治疗仓串口
            SerialPortConnection lowerConnection = new SerialPortConnection(
                serialConfig.getLowerPort(), serialConfig.getLowerBaudRate());
            connectSerialPort(lowerConnection);
            connections.put("lower", lowerConnection);
            
            logger.info("串口连接初始化完成: 上层={}, 下层={}", 
                       serialConfig.getUpperPort(), serialConfig.getLowerPort());
            
        } catch (Exception e) {
            throw new SerialCommunicationException("初始化串口连接失败", e);
        }
    }
    
    /**
     * 连接串口
     */
    private void connectSerialPort(SerialPortConnection connection) throws SerialCommunicationException {
        try {
            logger.info("连接串口: {} @ {}", connection.getPortName(), connection.getBaudRate());
            
            // 这里应该实现实际的串口连接逻辑
            // 由于没有实际的串口库，这里模拟连接成功
            connection.setConnected(true);
            
            logger.info("串口连接成功: {}", connection.getPortName());
            
        } catch (Exception e) {
            throw new SerialCommunicationException("连接串口失败: " + connection.getPortName(), e);
        }
    }
    
    /**
     * 关闭串口连接
     */
    private void closeSerialConnection(SerialPortConnection connection) {
        try {
            if (connection.isConnected()) {
                logger.info("关闭串口连接: {}", connection.getPortName());
                
                // 这里应该实现实际的串口关闭逻辑
                connection.setConnected(false);
                
                logger.info("串口连接已关闭: {}", connection.getPortName());
            }
        } catch (Exception e) {
            logger.warn("关闭串口连接失败: {}", connection.getPortName(), e);
        }
    }
    
    /**
     * 根据指令确定使用的串口
     */
    private String determinePortForCommand(String command) {
        // 解析指令中的治疗头编号
        if (command.startsWith("TWZS") || command.startsWith("TWZO") || command.startsWith("TWSDT")) {
            try {
                // 提取治疗头编号（通常在第4-5位）
                int headNumber = Integer.parseInt(command.substring(4, 6));
                return serialConfig.getPortForTreatmentHead(headNumber);
            } catch (Exception e) {
                logger.warn("无法解析指令中的治疗头编号: {}", command);
            }
        }
        
        // 默认使用上层串口
        return serialConfig.getUpperPort();
    }
    
    /**
     * 获取或创建串口连接
     */
    private SerialPortConnection getOrCreateConnection(String portName) throws SerialCommunicationException {
        // 根据端口名确定连接类型
        String connectionKey = portName.equals(serialConfig.getUpperPort()) ? "upper" : "lower";
        
        SerialPortConnection connection = connections.get(connectionKey);
        if (connection == null || !connection.isConnected()) {
            // 重新创建连接
            if (connectionKey.equals("upper")) {
                connection = new SerialPortConnection(serialConfig.getUpperPort(), serialConfig.getUpperBaudRate());
            } else {
                connection = new SerialPortConnection(serialConfig.getLowerPort(), serialConfig.getLowerBaudRate());
            }
            
            connectSerialPort(connection);
            connections.put(connectionKey, connection);
        }
        
        connection.updateLastUsedTime();
        return connection;
    }
    
    /**
     * 向指定串口发送指令
     */
    private String sendCommandToPort(SerialPortConnection connection, String command) throws SerialCommunicationException {
        try {
            logger.debug("向串口 {} 发送指令: {}", connection.getPortName(), command);
            
            // 这里应该实现实际的串口通信逻辑
            // 模拟发送指令和接收响应
            String response = simulateSerialCommunication(command);
            
            logger.debug("从串口 {} 接收响应: {}", connection.getPortName(), response);
            return response;
            
        } catch (Exception e) {
            throw new SerialCommunicationException("串口通信失败: " + connection.getPortName(), e);
        }
    }
    
    /**
     * 模拟串口通信（用于测试）
     */
    private String simulateSerialCommunication(String command) {
        // 模拟不同指令的响应
        if (command.startsWith("TRZI")) {
            return "TRZI10010110020110030110040110050110060110070110080110090110100110\r\n";
        } else if (command.startsWith("TWSC")) {
            return "TWSC1\r\n";
        } else if (command.startsWith("TWSN")) {
            return "TWSN1\r\n";
        } else if (command.startsWith("TWZS")) {
            return command.replace("TWZS", "TWZS") + "\r\n";
        } else if (command.startsWith("TWZO")) {
            return command.replace("TWZO", "TWZO") + "\r\n";
        } else if (command.startsWith("TWSDT")) {
            return command.replace("TWSDT", "TWSDT") + "\r\n";
        } else {
            return "OK\r\n";
        }
    }
}
