# 🧪 真实硬件连接测试指南

## 测试前准备

### 1. 硬件设备检查
- [ ] 确保硬件设备已连接并启动
- [ ] 确认WebSocket服务器运行在 `ws://122.51.229.122:6123`
- [ ] 治疗头已正确放置在治疗仓中
- [ ] 治疗头电量充足（建议 > 50%）

### 2. 软件环境准备
- [ ] 后端Spring Boot应用已启动
- [ ] 数据库连接正常
- [ ] WebSocket硬件服务已初始化

## 测试步骤

### 第一阶段：基础连接测试

#### 1. WebSocket连接测试
```bash
# 运行连接测试
./gradlew test --tests WebSocketHardwareTest.testWebSocketConnection
```

**预期结果：**
- WebSocket连接状态显示"已连接"
- 无连接异常或超时错误

#### 2. TRZI指令测试 - 查询治疗头信息
```bash
# 运行TRZI测试
./gradlew test --tests WebSocketHardwareTest.testTRZICommand
```

**预期结果：**
- 成功发送 `TRZI\r\n` 指令
- 收到格式正确的响应：`TRZI+治疗头数量+治疗头数据\r\n`
- 解析出至少1个治疗头信息
- 显示治疗头编号、电量、槽位信息

### 第二阶段：指示灯控制测试

#### 3. TWSC指令测试 - 点亮治疗头指示灯
```bash
# 运行TWSC测试
./gradlew test --tests WebSocketHardwareTest.testTWSCCommand
```

**预期结果：**
- 成功发送 `TWSC+数量+治疗头编号+颜色` 指令
- 治疗头1显示红色指示灯
- 治疗头2显示绿色指示灯
- 收到正确的响应确认

#### 4. TWSN指令测试 - 关闭治疗头指示灯
```bash
# 运行TWSN测试
./gradlew test --tests WebSocketHardwareTest.testTWSNCommand
```

**预期结果：**
- 成功发送 `TWSN+数量+治疗头编号` 指令
- 治疗头指示灯熄灭
- 收到正确的响应确认

### 第三阶段：治疗参数测试

#### 5. TWSDT指令测试 - 发送治疗参数（不启动）
```bash
# 运行TWSDT测试
./gradlew test --tests WebSocketHardwareTest.testTWSDTCommand
```

**预期结果：**
- 成功发送 `TWSDT+时间+声强+F+频率+ID+数量+治疗头编号` 指令
- 治疗头接收到参数但不启动治疗
- 收到参数确认响应

#### 6. TWZS指令测试 - 发送治疗参数并启动治疗
```bash
# 运行TWZS测试（谨慎执行）
./gradlew test --tests WebSocketHardwareTest.testTWZSCommand
```

**⚠️ 安全警告：此测试将启动真实的治疗过程！**

**预期结果：**
- 成功发送 `TWZS+治疗头编号+时间+声强+频率` 指令
- 治疗头开始工作（可能有声音或振动）
- 10秒后自动发送TWZO指令停止治疗
- 治疗头停止工作

#### 7. TWZO指令测试 - 关闭治疗头
```bash
# 运行TWZO测试
./gradlew test --tests WebSocketHardwareTest.testTWZOCommand
```

**预期结果：**
- 成功发送 `TWZO+治疗头编号` 指令
- 治疗头停止工作
- 收到停止确认响应

### 第四阶段：完整流程测试

#### 8. 完整工作流程测试
```bash
# 运行完整流程测试
./gradlew test --tests WebSocketHardwareTest.testCompleteWorkflow
```

**预期结果：**
1. 查询治疗头信息成功
2. 点亮推荐治疗头指示灯
3. 发送治疗参数成功
4. 关闭指示灯成功

## 测试结果记录

### 测试环境信息
- 测试日期：`____年__月__日`
- 硬件设备型号：`________________`
- WebSocket服务器地址：`ws://122.51.229.122:6123`
- 软件版本：`________________`

### 测试结果
| 测试项目 | 状态 | 备注 |
|---------|------|------|
| WebSocket连接 | ⬜ 通过 / ⬜ 失败 | |
| TRZI查询指令 | ⬜ 通过 / ⬜ 失败 | 找到__个治疗头 |
| TWSC指示灯点亮 | ⬜ 通过 / ⬜ 失败 | |
| TWSN指示灯关闭 | ⬜ 通过 / ⬜ 失败 | |
| TWSDT参数发送 | ⬜ 通过 / ⬜ 失败 | |
| TWZS启动治疗 | ⬜ 通过 / ⬜ 失败 | |
| TWZO关闭治疗 | ⬜ 通过 / ⬜ 失败 | |
| 完整流程测试 | ⬜ 通过 / ⬜ 失败 | |

### 问题记录
如果测试失败，请记录以下信息：
1. 错误信息：`________________________________`
2. 发生时间：`________________________________`
3. 硬件状态：`________________________________`
4. 解决方案：`________________________________`

## 故障排除

### 常见问题

#### 1. WebSocket连接失败
**可能原因：**
- 硬件设备未启动
- 网络连接问题
- WebSocket服务器地址错误

**解决方案：**
- 检查硬件设备电源和网络连接
- 确认WebSocket服务器地址：`ws://122.51.229.122:6123`
- 检查防火墙设置

#### 2. 治疗头查询失败
**可能原因：**
- 治疗头未正确放置
- 治疗头电量不足
- 硬件通信故障

**解决方案：**
- 重新放置治疗头到治疗仓
- 检查治疗头电量
- 重启硬件设备

#### 3. 指示灯不亮
**可能原因：**
- 治疗头编号错误
- 颜色代码不支持
- 硬件故障

**解决方案：**
- 确认治疗头编号正确
- 使用标准颜色代码（1=红色，2=绿色，3=蓝色）
- 检查硬件指示灯功能

#### 4. 治疗启动失败
**可能原因：**
- 治疗参数超出范围
- 治疗头状态异常
- 安全保护机制触发

**解决方案：**
- 检查治疗参数范围（时间1-99分钟，声强1-999mW/cm²）
- 确认治疗头处于可用状态
- 检查安全保护设置

## 测试完成确认

- [ ] 所有基础功能测试通过
- [ ] 硬件指令响应正常
- [ ] 治疗头状态同步正确
- [ ] 安全保护机制有效
- [ ] 测试结果已记录

**测试负责人签名：** `________________`
**测试完成时间：** `____年__月__日 __:__`

---

## 下一步：全链路功能验证

测试通过后，可以进行前后端与硬件的全链路连通测试：
1. 启动前端Vue应用
2. 启动后端Spring Boot应用
3. 通过前端界面进行完整的治疗流程操作
4. 验证前端→后端→硬件的完整数据流
