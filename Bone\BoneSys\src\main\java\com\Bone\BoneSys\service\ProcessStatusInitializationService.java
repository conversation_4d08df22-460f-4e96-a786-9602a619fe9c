package com.Bone.BoneSys.service;

import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.repository.ProcessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 进程状态初始化服务
 * 负责在应用启动时检测和更新进程状态
 */
@Service
public class ProcessStatusInitializationService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProcessStatusInitializationService.class);
    
    @Autowired
    private ProcessRepository processRepository;
    
    /**
     * 检测并更新进行中的进程状态
     * 在应用启动时调用，将所有进行中的进程状态改为完成状态
     */
    @Transactional
    public void checkAndUpdateInProgressProcesses() {
        logger.info("开始检测进程表中的进行中进程...");
        
        try {
            // 查找所有进行中的进程
            List<Process> inProgressProcesses = processRepository.findByStatus(ProcessStatus.IN_PROGRESS);
            
            if (inProgressProcesses.isEmpty()) {
                logger.info("未发现进行中的进程，无需更新");
                return;
            }
            
            logger.info("发现 {} 个进行中的进程，开始更新为完成状态", inProgressProcesses.size());
            
            int updatedCount = 0;
            for (Process process : inProgressProcesses) {
                try {
                    // 将进程状态改为完成状态
                    process.setStatus(ProcessStatus.COMPLETED);
                    
                    // 设置结束时间为当前时间
                    if (process.getEndTime() == null) {
                        process.setEndTime(LocalDateTime.now());
                    }
                    
                    // 保存更新
                    processRepository.save(process);
                    updatedCount++;
                    
                    logger.debug("进程 ID {} 状态已更新为完成状态", process.getId());
                    
                } catch (Exception e) {
                    logger.error("更新进程 ID {} 状态失败: {}", process.getId(), e.getMessage());
                }
            }
            
            logger.info("进程状态更新完成，成功更新 {} 个进程", updatedCount);
            
        } catch (Exception e) {
            logger.error("检测和更新进程状态时发生异常", e);
            throw new RuntimeException("进程状态初始化失败", e);
        }
    }
    
    /**
     * 获取进程状态统计信息
     */
    public void logProcessStatusStatistics() {
        try {
            List<Object[]> statusCounts = processRepository.countByStatus();
            
            logger.info("=== 进程状态统计 ===");
            for (Object[] statusCount : statusCounts) {
                ProcessStatus status = (ProcessStatus) statusCount[0];
                Long count = (Long) statusCount[1];
                logger.info("状态: {} - 数量: {}", status, count);
            }
            logger.info("==================");
            
        } catch (Exception e) {
            logger.error("获取进程状态统计信息失败", e);
        }
    }
}
