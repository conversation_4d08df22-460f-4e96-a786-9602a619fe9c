package com.Bone.BoneSys.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 硬件通信配置类
 * 支持WebSocket和串口两种通信模式的配置
 */
@Configuration
@ConfigurationProperties(prefix = "hardware.communication")
public class HardwareCommunicationConfig {
    
    /**
     * 通信模式：websocket 或 serial
     */
    private String mode = "websocket";
    
    /**
     * WebSocket配置
     */
    private WebSocketConfig websocket = new WebSocketConfig();
    
    /**
     * 串口配置
     */
    private SerialConfig serial = new SerialConfig();
    
    // Getters and Setters
    public String getMode() {
        return mode;
    }
    
    public void setMode(String mode) {
        this.mode = mode;
    }
    
    public WebSocketConfig getWebsocket() {
        return websocket;
    }
    
    public void setWebsocket(WebSocketConfig websocket) {
        this.websocket = websocket;
    }
    
    public SerialConfig getSerial() {
        return serial;
    }
    
    public void setSerial(SerialConfig serial) {
        this.serial = serial;
    }
    
    /**
     * 判断是否为WebSocket模式
     */
    public boolean isWebSocketMode() {
        return "websocket".equalsIgnoreCase(mode);
    }
    
    /**
     * 判断是否为串口模式
     */
    public boolean isSerialMode() {
        return "serial".equalsIgnoreCase(mode);
    }
    
    /**
     * WebSocket配置类
     */
    public static class WebSocketConfig {
        private String url = "ws://localhost:8081/hardware";
        private boolean reconnect = true;
        private int timeout = 5000;
        private int maxReconnectAttempts = 10;
        private int reconnectInterval = 3000;
        
        // Getters and Setters
        public String getUrl() {
            return url;
        }
        
        public void setUrl(String url) {
            this.url = url;
        }
        
        public boolean isReconnect() {
            return reconnect;
        }
        
        public void setReconnect(boolean reconnect) {
            this.reconnect = reconnect;
        }
        
        public int getTimeout() {
            return timeout;
        }
        
        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
        
        public int getMaxReconnectAttempts() {
            return maxReconnectAttempts;
        }
        
        public void setMaxReconnectAttempts(int maxReconnectAttempts) {
            this.maxReconnectAttempts = maxReconnectAttempts;
        }
        
        public int getReconnectInterval() {
            return reconnectInterval;
        }
        
        public void setReconnectInterval(int reconnectInterval) {
            this.reconnectInterval = reconnectInterval;
        }
    }
    
    /**
     * 串口配置类
     */
    public static class SerialConfig {
        private String port = "COM3";
        private int baudRate = 115200;
        private int dataBits = 8;
        private int stopBits = 1;
        private String parity = "NONE";
        private int timeout = 3000;
        private boolean autoDetectPort = true;
        
        // Getters and Setters
        public String getPort() {
            return port;
        }
        
        public void setPort(String port) {
            this.port = port;
        }
        
        public int getBaudRate() {
            return baudRate;
        }
        
        public void setBaudRate(int baudRate) {
            this.baudRate = baudRate;
        }
        
        public int getDataBits() {
            return dataBits;
        }
        
        public void setDataBits(int dataBits) {
            this.dataBits = dataBits;
        }
        
        public int getStopBits() {
            return stopBits;
        }
        
        public void setStopBits(int stopBits) {
            this.stopBits = stopBits;
        }
        
        public String getParity() {
            return parity;
        }
        
        public void setParity(String parity) {
            this.parity = parity;
        }
        
        public int getTimeout() {
            return timeout;
        }
        
        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
        
        public boolean isAutoDetectPort() {
            return autoDetectPort;
        }
        
        public void setAutoDetectPort(boolean autoDetectPort) {
            this.autoDetectPort = autoDetectPort;
        }
    }
    
    @Override
    public String toString() {
        return String.format("HardwareCommunicationConfig{mode='%s', websocket=%s, serial=%s}", 
                           mode, websocket.url, serial.port);
    }
}
