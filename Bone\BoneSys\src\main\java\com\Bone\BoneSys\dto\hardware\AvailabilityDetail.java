package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗头可用性详细信息
 * 提供分类的可用性统计数据
 */
public class AvailabilityDetail {
    
    private int shallowAvailable;    // 浅部可用数量
    private int shallowRequired;     // 浅部需求数量
    private boolean shallowSufficient; // 浅部是否充足
    
    private int deepAvailable;       // 深部可用数量
    private int deepRequired;        // 深部需求数量
    private boolean deepSufficient;  // 深部是否充足
    
    private String detailedMessage;  // 详细说明
    
    public AvailabilityDetail() {}
    
    public AvailabilityDetail(int shallowAvailable, int shallowRequired, boolean shallowSufficient,
                             int deepAvailable, int deepRequired, boolean deepSufficient,
                             String detailedMessage) {
        this.shallowAvailable = shallowAvailable;
        this.shallowRequired = shallowRequired;
        this.shallowSufficient = shallowSufficient;
        this.deepAvailable = deepAvailable;
        this.deepRequired = deepRequired;
        this.deepSufficient = deepSufficient;
        this.detailedMessage = detailedMessage;
    }
    
    // Getters and Setters
    public int getShallowAvailable() {
        return shallowAvailable;
    }
    
    public void setShallowAvailable(int shallowAvailable) {
        this.shallowAvailable = shallowAvailable;
    }
    
    public int getShallowRequired() {
        return shallowRequired;
    }
    
    public void setShallowRequired(int shallowRequired) {
        this.shallowRequired = shallowRequired;
    }
    
    public boolean isShallowSufficient() {
        return shallowSufficient;
    }
    
    public void setShallowSufficient(boolean shallowSufficient) {
        this.shallowSufficient = shallowSufficient;
    }
    
    public int getDeepAvailable() {
        return deepAvailable;
    }
    
    public void setDeepAvailable(int deepAvailable) {
        this.deepAvailable = deepAvailable;
    }
    
    public int getDeepRequired() {
        return deepRequired;
    }
    
    public void setDeepRequired(int deepRequired) {
        this.deepRequired = deepRequired;
    }
    
    public boolean isDeepSufficient() {
        return deepSufficient;
    }
    
    public void setDeepSufficient(boolean deepSufficient) {
        this.deepSufficient = deepSufficient;
    }
    
    public String getDetailedMessage() {
        return detailedMessage;
    }
    
    public void setDetailedMessage(String detailedMessage) {
        this.detailedMessage = detailedMessage;
    }
    
    /**
     * 检查整体是否充足
     */
    public boolean isOverallSufficient() {
        return shallowSufficient && deepSufficient;
    }
    
    /**
     * 获取总可用数量
     */
    public int getTotalAvailable() {
        return shallowAvailable + deepAvailable;
    }
    
    /**
     * 获取总需求数量
     */
    public int getTotalRequired() {
        return shallowRequired + deepRequired;
    }
    
    @Override
    public String toString() {
        return String.format("AvailabilityDetail{shallowAvailable=%d, shallowRequired=%d, shallowSufficient=%s, " +
                           "deepAvailable=%d, deepRequired=%d, deepSufficient=%s, detailedMessage='%s'}", 
                           shallowAvailable, shallowRequired, shallowSufficient,
                           deepAvailable, deepRequired, deepSufficient, detailedMessage);
    }
}