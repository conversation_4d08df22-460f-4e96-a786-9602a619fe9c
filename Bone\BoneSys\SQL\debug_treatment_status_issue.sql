-- ========================================
-- 调试治疗状态更新问题
-- 分析数据不一致和状态更新失败的原因
-- ========================================

USE bonesys;

-- 1. 检查当前进程和治疗详情的状态
SELECT '=== 当前进程状态 ===' as message;
SELECT 
    p.id as process_id,
    p.status as process_status,
    p.treatment_mode,
    p.start_time,
    TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) as elapsed_minutes,
    pat.name as patient_name,
    r.record_number
FROM processes p
JOIN records r ON p.record_id = r.id
JOIN patients pat ON r.patient_id = pat.id
WHERE p.status = 'IN_PROGRESS'
ORDER BY p.id DESC;

-- 2. 检查对应的治疗详情
SELECT '=== 治疗详情状态 ===' as message;
SELECT 
    td.id as detail_id,
    td.process_id,
    td.body_part,
    td.head_number_used,
    td.duration as duration_minutes,
    td.status,
    p.start_time,
    TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) as elapsed_minutes,
    (td.duration - TIMESTAMPDIFF(MINUTE, p.start_time, NOW())) as remaining_minutes
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE p.status = 'IN_PROGRESS'
ORDER BY td.process_id, td.id;

-- 3. 检查应该更新但没有更新的治疗详情
SELECT '=== 应该更新状态的治疗详情 ===' as message;
SELECT 
    td.id as detail_id,
    td.process_id,
    td.body_part,
    td.duration as duration_minutes,
    td.status as current_status,
    TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) as elapsed_minutes,
    CASE 
        WHEN p.treatment_mode = 'ON_SITE' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration 
        THEN 'SHOULD_BE_COMPLETED'
        WHEN p.treatment_mode = 'TAKE_AWAY' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15)
        THEN 'SHOULD_BE_AWAITING_RETURN'
        ELSE 'NO_UPDATE_NEEDED'
    END as should_be_status
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE td.status = 'TREATING'
  AND p.status = 'IN_PROGRESS'
ORDER BY td.process_id, td.id;

-- 4. 检查重复的治疗详情
SELECT '=== 重复的治疗详情 ===' as message;
SELECT 
    process_id,
    body_part,
    COUNT(*) as count,
    GROUP_CONCAT(id) as detail_ids,
    GROUP_CONCAT(status) as statuses
FROM treatment_details
WHERE process_id IN (
    SELECT id FROM processes WHERE status = 'IN_PROGRESS'
)
GROUP BY process_id, body_part
HAVING COUNT(*) > 1
ORDER BY process_id, body_part;

-- 5. 检查进程和详情的数量不匹配
SELECT '=== 进程详情数量统计 ===' as message;
SELECT 
    p.id as process_id,
    p.status as process_status,
    COUNT(td.id) as total_details,
    SUM(CASE WHEN td.status = 'TREATING' THEN 1 ELSE 0 END) as treating_count,
    SUM(CASE WHEN td.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_count,
    SUM(CASE WHEN td.status = 'TERMINATED' THEN 1 ELSE 0 END) as terminated_count,
    SUM(CASE WHEN td.status = 'AWAITING_RETURN' THEN 1 ELSE 0 END) as awaiting_return_count
FROM processes p
LEFT JOIN treatment_details td ON p.id = td.process_id
WHERE p.status = 'IN_PROGRESS'
GROUP BY p.id, p.status
ORDER BY p.id;

-- 6. 检查最近的治疗详情创建时间
SELECT '=== 最近创建的治疗详情 ===' as message;
SELECT 
    td.id,
    td.process_id,
    td.body_part,
    td.status,
    td.head_number_used,
    p.start_time as process_start_time
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE td.id >= 55  -- 查看最近的记录
ORDER BY td.id DESC;

-- 7. 手动更新过期的治疗详情状态（仅用于测试）
SELECT '=== 准备手动更新过期状态 ===' as message;
SELECT 
    td.id,
    td.status as current_status,
    CASE 
        WHEN p.treatment_mode = 'ON_SITE' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration 
        THEN 'COMPLETED'
        WHEN p.treatment_mode = 'TAKE_AWAY' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15)
        THEN 'AWAITING_RETURN'
        ELSE td.status
    END as new_status
FROM treatment_details td
JOIN processes p ON td.process_id = p.id
WHERE td.status = 'TREATING'
  AND p.status = 'IN_PROGRESS'
  AND (
    (p.treatment_mode = 'ON_SITE' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration) OR
    (p.treatment_mode = 'TAKE_AWAY' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15))
  );

-- 8. 实际执行状态更新（取消注释以执行）
/*
UPDATE treatment_details td
JOIN processes p ON td.process_id = p.id
SET td.status = CASE 
    WHEN p.treatment_mode = 'ON_SITE' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration 
    THEN 'COMPLETED'
    WHEN p.treatment_mode = 'TAKE_AWAY' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15)
    THEN 'AWAITING_RETURN'
    ELSE td.status
END
WHERE td.status = 'TREATING'
  AND p.status = 'IN_PROGRESS'
  AND (
    (p.treatment_mode = 'ON_SITE' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= td.duration) OR
    (p.treatment_mode = 'TAKE_AWAY' AND TIMESTAMPDIFF(MINUTE, p.start_time, NOW()) >= (td.duration + 15))
  );
*/

SELECT '=== 调试完成 ===' as message;
