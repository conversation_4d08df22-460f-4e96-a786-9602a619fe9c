-- 修改治疗仓槽号逻辑 - 简化版
-- 上下层都是1-10，分别对应浅部和深部

USE bonesys;

-- 1. 修改表结构注释
ALTER TABLE treatment_heads 
MODIFY COLUMN slot_number INT NULL DEFAULT NULL COMMENT '槽位号 (上层1-10浅部, 下层1-10深部)';

-- 2. 添加仓位类型字段
ALTER TABLE treatment_heads 
ADD COLUMN compartment_type VARCHAR(10) NULL DEFAULT NULL COMMENT '仓位类型: SHALLOW(浅部上层), DEEP(深部下层)' 
AFTER slot_number;

-- 3. 更新现有数据的仓位类型
-- 治疗头1-10为浅部，11-20为深部
UPDATE treatment_heads 
SET compartment_type = 'SHALLOW' 
WHERE head_number BETWEEN 1 AND 10;

UPDATE treatment_heads 
SET compartment_type = 'DEEP' 
WHERE head_number BETWEEN 11 AND 20;

-- 4. 重置槽号为NULL（由硬件查询时动态设置）
UPDATE treatment_heads 
SET slot_number = NULL;

-- 5. 创建索引
CREATE INDEX idx_compartment_type ON treatment_heads (compartment_type);
CREATE INDEX idx_compartment_slot ON treatment_heads (compartment_type, slot_number);

-- 6. 验证数据
SELECT 
    head_number as '治疗头编号',
    slot_number as '槽号',
    compartment_type as '仓位类型',
    CASE 
        WHEN head_number BETWEEN 1 AND 10 THEN '浅部治疗头'
        WHEN head_number BETWEEN 11 AND 20 THEN '深部治疗头'
        ELSE '未知类型'
    END as '头类型',
    realtime_status as '状态',
    battery_level as '电量'
FROM treatment_heads 
ORDER BY head_number;

-- 7. 显示修改后的表结构
DESCRIBE treatment_heads;
