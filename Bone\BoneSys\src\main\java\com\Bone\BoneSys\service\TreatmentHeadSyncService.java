package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.dto.sync.SyncResult;
import com.Bone.BoneSys.dto.sync.SyncStatus;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 治疗头自动同步服务
 * 
 * 功能：
 * 1. 应用启动时初始化治疗头数据
 * 2. 定期同步治疗头状态到数据库
 * 3. 提供同步状态监控和手动触发功能
 * 
 * 配置参数：
 * - treatment-head.sync.enabled: 是否启用定时同步（默认true）
 * - treatment-head.sync.interval: 同步间隔毫秒数（默认10000ms）
 * - treatment-head.sync.startup-delay: 启动延迟毫秒数（默认5000ms）
 */
@Service
public class TreatmentHeadSyncService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadSyncService.class);
    
    // 配置参数
    @Value("${treatment-head.sync.enabled:true}")
    private boolean syncEnabled;
    
    @Value("${treatment-head.sync.interval:10000}")
    private long syncInterval;
    
    @Value("${treatment-head.sync.startup-delay:5000}")
    private long startupDelay;
    
    // 依赖服务
    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;

    @Autowired
    private TreatmentHeadAbnormalDetectionService abnormalDetectionService;
    
    // 同步状态跟踪
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong totalSyncCount = new AtomicLong(0);
    private final AtomicLong successfulSyncCount = new AtomicLong(0);
    private final AtomicLong failedSyncCount = new AtomicLong(0);
    
    private volatile LocalDateTime lastSyncTime;
    private volatile boolean lastSyncSuccess = false;
    private volatile String lastErrorMessage;
    private volatile long lastExecutionTimeMs = 0;
    
    /**
     * 应用启动时初始化治疗头数据
     */
    @PostConstruct
    public void initializeOnStartup() {
        if (!syncEnabled) {
            logger.info("Treatment head sync is disabled, skipping startup initialization");
            return;
        }
        
        logger.info("Initializing treatment head data on application startup...");
        
        try {
            // 延迟启动，确保其他服务已经初始化完成
            Thread.sleep(startupDelay);
            
            // 执行初始同步
            SyncResult result = performSync();
            
            if (result.isSuccess()) {
                logger.info("Treatment head initialization completed successfully: {} heads processed", 
                           result.getTotalHeads());
            } else {
                logger.warn("Treatment head initialization failed: {}", result.getErrorMessage());
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Treatment head initialization was interrupted");
        } catch (Exception e) {
            logger.error("Unexpected error during treatment head initialization", e);
        }
    }
    
    /**
     * 定时同步任务
     * 每隔配置的间隔时间执行一次同步
     */
    @Scheduled(fixedDelayString = "${treatment-head.sync.interval:10000}")
    public void scheduledSync() {
        if (!syncEnabled) {
            return;
        }
        
        if (isRunning.get()) {
            logger.debug("Previous sync is still running, skipping this cycle");
            return;
        }
        
        logger.debug("Starting scheduled treatment head sync...");
        
        try {
            SyncResult result = performSync();
            
            if (result.isSuccess()) {
                logger.debug("Scheduled sync completed successfully: {} heads processed in {}ms", 
                           result.getTotalHeads(), result.getExecutionTimeMs());
            } else {
                logger.warn("Scheduled sync failed: {}", result.getErrorMessage());
            }
            
        } catch (Exception e) {
            logger.error("Unexpected error during scheduled sync", e);
            updateSyncStatus(false, "Unexpected error: " + e.getMessage(), 0, 0, 0);
        }
    }
    
    /**
     * 执行同步操作
     * 
     * @return 同步结果
     */
    public SyncResult performSync() {
        if (!isRunning.compareAndSet(false, true)) {
            return SyncResult.failure("Sync is already running");
        }
        
        long startTime = System.currentTimeMillis();
        totalSyncCount.incrementAndGet();
        
        try {
            logger.debug("Starting treatment head sync...");
            
            // 调用硬件服务同步治疗头数据
            List<TreatmentHeadInfo> syncedHeads = hardwareService.syncAllTreatmentHeads();

            // 执行异常检测和通知
            abnormalDetectionService.processQueryResults(syncedHeads);

            // 将硬件数据更新到数据库
            int updatedCount = updateDatabaseFromHardwareData(syncedHeads);

            // 更新未查询到的治疗头状态为治疗中
            int treatingCount = updateMissingTreatmentHeadsToTreating(syncedHeads);

            long executionTime = System.currentTimeMillis() - startTime;
            
            // 更新同步状态
            updateSyncStatus(true, null, syncedHeads.size(), updatedCount, 0);
            successfulSyncCount.incrementAndGet();
            
            logger.info("Treatment head sync completed successfully: {} hardware heads, {} database records updated, {} heads set to treating in {}ms",
                        syncedHeads.size(), updatedCount, treatingCount, executionTime);
            
            return SyncResult.success(syncedHeads, executionTime);
            
        } catch (SerialCommunicationException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMessage = "Hardware communication failed: " + e.getMessage();
            
            updateSyncStatus(false, errorMessage, 0, 0, 0);
            failedSyncCount.incrementAndGet();
            
            logger.warn("Treatment head sync failed: {}", errorMessage);
            return SyncResult.failure(errorMessage);
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMessage = "Unexpected error: " + e.getMessage();
            
            updateSyncStatus(false, errorMessage, 0, 0, 0);
            failedSyncCount.incrementAndGet();
            
            logger.error("Treatment head sync failed with unexpected error", e);
            return SyncResult.failure(errorMessage);
            
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 获取同步状态
     * 
     * @return 当前同步状态
     */
    public SyncStatus getSyncStatus() {
        return new SyncStatus(
            lastSyncTime,
            lastSyncSuccess,
            totalSyncCount.get(),
            successfulSyncCount.get(),
            failedSyncCount.get(),
            calculateAverageExecutionTime(),
            getCurrentStatus(),
            lastErrorMessage
        );
    }
    
    /**
     * 手动触发同步
     * 
     * @return 同步结果
     */
    public SyncResult triggerManualSync() {
        logger.info("Manual sync triggered");
        return performSync();
    }
    
    /**
     * 应用关闭时的清理工作
     */
    @PreDestroy
    public void shutdown() {
        logger.info("Treatment head sync service is shutting down...");
        
        // 等待当前同步完成
        int maxWaitSeconds = 10;
        int waitCount = 0;
        
        while (isRunning.get() && waitCount < maxWaitSeconds) {
            try {
                Thread.sleep(1000);
                waitCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        if (isRunning.get()) {
            logger.warn("Sync service shutdown timeout, forcing stop");
        } else {
            logger.info("Treatment head sync service shutdown completed");
        }
    }
    
    /**
     * 更新同步状态
     */
    private void updateSyncStatus(boolean success, String errorMessage, 
                                 int totalHeads, int updatedRecords, int createdRecords) {
        this.lastSyncTime = LocalDateTime.now();
        this.lastSyncSuccess = success;
        this.lastErrorMessage = errorMessage;
        this.lastExecutionTimeMs = System.currentTimeMillis();
    }
    
    /**
     * 计算平均执行时间
     */
    private double calculateAverageExecutionTime() {
        // 简化实现，后续可以维护执行时间历史记录
        return lastExecutionTimeMs;
    }
    
    /**
     * 获取当前状态
     */
    private String getCurrentStatus() {
        if (isRunning.get()) {
            return "RUNNING";
        } else if (!syncEnabled) {
            return "DISABLED";
        } else if (lastSyncSuccess) {
            return "IDLE";
        } else {
            return "ERROR";
        }
    }
    
    // Getter方法用于配置查询
    public boolean isSyncEnabled() {
        return syncEnabled;
    }
    
    public long getSyncInterval() {
        return syncInterval;
    }
    
    public long getStartupDelay() {
        return startupDelay;
    }
    
    /**
     * 将硬件数据更新到数据库
     * 
     * @param hardwareHeads 从硬件获取的治疗头信息列表
     * @return 更新的数据库记录数量
     */
    private int updateDatabaseFromHardwareData(List<TreatmentHeadInfo> hardwareHeads) {
        int updatedCount = 0;
        
        logger.debug("Updating database with {} treatment heads from hardware", hardwareHeads.size());
        
        for (TreatmentHeadInfo hardwareHead : hardwareHeads) {
            try {
                // 查找数据库中对应的治疗头记录
                Optional<TreatmentHead> existingHeadOpt = treatmentHeadRepository.findByHeadNumber(hardwareHead.getHeadNumber());
                
                TreatmentHead dbHead;
                if (existingHeadOpt.isPresent()) {
                    // 更新现有记录
                    dbHead = existingHeadOpt.get();
                    logger.debug("Updating existing treatment head {}", hardwareHead.getHeadNumber());
                } else {
                    // 创建新记录
                    dbHead = new TreatmentHead();
                    dbHead.setHeadNumber(hardwareHead.getHeadNumber());
                    logger.debug("Creating new treatment head record for {}", hardwareHead.getHeadNumber());
                }
                
                // 从硬件数据更新字段
                // 处理槽位号：下层需要+10偏移以避免与上层冲突
                Integer dbSlotNumber = hardwareHead.getSlotNumber();
                if ("下仓(深部)".equals(hardwareHead.getCompartmentType())) {
                    dbSlotNumber += 10;  // 下层槽位1-10 映射到数据库槽位11-20
                }
                dbHead.setSlotNumber(dbSlotNumber);
                dbHead.setCompartmentType(hardwareHead.getCompartmentType());
                dbHead.setBatteryLevel(hardwareHead.getBatteryLevel());
                dbHead.setTotalUsageCount(hardwareHead.getUsageCount());
                
                // 根据编号和电量设置状态
                Integer batteryLevel = hardwareHead.getBatteryLevel();
                Integer headNumber = hardwareHead.getHeadNumber();

                // 编号为99且在仓中，显示为异常
                if (headNumber != null && headNumber == 99) {
                    dbHead.setRealtimeStatus(TreatmentHeadStatus.ABNORMAL);
                    logger.warn("治疗头编号99检测到，设置为异常状态");
                } else if (batteryLevel != null && batteryLevel >= 100) {
                    // 电量满且在仓中，显示充电完成
                    dbHead.setRealtimeStatus(TreatmentHeadStatus.CHARGED);
                } else if (batteryLevel != null && batteryLevel < 100) {
                    // 电量未满且在仓中，显示充电中
                    dbHead.setRealtimeStatus(TreatmentHeadStatus.CHARGING);
                } else {
                    // 默认充电中状态
                    dbHead.setRealtimeStatus(TreatmentHeadStatus.CHARGING);
                }
                
                // 重置指示灯颜色（硬件同步后默认关闭）
                dbHead.setLightColor(0);
                
                // 保存到数据库
                treatmentHeadRepository.save(dbHead);
                updatedCount++;
                
                logger.debug("Successfully updated treatment head {}: battery={}%, compartment={}, status={}", 
                           hardwareHead.getHeadNumber(), hardwareHead.getBatteryLevel(), 
                           hardwareHead.getCompartmentType(), dbHead.getRealtimeStatus());
                
            } catch (Exception e) {
                logger.error("Failed to update database for treatment head {}: {}", 
                           hardwareHead.getHeadNumber(), e.getMessage(), e);
            }
        }
        
        logger.info("Database update completed: {}/{} treatment heads updated successfully", 
                   updatedCount, hardwareHeads.size());
        
        return updatedCount;
    }

    /**
     * 更新未查询到的治疗头状态为治疗中
     * 每次查询治疗头时，将没有查询到的治疗头的状态改为治疗中
     *
     * @param queriedHeads 从硬件查询到的治疗头信息列表
     * @return 更新为治疗中状态的治疗头数量
     */
    private int updateMissingTreatmentHeadsToTreating(List<TreatmentHeadInfo> queriedHeads) {
        int treatingCount = 0;

        try {
            logger.debug("开始检查未查询到的治疗头，将其状态更新为治疗中...");

            // 获取所有数据库中的治疗头
            List<TreatmentHead> allDbHeads = treatmentHeadRepository.findAll();

            // 提取查询到的治疗头编号
            Set<Integer> queriedHeadNumbers = queriedHeads.stream()
                    .map(TreatmentHeadInfo::getHeadNumber)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            logger.debug("查询到的治疗头编号: {}", queriedHeadNumbers);

            // 找出未查询到的治疗头
            for (TreatmentHead dbHead : allDbHeads) {
                if (dbHead.getHeadNumber() != null &&
                    !queriedHeadNumbers.contains(dbHead.getHeadNumber())) {

                    // 只有当前状态不是治疗中时才更新
                    if (dbHead.getRealtimeStatus() != TreatmentHeadStatus.TREATING) {
                        TreatmentHeadStatus oldStatus = dbHead.getRealtimeStatus();
                        dbHead.setRealtimeStatus(TreatmentHeadStatus.TREATING);

                        // 保存更新
                        treatmentHeadRepository.save(dbHead);
                        treatingCount++;

                        logger.info("治疗头 {} 未在查询中发现，状态已更新: {} -> TREATING",
                                   dbHead.getHeadNumber(), oldStatus);
                    }
                }
            }

            if (treatingCount > 0) {
                logger.info("共有 {} 个未查询到的治疗头状态已更新为治疗中", treatingCount);
            } else {
                logger.debug("所有治疗头都已查询到或已是治疗中状态，无需更新");
            }

        } catch (Exception e) {
            logger.error("更新未查询到的治疗头状态时发生异常", e);
        }

        return treatingCount;
    }
}