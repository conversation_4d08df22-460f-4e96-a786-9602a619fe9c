package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.EnhancedCandidateItem;
import com.Bone.BoneSys.dto.EnhancedCandidateListResponse;
import com.Bone.BoneSys.dto.CandidateResponseBuilder;
import com.Bone.BoneSys.entity.BodyPartStat;
import com.Bone.BoneSys.entity.Patient;
import com.Bone.BoneSys.entity.Record;
import com.Bone.BoneSys.repository.BodyPartStatRepository;
import com.Bone.BoneSys.repository.PatientRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import com.Bone.BoneSys.util.DataValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 患者数据聚合服务
 * 负责聚合患者、档案和部位统计数据，为新建档案页面提供完整的患者信息
 */
@Service
@Slf4j
@Transactional(readOnly = true)
public class PatientDataAggregationService {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;
    
    @Autowired
    private DataMissingHandlerService dataMissingHandlerService;
    
    /**
     * 获取患者完整信息（包含档案和部位统计）
     * 
     * @param search 搜索关键词（患者姓名或就诊卡号）
     * @param pageable 分页参数
     * @return 增强的候选患者列表响应
     */
    public EnhancedCandidateListResponse getPatientCandidatesWithStats(String search, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始获取患者候选列表，搜索条件: {}, 分页: {}", search, pageable);
            
            // 查询患者基本信息
            Page<Patient> patientPage = queryPatients(search, pageable);
            
            // 转换为增强的候选项
            Page<EnhancedCandidateItem> candidatePage = patientPage.map(this::convertToEnhancedCandidateItem);
            
            // 构建响应
            EnhancedCandidateListResponse response = CandidateResponseBuilder.buildResponse(candidatePage);
            
            log.info("获取患者候选列表成功，共{}条记录，耗时: {}ms", 
                    candidatePage.getTotalElements(), System.currentTimeMillis() - startTime);
            
            // 记录响应统计信息
            log.info(CandidateResponseBuilder.getResponseStatistics(response));
            
            return response;
            
        } catch (Exception e) {
            log.error("获取患者候选列表失败", e);
            return CandidateResponseBuilder.createErrorResponse("获取患者候选列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询患者基本信息
     */
    private Page<Patient> queryPatients(String search, Pageable pageable) {
        if (search != null && !search.trim().isEmpty()) {
            String trimmedSearch = search.trim();
            return patientRepository.findByNameContainingOrPatientCardIdContaining(
                    trimmedSearch, trimmedSearch, pageable);
        } else {
            return patientRepository.findAll(pageable);
        }
    }
    
    /**
     * 将Patient转换为EnhancedCandidateItem
     */
    private EnhancedCandidateItem convertToEnhancedCandidateItem(Patient patient) {
        // 创建基础候选项（使用静态格式化方法）
        EnhancedCandidateItem item = new EnhancedCandidateItem(
                patient.getId(),
                patient.getPatientCardId(),
                patient.getName(),
                patient.getAge(),
                patient.getGender()
        );
        
        // 聚合档案和部位统计数据
        aggregatePatientData(patient.getId(), item);
        
        // 使用数据验证工具清理和验证数据
        EnhancedCandidateItem validatedItem = DataValidationUtil.cleanAndValidate(item);
        
        // 处理数据缺失情况
        return dataMissingHandlerService.handleMissingDataSingle(validatedItem);
    }
    
    /**
     * 聚合患者的档案和部位统计数据
     */
    private void aggregatePatientData(Long patientId, EnhancedCandidateItem item) {
        try {
            // 获取患者的所有档案记录
            List<Record> records = recordRepository.findByPatientId(patientId);
            
            if (records.isEmpty()) {
                // 患者没有档案记录，使用数据缺失处理服务
                dataMissingHandlerService.handleNoRecords(item);
                return;
            }
            
            // 设置最新就诊时间
            String latestVisitTime = getLatestVisitTime(records);
            item.setVisitTime(latestVisitTime);
            
            // 聚合治疗部位和次数
            aggregateTreatmentData(patientId, item);
            
        } catch (Exception e) {
            log.warn("聚合患者{}数据时出现异常: {}", patientId, e.getMessage());
            // 使用数据缺失处理服务处理异常情况
            dataMissingHandlerService.handleDataException(item, e);
        }
    }
    
    /**
     * 获取患者最新就诊时间
     */
    private String getLatestVisitTime(List<Record> records) {
        return records.stream()
                .map(Record::getCreatedAt)
                .max(java.time.LocalDate::compareTo)
                .map(EnhancedCandidateItem::formatVisitTime)
                .orElse("无档案");
    }
    
    /**
     * 聚合患者的治疗部位信息和治疗次数
     */
    private void aggregateTreatmentData(Long patientId, EnhancedCandidateItem item) {
        // 获取患者所有的部位统计
        List<BodyPartStat> bodyPartStats = bodyPartStatRepository.findByPatientId(patientId);
        
        if (bodyPartStats.isEmpty()) {
            // 档案没有部位统计，使用数据缺失处理服务
            dataMissingHandlerService.handleNoBodyPartStats(item);
            return;
        }
        
        // 聚合治疗部位（去重）
        String treatmentParts = aggregateTreatmentParts(bodyPartStats);
        item.setTreatmentParts(treatmentParts);
        
        // 计算总治疗次数
        Integer totalSessions = calculateTotalSessions(bodyPartStats);
        item.setTotalSessions(totalSessions);
    }
    
    /**
     * 聚合患者的治疗部位信息
     */
    private String aggregateTreatmentParts(List<BodyPartStat> bodyPartStats) {
        List<String> distinctParts = bodyPartStats.stream()
                .map(BodyPartStat::getBodyPart)
                .filter(part -> part != null && !part.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        
        return EnhancedCandidateItem.formatTreatmentParts(distinctParts);
    }
    
    /**
     * 计算患者的总治疗次数（使用档案的 sessions_completed_count）
     */
    private Integer calculateTotalSessions(List<BodyPartStat> bodyPartStats) {
        // 改为使用档案表的 sessions_completed_count 字段，与档案管理页面保持一致
        if (bodyPartStats.isEmpty()) {
            return 0;
        }

        // 获取该患者所有档案的完成次数总和（去重避免同一档案重复计算）
        int total = bodyPartStats.stream()
                .map(stat -> stat.getRecord())
                .filter(record -> record != null)
                .distinct() // 去重档案
                .mapToInt(record -> record.getSessionsCompletedCount() != null
                        ? record.getSessionsCompletedCount() : 0)
                .sum();

        return EnhancedCandidateItem.formatTotalSessions(total);
    }
    

    
    /**
     * 获取患者统计摘要（用于调试和监控）
     */
    public Map<String, Object> getPatientStatsSummary(Long patientId) {
        try {
            List<Record> records = recordRepository.findByPatientId(patientId);
            List<BodyPartStat> bodyPartStats = bodyPartStatRepository.findByPatientId(patientId);
            
            return Map.of(
                    "patientId", patientId,
                    "recordCount", records.size(),
                    "bodyPartStatCount", bodyPartStats.size(),
                    "totalSessions", calculateTotalSessions(bodyPartStats),
                    "treatmentParts", aggregateTreatmentParts(bodyPartStats)
            );
        } catch (Exception e) {
            log.error("获取患者{}统计摘要失败", patientId, e);
            return Map.of("error", e.getMessage());
        }
    }
}