#!/bin/bash

# 🧪 Mock硬件测试脚本
# 用于快速验证Mock硬件功能是否正常工作

BASE_URL="http://localhost:8080/api"

echo "🚀 开始测试Mock硬件功能..."

# 1. 检查硬件连接状态
echo "📡 1. 检查硬件连接状态"
curl -s "$BASE_URL/test/hardware/connection-status" | jq '.'
echo ""

# 2. 同步治疗头数据
echo "🔄 2. 同步治疗头数据"
curl -s -X POST "$BASE_URL/test/hardware/sync-treatment-heads" | jq '.data.syncedHeads'
echo ""

# 3. 测试指示灯控制
echo "💡 3. 测试指示灯控制"
echo "   点亮治疗头1,2,3的指示灯..."
curl -s -X POST "$BASE_URL/test/hardware/test-lights?headNumbers=1,2,3&turnOn=true" | jq '.message'

echo "   关闭治疗头1,2,3的指示灯..."
curl -s -X POST "$BASE_URL/test/hardware/test-lights?headNumbers=1,2,3&turnOn=false" | jq '.message'
echo ""

# 4. 测试治疗启动和停止
echo "⚡ 4. 测试治疗启动和停止"
echo "   启动治疗头5的治疗..."
curl -s -X POST "$BASE_URL/test/hardware/start-treatment?headNumber=5&duration=10&intensity=30&frequency=1000" | jq '.message'

sleep 2

echo "   停止治疗头5的治疗..."
curl -s -X POST "$BASE_URL/test/hardware/stop-treatment?headNumber=5" | jq '.message'
echo ""

# 5. 批量测试治疗流程
echo "🔄 5. 批量测试治疗流程"
curl -s -X POST "$BASE_URL/test/hardware/test-treatment-flow?headNumbers=1,2" | jq '.data'
echo ""

# 6. 测试通知功能
echo "🔔 6. 测试通知功能"
echo "   发送治疗完成通知..."
curl -s -X POST "$BASE_URL/test/notifications/treatment-completed?patientName=测试患者" | jq '.message'

echo "   发送待取回提醒..."
curl -s -X POST "$BASE_URL/test/notifications/pickup-reminder?headNumbers=1,2,3" | jq '.message'
echo ""

# 7. 获取数据库治疗头状态
echo "🗄️ 7. 获取数据库治疗头状态"
curl -s "$BASE_URL/test/hardware/database-heads" | jq '.data | length'
echo ""

echo "✅ Mock硬件测试完成！"
echo ""
echo "📋 测试结果说明："
echo "   - 如果所有API都返回成功状态，说明Mock硬件工作正常"
echo "   - 可以在前端页面查看WebSocket通知是否正常显示"
echo "   - 数据库中应该有治疗头数据记录"
