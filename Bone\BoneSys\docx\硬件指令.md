贴片式超声治疗仪主板和控制板之间的通信协议
主板和控制板之间使用串口通信，波特率115200，停止位1，数据位8，校验位None
发送接收数据类型为 char
1.主板查询所有在盒子里面的治疗头数据
    主板串口发送：TRZI\r\n
    控制板返回：TRZI+治疗头数量(2 char)+((治疗头编号（2char)+治疗头电量(2char或者电量100为3char)+槽编号（2char）)*治疗头数量\r\n
例子：09治疗头数量 14治疗头编号 88治疗头电量（如电量满为100）01槽编号
发送-TRZI\r\n
返回-TRZI0914880120890213830318840412100051984061685071110008158310\r\n
2.主板点亮推荐治疗头指示灯
    主板串口发送：TWSC+治疗头数量(2 char)+(治疗头编号(2char)+颜色（1char)*治疗头数量\r\n
控制板返回： TWSC+治疗头数量(2 char)+(治疗头编号(2char)+颜色(1char)）*治疗头数量\r\n
例子：06治疗头数量  11治疗头编号 1颜色（1黄色 2绿色 3蓝色）
发送-TWSC06111122133141152163\r\n
接收-TWSC06111122133141152163\r\n
3.主板关闭推荐治疗头指示灯
     主板串口发送：TWSN+治疗头数量(2 char)+治疗头编号（2char)*治疗头数量\r\n
     控制板返回：  TWSN+治疗头数量(2 char)+治疗头编号（2char)*治疗头数量\r\n  
例子：06治疗头数量  11治疗头编号
发送-TWSN06111213141516\r\n
接收-TWSN06111213141516\r\n
 4.主板向治疗头发送治疗参数（串口）
     主板串口发送： TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记（1 char)+ID+治疗头数量（2char)+治疗头编号(2char)*治疗头数量\r\n     //重复周期标记 0---100Hz  1---1000Hz
     控制板返回：   TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记（1 char)+ID+治疗头数量（2char)+治疗头编号(2char)*治疗头数量\r\n
例子：20设置时间（分钟）030设置声强 0重复周期标记 01治疗头数量 14治疗头编号
发送-TWSDT20030F0ID0114
接收-TWSDT20030F0ID0114
 5.主板向某个治疗头发送治疗参数并工作
     主板串口发送： TWZS+治疗头编号(2char)+设置时间(2 char) +(设置声强3char) +重复周期标记（1 char)\r\n  
     控制板返回：   TWZS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char) +重复周期标记（1 char)\r\n
例子：14治疗头编号 20设置时间 030设置声强 0重复周期标记//重复周期标记 0---100Hz  1---1000Hz
发送-TWZS14200300\r\n
接收-TWZS14200300\r\n
6.主板向关闭某个治疗头
主板串口发送： TWZO+治疗头编号(2char) \r\n
控制板返回：TWZO+治疗头编号(2char) \r\n
例子：14治疗头编号
发送-TWZO14
接版-TWZO14