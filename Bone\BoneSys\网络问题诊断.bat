@echo off
chcp 65001 > nul
echo ========================================
echo 网络问题诊断脚本
echo ========================================
echo.

echo [1/6] 检查端口占用情况...
echo 检查8080端口（后端）：
netstat -an | findstr :8080
echo.
echo 检查5173端口（前端）：
netstat -an | findstr :5173
echo.

echo [2/6] 测试后端服务连接...
echo 测试健康检查接口：
curl -s -w "HTTP状态码: %%{http_code}\n响应时间: %%{time_total}s\n" http://localhost:8080/api/health
echo.

echo [3/6] 测试治疗参数API...
echo 测试check-availability接口：
curl -s -w "HTTP状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
  -X POST http://localhost:8080/api/treatment-parameters/check-availability ^
  -H "Content-Type: application/json" ^
  -d "{\"patientId\":\"TEST001\",\"treatmentMode\":\"local\",\"bodyParts\":[{\"name\":\"测试部位\",\"color\":\"#FF0000\",\"parameters\":{\"time\":\"10分钟\",\"intensity\":\"20\",\"frequency\":\"100\",\"depth\":\"浅部\",\"count\":1}}]}"
echo.

echo [4/6] 测试治疗头数据API...
echo 测试治疗头列表接口：
curl -s -w "HTTP状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
  "http://localhost:8080/api/hardware/heads?page=1&size=5"
echo.

echo [5/6] 测试同步状态API...
echo 测试同步状态接口：
curl -s -w "HTTP状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
  http://localhost:8080/api/hardware/treatment-heads/sync/status
echo.

echo [6/6] 检查前端代理配置...
echo 如果前端正在运行，测试代理：
curl -s -w "HTTP状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
  http://localhost:5173/api/health 2>nul
if %errorlevel% neq 0 (
    echo ❌ 前端代理测试失败，可能前端未运行或代理配置错误
) else (
    echo ✅ 前端代理测试成功
)
echo.

echo ========================================
echo 诊断完成！
echo ========================================
echo.
echo 🔍 问题分析：
echo.
echo 1. **端口检查**
echo    - 8080端口应该被Java进程占用（后端）
echo    - 5173端口应该被Node.js进程占用（前端）
echo.
echo 2. **API连接测试**
echo    - HTTP状态码200表示成功
echo    - HTTP状态码404表示路径不存在
echo    - HTTP状态码500表示服务器错误
echo    - 连接失败表示服务未启动
echo.
echo 3. **常见问题解决**
echo    - 如果8080端口无响应：检查后端是否启动
echo    - 如果5173端口无响应：检查前端是否启动
echo    - 如果API返回404：检查控制器路径映射
echo    - 如果前端代理失败：检查vite.config.ts代理配置
echo.
echo 🚨 解决方案：
echo.
echo "后端未启动："
echo "cd BoneSys && ./gradlew bootRun"
echo.
echo "前端未启动："
echo "cd Bone_Vue && npm run dev"
echo.
echo "前端代理问题："
echo "检查 Bone_Vue/vite.config.ts 中的proxy配置"
echo "确保target指向 http://127.0.0.1:8080"
echo.
echo "API路径问题："
echo "前端应该请求 /api/... 而不是 http://localhost:5173/api/..."
echo "Vite代理会自动转发到后端8080端口"
echo.
pause
