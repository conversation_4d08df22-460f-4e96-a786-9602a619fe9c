<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="validation-modal">
        <!-- 关闭按钮 -->
        <img
          class="close-btn"
          :src="closeIcon"
          alt="关闭"
          @click="closeModal"
        />
        
        <!-- 错误消息显示区域 -->
        <div class="message-content">
          <div class="message-text">{{ message }}</div>
          <div class="countdown-text" v-if="showCountdown">
            {{ countdownSeconds }}秒后自动关闭
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, onMounted, onUnmounted, watch, ref } from 'vue'

// 导入图片
import validationBackground from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/-h-温馨提示治疗头数量不足.png'
import closeIcon from '@/assets/images/交互界面/参数设置/×.png'

// Props
const props = defineProps<{
  visible: boolean
  message: string
  autoCloseSeconds?: number
}>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const countdownSeconds = ref(5)
const showCountdown = ref(false)
let countdownTimer: NodeJS.Timeout | null = null

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const closeModal = () => {
  clearTimer()
  emit('close')
}

const startCountdown = () => {
  const autoCloseTime = props.autoCloseSeconds || 5
  countdownSeconds.value = autoCloseTime
  showCountdown.value = true
  
  countdownTimer = setInterval(() => {
    countdownSeconds.value--
    if (countdownSeconds.value <= 0) {
      clearTimer()
      emit('close')
    }
  }, 1000)
}

const clearTimer = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  showCountdown.value = false
}

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    startCountdown()
  } else {
    clearTimer()
  }
}, { immediate: true })

// 生命周期
onUnmounted(() => {
  clearTimer()
})
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background: transparent;
  overflow: hidden;
  position: absolute;
  top: 147px;
  left: 658px;
}

/* 治疗头校验失败弹窗样式 */
.validation-modal {
  height: 752px;
  background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/-h-温馨提示治疗头数量不足.png') 0px 0px no-repeat;
  background-size: 631px 774px;
  width: 612px;
  position: relative;
}

/* 关闭按钮样式 */
.close-btn {
  width: 60px;
  height: 60px;
  margin: 31px 0 0 510px;
  cursor: pointer;
  position: absolute;
  transition: transform 0.2s ease;
}

.close-btn:hover {
  transform: scale(1.1);
}

/* 消息内容区域 */
.message-content {
  position: absolute;
  top: 300px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  text-align: center;
  color: #333;
}

.message-text {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 20px;
  word-wrap: break-word;
}

.countdown-text {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}
</style>
