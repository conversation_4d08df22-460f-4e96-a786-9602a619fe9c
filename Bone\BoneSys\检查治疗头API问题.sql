-- ========================================
-- 检查治疗头API问题诊断脚本
-- ========================================

USE bonesys;

-- 1. 检查治疗头表是否有数据
SELECT '=== 治疗头表数据检查 ===' as info;

SELECT 
    COUNT(*) as total_heads,
    COUNT(CASE WHEN realtime_status = 'CHARGED' THEN 1 END) as charged_heads,
    COUNT(CASE WHEN realtime_status = 'CHARGING' THEN 1 END) as charging_heads,
    COUNT(CASE WHEN realtime_status = 'TREATING' THEN 1 END) as treating_heads,
    COUNT(CASE WHEN battery_level >= 60 THEN 1 END) as high_battery_heads,
    COUNT(CASE WHEN battery_level >= 60 AND realtime_status = 'CHARGED' THEN 1 END) as available_heads
FROM treatment_heads;

-- 2. 检查具体的治疗头数据
SELECT '=== 治疗头详细数据 ===' as info;

SELECT 
    head_number,
    slot_number,
    realtime_status,
    battery_level,
    total_usage_count,
    CASE 
        WHEN battery_level >= 60 AND realtime_status = 'CHARGED' THEN 'YES'
        ELSE 'NO'
    END as is_available
FROM treatment_heads
ORDER BY head_number;

-- 3. 检查可用治疗头查询（模拟API查询）
SELECT '=== 可用治疗头查询（电量>=60%且状态为CHARGED）===' as info;

SELECT 
    head_number,
    realtime_status,
    battery_level,
    slot_number
FROM treatment_heads 
WHERE realtime_status = 'CHARGED' AND battery_level >= 60
ORDER BY head_number;

-- 4. 检查患者数据
SELECT '=== 患者数据检查 ===' as info;

SELECT COUNT(*) as total_patients FROM patients;

-- 5. 检查档案数据
SELECT '=== 档案数据检查 ===' as info;

SELECT COUNT(*) as total_records FROM records;

-- 6. 检查进程数据
SELECT '=== 进程数据检查 ===' as info;

SELECT 
    COUNT(*) as total_processes,
    COUNT(CASE WHEN status = 'IN_PROGRESS' THEN 1 END) as in_progress_processes,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_processes,
    COUNT(CASE WHEN DATE(start_time) = CURDATE() THEN 1 END) as today_processes
FROM processes;

-- 7. 检查治疗详情数据
SELECT '=== 治疗详情数据检查 ===' as info;

SELECT 
    COUNT(*) as total_details,
    COUNT(CASE WHEN status = 'RETURNED' THEN 1 END) as returned_details,
    COUNT(CASE WHEN status = 'AWAITING_RETURN' THEN 1 END) as awaiting_return_details
FROM treatment_details;

-- 8. 模拟主界面API查询
SELECT '=== 模拟主界面API数据 ===' as info;

SELECT 
    'availableHeads' as field,
    COUNT(*) as value
FROM treatment_heads 
WHERE realtime_status = 'CHARGED' AND battery_level >= 60

UNION ALL

SELECT 
    'totalHeads' as field,
    20 as value

UNION ALL

SELECT 
    'totalPatients' as field,
    COUNT(*) as value
FROM patients

UNION ALL

SELECT 
    'totalRecords' as field,
    COUNT(*) as value
FROM records

UNION ALL

SELECT 
    'totalProcesses' as field,
    COUNT(*) as value
FROM processes

UNION ALL

SELECT 
    'todayProcesses' as field,
    COUNT(*) as value
FROM processes
WHERE DATE(start_time) = CURDATE();

-- 9. 检查数据库约束
SELECT '=== 数据库约束检查 ===' as info;

SHOW CREATE TABLE treatment_heads;

SELECT '=== 诊断完成 ===' as message;
