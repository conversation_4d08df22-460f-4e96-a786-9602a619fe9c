package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.exception.TreatmentHeadNotTakenException;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 硬件服务 - 完整实现版
 * 支持WebSocket(远程硬件)和串口(本地硬件)两种通信模式
 * 通信模式通过配置文件hardware.communication.mode切换
 */
@Service
public class HardwareService {

    private static final Logger logger = LoggerFactory.getLogger(HardwareService.class);

    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;

    @Autowired
    private HardwareCommunicationInterface hardwareCommunication;

    @Autowired
    private HardwareCommandParser commandParser;
    
    public List<TreatmentHeadInfo> getAllTreatmentHeadsForManagement() {
        List<TreatmentHead> allDbHeads = treatmentHeadRepository.findAll();
        return allDbHeads.stream()
            .map(this::convertToTreatmentHeadInfo)
            .sorted((h1, h2) -> Integer.compare(h1.getHeadNumber(), h2.getHeadNumber()))
            .collect(Collectors.toList());
    }
    
    private TreatmentHeadInfo convertToTreatmentHeadInfo(TreatmentHead head) {
        TreatmentHeadInfo info = new TreatmentHeadInfo();
        info.setHeadNumber(head.getHeadNumber());
        info.setBatteryLevel(head.getBatteryLevel() != null ? head.getBatteryLevel() : 0);
        info.setUsageCount(head.getTotalUsageCount());
        info.setSlotNumber(head.getSlotNumber() != null ? head.getSlotNumber() : 0);
        info.setStatus(head.getRealtimeStatus().name());
        info.setCompartmentType(head.getCompartmentType());
        return info;
    }
    
    public List<TreatmentHeadInfo> syncAllTreatmentHeads() throws SerialCommunicationException {
        try {
            logger.info("Syncing all treatment heads from both upper and lower layers...");

            // 构建TRZI查询指令
            String command = commandParser.buildQueryAllTreatmentHeadsCommand();

            // 用于存储所有治疗头信息
            List<TreatmentHeadInfo> allHeads = new ArrayList<>();
            
            // 同时查询上层和下层治疗仓
            try {
                // 查询上层治疗仓（治疗头1-10）
                logger.debug("Querying upper layer treatment heads...");
                String upperResponse = hardwareCommunication.sendCommand(command, "upper");
                List<TreatmentHeadInfo> upperHeads = commandParser.parseQueryAllTreatmentHeadsResponse(upperResponse);
                
                // 为上层治疗头设置正确的compartmentType
                for (TreatmentHeadInfo head : upperHeads) {
                    head.setCompartmentType("上仓(浅部)");
                }
                allHeads.addAll(upperHeads);
                logger.info("Successfully synced {} treatment heads from upper layer", upperHeads.size());
                
            } catch (Exception e) {
                logger.warn("Failed to sync treatment heads from upper layer: {}", e.getMessage());
            }

            try {
                // 查询下层治疗仓（治疗头11-20）
                logger.debug("Querying lower layer treatment heads...");
                String lowerResponse = hardwareCommunication.sendCommand(command, "lower");
                List<TreatmentHeadInfo> lowerHeads = commandParser.parseQueryAllTreatmentHeadsResponse(lowerResponse);
                
                // 为下层治疗头设置正确的compartmentType
                for (TreatmentHeadInfo head : lowerHeads) {
                    head.setCompartmentType("下仓(深部)");
                    // 注意：保持硬件返回的真实治疗头编号不变
                }
                allHeads.addAll(lowerHeads);
                logger.info("Successfully synced {} treatment heads from lower layer", lowerHeads.size());
                
            } catch (Exception e) {
                logger.warn("Failed to sync treatment heads from lower layer: {}", e.getMessage());
            }

            if (allHeads.isEmpty()) {
                throw new SerialCommunicationException("Failed to sync treatment heads from both layers");
            }

            logger.info("Successfully synced {} treatment heads total from hardware", allHeads.size());
            return allHeads;

        } catch (Exception e) {
            logger.error("Failed to sync treatment heads from hardware", e);
            throw new SerialCommunicationException("Hardware sync failed: " + e.getMessage(), e);
        }
    }

    public boolean isHardwareConnected() {
        return hardwareCommunication.isConnected();
    }

    public List<TreatmentHeadLightResponse> setTreatmentHeadLights(List<TreatmentHeadLightRequest> lightRequests)
            throws SerialCommunicationException {
        try {
            logger.info("Setting treatment head lights for {} heads", lightRequests.size());

            // 按层级分组治疗头请求
            List<TreatmentHeadLightRequest> upperRequests = new ArrayList<>();
            List<TreatmentHeadLightRequest> lowerRequests = new ArrayList<>();
            
            for (TreatmentHeadLightRequest request : lightRequests) {
                if (isUpperLayerHead(request.getHeadNumber())) {
                    upperRequests.add(request);
                } else if (isLowerLayerHead(request.getHeadNumber())) {
                    // 直接使用实际的治疗头编号（硬件应该能识别自己的编号）
                    lowerRequests.add(request);
                } else {
                    logger.warn("Unknown treatment head number: {} (not found in database)", request.getHeadNumber());
                }
            }

            List<TreatmentHeadLightResponse> allResponses = new ArrayList<>();

            // 处理上层治疗头
            if (!upperRequests.isEmpty()) {
                try {
                    logger.debug("Setting lights for {} upper layer heads", upperRequests.size());
                    String upperCommand = commandParser.buildLightUpCommand(upperRequests);
                    String upperResponse = hardwareCommunication.sendCommand(upperCommand, "upper");
                    List<TreatmentHeadLightResponse> upperLightResponses = commandParser.parseLightUpResponse(upperResponse);
                    allResponses.addAll(upperLightResponses);
                } catch (Exception e) {
                    logger.error("Failed to set lights for upper layer heads", e);
                    throw new SerialCommunicationException("Upper layer light control failed: " + e.getMessage(), e);
                }
            }

            // 处理下层治疗头
            if (!lowerRequests.isEmpty()) {
                try {
                    logger.debug("Setting lights for {} lower layer heads", lowerRequests.size());
                    String lowerCommand = commandParser.buildLightUpCommand(lowerRequests);
                    String lowerResponse = hardwareCommunication.sendCommand(lowerCommand, "lower");
                    List<TreatmentHeadLightResponse> lowerLightResponses = commandParser.parseLightUpResponse(lowerResponse);
                    
                    // 保持硬件返回的真实治疗头编号不变
                    allResponses.addAll(lowerLightResponses);
                } catch (Exception e) {
                    logger.error("Failed to set lights for lower layer heads", e);
                    throw new SerialCommunicationException("Lower layer light control failed: " + e.getMessage(), e);
                }
            }

            logger.info("Successfully set lights for {} treatment heads", allResponses.size());
            return allResponses;

        } catch (Exception e) {
            logger.error("Failed to set treatment head lights", e);
            throw new SerialCommunicationException("Light control failed: " + e.getMessage(), e);
        }
    }

    /**
     * 点亮治疗头指示灯（别名方法，用于向后兼容）
     */
    public List<TreatmentHeadLightResponse> lightUpTreatmentHeads(List<TreatmentHeadLightRequest> lightRequests)
            throws SerialCommunicationException {
        return setTreatmentHeadLights(lightRequests);
    }

    public List<Integer> turnOffTreatmentHeadLights(List<Integer> headNumbers) throws SerialCommunicationException {
        try {
            logger.info("Turning off lights for {} treatment heads", headNumbers.size());

            // 按层级分组治疗头
            List<Integer> upperHeads = new ArrayList<>();
            List<Integer> lowerHeads = new ArrayList<>();
            
            for (Integer headNumber : headNumbers) {
                if (isUpperLayerHead(headNumber)) {
                    upperHeads.add(headNumber);
                } else if (isLowerLayerHead(headNumber)) {
                    // 直接使用实际的治疗头编号
                    lowerHeads.add(headNumber);
                } else {
                    logger.warn("Unknown treatment head number: {} (not found in database)", headNumber);
                }
            }

            List<Integer> allTurnedOffHeads = new ArrayList<>();

            // 处理上层治疗头
            if (!upperHeads.isEmpty()) {
                try {
                    logger.debug("Turning off lights for {} upper layer heads", upperHeads.size());
                    String upperCommand = commandParser.buildTurnOffLightCommand(upperHeads);
                    String upperResponse = hardwareCommunication.sendCommand(upperCommand, "upper");
                    List<Integer> upperTurnedOff = commandParser.parseTurnOffLightResponse(upperResponse);
                    allTurnedOffHeads.addAll(upperTurnedOff);
                } catch (Exception e) {
                    logger.error("Failed to turn off lights for upper layer heads", e);
                    throw new SerialCommunicationException("Upper layer light turn off failed: " + e.getMessage(), e);
                }
            }

            // 处理下层治疗头
            if (!lowerHeads.isEmpty()) {
                try {
                    logger.debug("Turning off lights for {} lower layer heads", lowerHeads.size());
                    String lowerCommand = commandParser.buildTurnOffLightCommand(lowerHeads);
                    String lowerResponse = hardwareCommunication.sendCommand(lowerCommand, "lower");
                    List<Integer> lowerTurnedOff = commandParser.parseTurnOffLightResponse(lowerResponse);
                    
                    // 保持硬件返回的真实治疗头编号不变
                    allTurnedOffHeads.addAll(lowerTurnedOff);
                } catch (Exception e) {
                    logger.error("Failed to turn off lights for lower layer heads", e);
                    throw new SerialCommunicationException("Lower layer light turn off failed: " + e.getMessage(), e);
                }
            }

            logger.info("Successfully turned off lights for {} treatment heads", allTurnedOffHeads.size());
            return allTurnedOffHeads;

        } catch (Exception e) {
            logger.error("Failed to turn off treatment head lights", e);
            throw new SerialCommunicationException("Light turn off failed: " + e.getMessage(), e);
        }
    }

    public boolean sendTreatmentParams(TreatmentParamsRequest request) throws SerialCommunicationException {
        try {
            logger.info("Sending treatment parameters to {} treatment heads", request.getHeadNumbers().size());

            // 按层分组治疗头
            List<Integer> upperHeads = new ArrayList<>();
            List<Integer> lowerHeads = new ArrayList<>();
            
            for (Integer headNumber : request.getHeadNumbers()) {
                String layer = determineLayerByHeadNumber(headNumber);
                if ("upper".equals(layer)) {
                    upperHeads.add(headNumber);
                } else if ("lower".equals(layer)) {
                    lowerHeads.add(headNumber);
                }
            }
            
            logger.info("Grouped treatment heads - Upper: {}, Lower: {}", upperHeads, lowerHeads);
            
            boolean allSuccess = true;
            
            // 发送到上层治疗头
            if (!upperHeads.isEmpty()) {
                TreatmentParamsRequest upperRequest = new TreatmentParamsRequest(
                    request.getDuration(), request.getIntensity(), request.getFrequency(), upperHeads);
                String upperCommand = commandParser.buildSendTreatmentParamsCommand(upperRequest);
                String upperResponse = hardwareCommunication.sendCommand(upperCommand, "upper");
                boolean upperSuccess = commandParser.validateSendTreatmentParamsResponse(upperResponse, upperRequest);
                logger.info("Upper layer parameters sent to heads {}: {}", upperHeads, upperSuccess ? "success" : "failed");
                allSuccess &= upperSuccess;
            }
            
            // 发送到下层治疗头
            if (!lowerHeads.isEmpty()) {
                TreatmentParamsRequest lowerRequest = new TreatmentParamsRequest(
                    request.getDuration(), request.getIntensity(), request.getFrequency(), lowerHeads);
                String lowerCommand = commandParser.buildSendTreatmentParamsCommand(lowerRequest);
                String lowerResponse = hardwareCommunication.sendCommand(lowerCommand, "lower");
                boolean lowerSuccess = commandParser.validateSendTreatmentParamsResponse(lowerResponse, lowerRequest);
                logger.info("Lower layer parameters sent to heads {}: {}", lowerHeads, lowerSuccess ? "success" : "failed");
                allSuccess &= lowerSuccess;
            }

            logger.info("Treatment parameters sent to all layers: {}", allSuccess ? "success" : "failed");
            return allSuccess;

        } catch (Exception e) {
            logger.error("Failed to send treatment parameters", e);
            throw new SerialCommunicationException("Parameter sending failed: " + e.getMessage(), e);
        }
    }

    public boolean startTreatment(int headNumber, int duration, int intensity, int frequency)
            throws SerialCommunicationException, TreatmentHeadNotTakenException {
        try {
            logger.info("Starting treatment on head {} with duration={}, intensity={}, frequency={}",
                       headNumber, duration, intensity, frequency);

            // 根据治疗头编号确定层级
            String layer = determineLayerByHeadNumber(headNumber);
            logger.info("Treatment head {} belongs to {} layer", headNumber, layer);

            // 构建TWS指令
            String command = commandParser.buildStartTreatmentCommand(headNumber, duration, intensity, frequency);

            // 发送指令到对应层级的硬件
            String response = hardwareCommunication.sendCommand(command, layer);

            // 验证响应
            boolean success = commandParser.validateStartTreatmentResponse(response, headNumber, duration, intensity, frequency);

            logger.info("Treatment start on head {} ({}): {}", headNumber, layer, success ? "success" : "failed");
            return success;

        } catch (Exception e) {
            logger.error("Failed to start treatment on head {}", headNumber, e);
            throw new SerialCommunicationException("Treatment start failed: " + e.getMessage(), e);
        }
    }

    public boolean stopTreatment(int headNumber) throws SerialCommunicationException {
        try {
            logger.info("Stopping treatment on head {}", headNumber);

            // 根据治疗头编号确定层级
            String layer = determineLayerByHeadNumber(headNumber);
            logger.info("Treatment head {} belongs to {} layer", headNumber, layer);

            // 构建TWZO指令
            String command = commandParser.buildStopTreatmentCommand(headNumber);

            // 发送指令到对应层级的硬件
            String response = hardwareCommunication.sendCommand(command, layer);

            // 解析响应并验证
            int responseHeadNumber = commandParser.parseStopTreatmentResponse(response);
            boolean success = responseHeadNumber == headNumber;

            logger.info("Treatment stop on head {} ({}): {}", headNumber, layer, success ? "success" : "failed");
            return success;

        } catch (Exception e) {
            logger.error("Failed to stop treatment on head {}", headNumber, e);
            throw new SerialCommunicationException("Treatment stop failed: " + e.getMessage(), e);
        }
    }

    public String getHardwareInfo() {
        try {
            boolean connected = hardwareCommunication.isConnected();
            String connectionStatus = connected ? "已连接" : "未连接";
            String communicationType = hardwareCommunication.getCommunicationType();
            return String.format("硬件通信服务 (%s) - %s", communicationType, connectionStatus);
        } catch (Exception e) {
            return "硬件通信服务 - 状态未知: " + e.getMessage();
        }
    }

    public boolean testHardwareConnection() {
        try {
            // 测试两个层级的连接
            String command = commandParser.buildQueryAllTreatmentHeadsCommand();
            
            boolean upperSuccess = false;
            boolean lowerSuccess = false;
            
            try {
                String upperResponse = hardwareCommunication.sendCommand(command, "upper");
                upperSuccess = upperResponse != null && !upperResponse.isEmpty();
                logger.debug("Upper layer connection test: {}", upperSuccess ? "success" : "failed");
            } catch (Exception e) {
                logger.debug("Upper layer connection test failed: {}", e.getMessage());
            }
            
            try {
                String lowerResponse = hardwareCommunication.sendCommand(command, "lower");
                lowerSuccess = lowerResponse != null && !lowerResponse.isEmpty();
                logger.debug("Lower layer connection test: {}", lowerSuccess ? "success" : "failed");
            } catch (Exception e) {
                logger.debug("Lower layer connection test failed: {}", e.getMessage());
            }
            
            boolean anySuccess = upperSuccess || lowerSuccess;
            logger.info("Hardware connection test - Upper: {}, Lower: {}, Overall: {}", 
                       upperSuccess, lowerSuccess, anySuccess);
            
            return anySuccess;
        } catch (Exception e) {
            logger.warn("Hardware connection test failed", e);
            return false;
        }
    }

    public void reconnectHardware() throws SerialCommunicationException {
        try {
            logger.info("Reconnecting to hardware communication service...");
            hardwareCommunication.disconnect();
            hardwareCommunication.connect();
            logger.info("Hardware reconnection completed");
        } catch (Exception e) {
            logger.error("Failed to reconnect to hardware", e);
            throw new SerialCommunicationException("Hardware reconnection failed: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否为上层治疗头（通过数据库查询compartment_type）
     */
    private boolean isUpperLayerHead(int headNumber) {
        return "上仓(浅部)".equals(getCompartmentTypeByHeadNumber(headNumber));
    }

    /**
     * 判断是否为下层治疗头（通过数据库查询compartment_type）
     */
    private boolean isLowerLayerHead(int headNumber) {
        return "下仓(深部)".equals(getCompartmentTypeByHeadNumber(headNumber));
    }

    /**
     * 根据治疗头编号确定层级（通过数据库查询）
     */
    private String determineLayerByHeadNumber(int headNumber) {
        String compartmentType = getCompartmentTypeByHeadNumber(headNumber);
        if ("上仓(浅部)".equals(compartmentType)) {
            return "upper";
        } else if ("下仓(深部)".equals(compartmentType)) {
            return "lower";
        } else {
            throw new IllegalArgumentException("Unknown compartment type for treatment head: " + headNumber);
        }
    }
    
    /**
     * 根据治疗头编号查询compartment_type
     * 如果数据库中没有记录，使用fallback逻辑
     */
    private String getCompartmentTypeByHeadNumber(int headNumber) {
        Optional<String> compartmentType = treatmentHeadRepository.findByHeadNumber(headNumber)
                .map(TreatmentHead::getCompartmentType);
        
        if (compartmentType.isPresent()) {
            return compartmentType.get();
        }
        
        // Fallback: 基于硬件协议的头编号范围判断
        // 根据实际硬件分配，头编号1-10为上层，11-20为下层
        if (headNumber >= 1 && headNumber <= 10) {
            logger.debug("Using fallback logic: head {} assigned to upper layer", headNumber);
            return "上仓(浅部)";
        } else if (headNumber >= 11 && headNumber <= 20) {
            logger.debug("Using fallback logic: head {} assigned to lower layer", headNumber);
            return "下仓(深部)";
        } else {
            logger.warn("Head number {} is outside expected range (1-20), assuming upper layer", headNumber);
            return "上仓(浅部)";
        }
    }
}