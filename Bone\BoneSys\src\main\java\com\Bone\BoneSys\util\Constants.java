package com.Bone.BoneSys.util;

/**
 * 系统常量类
 */
public class Constants {
    
    /**
     * 响应码常量
     */
    public static class ResponseCode {
        public static final int SUCCESS = 200;
        public static final int BAD_REQUEST = 400;
        public static final int UNAUTHORIZED = 401;
        public static final int FORBIDDEN = 403;
        public static final int NOT_FOUND = 404;
        public static final int SERVER_ERROR = 500;
        
        // 业务错误码
        public static final int SERIAL_COMMUNICATION_ERROR = 1001;
        public static final int TREATMENT_HEAD_UNAVAILABLE = 1002;
        public static final int PATIENT_INFO_DUPLICATE = 1003;
    }
    
    /**
     * 治疗模式常量
     */
    public static class TreatmentMode {
        public static final String ON_SITE = "ON_SITE";
        public static final String TAKE_AWAY = "TAKE_AWAY";
    }
    
    /**
     * 进程状态常量
     */
    public static class ProcessStatus {
        public static final String IN_PROGRESS = "IN_PROGRESS";
        public static final String COMPLETED = "COMPLETED";
        public static final String CANCELLED = "CANCELLED";
    }
    
    /**
     * 治疗头状态常量（四种状态）
     */
    public static class TreatmentHeadStatus {
        public static final String CHARGING = "CHARGING";  // 充电中
        public static final String CHARGED = "CHARGED";    // 充电完成
        public static final String TREATING = "TREATING";  // 治疗中
        public static final String ABNORMAL = "ABNORMAL";  // 异常
    }
    
    /**
     * 贴片类型常量
     */
    public static class PatchType {
        public static final String DEEP = "DEEP";
        public static final String SHALLOW = "SHALLOW";
    }
    
    /**
     * 治疗部位常量
     */
    public static class BodyPart {
        public static final String SHOULDER_NECK = "肩颈部";      // 肩颈部
        public static final String WAIST_BACK = "腰背部";        // 腰背部
        public static final String HIP = "髋部";                 // 髋部
        public static final String UPPER_LIMB = "上肢";          // 上肢
        public static final String LOWER_LIMB = "下肢";          // 下肢
        public static final String OTHER = "其他部位";           // 其他部位（可编辑）
    }
    
    /**
     * 治疗详情状态常量
     */
    public static class TreatmentDetailStatus {
        public static final String TREATING = "TREATING";
        public static final String COMPLETED = "COMPLETED";
        public static final String AWAITING_RETURN = "AWAITING_RETURN";
        public static final String RETURNED = "RETURNED";
        public static final String TERMINATED = "TERMINATED";
    }
    
    /**
     * 硬件指令常量
     */
    public static class HardwareCommand {
        public static final String QUERY_TREATMENT_HEADS = "TRZI";
        public static final String LIGHT_UP_HEADS = "TWSC";
        public static final String LIGHT_OFF_HEADS = "TWSN";
        public static final String DOWNLOAD_PARAMETERS = "TWSDT";
        public static final String START_TREATMENT = "TWZS";
        public static final String STOP_TREATMENT = "TWZO";
    }
    
    /**
     * 频率常量
     */
    public static class Frequency {
        public static final int FREQ_100HZ = 100;
        public static final int FREQ_1000HZ = 1000;
    }
    
    /**
     * 指示灯颜色常量
     */
    public static class LightColor {
        public static final int OFF = 0;        // 关闭
        public static final int ORANGE = 1;     // 橙色
        public static final int BLUE = 2;       // 蓝色
        public static final int GREEN = 3;      // 绿色
    }
    
    /**
     * 治疗头仓位常量
     */
    public static class Compartment {
        public static final int SHALLOW_START = 1;   // 浅部开始槽号
        public static final int SHALLOW_END = 10;    // 浅部结束槽号
        public static final int DEEP_START = 11;     // 深部开始槽号
        public static final int DEEP_END = 20;       // 深部结束槽号
        public static final int TOTAL_HEADS = 20;    // 总治疗头数量
    }
    
    /**
     * 默认值常量
     */
    public static class DefaultValues {
        public static final int DEFAULT_PAGE_SIZE = 10;
        public static final int MAX_USAGE_COUNT = 500;
        public static final int DEFAULT_TREATMENT_DURATION = 20;
        public static final double DEFAULT_TREATMENT_INTENSITY = 500.0;
        public static final int DEFAULT_TREATMENT_FREQUENCY = 1000;
        public static final int LOW_BATTERY_THRESHOLD = 20;
        public static final double REPLACEMENT_THRESHOLD = 0.9;
    }
}