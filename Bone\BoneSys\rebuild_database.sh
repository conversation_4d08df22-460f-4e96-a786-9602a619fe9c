#!/bin/bash

echo "========================================"
echo "FREEBONE医疗系统 - 数据库重建脚本"
echo "========================================"
echo

echo "[1/3] 删除现有数据库..."
mysql -u root -p -e "DROP DATABASE IF EXISTS bonesys;"
if [ $? -ne 0 ]; then
    echo "错误：删除数据库失败，请检查MySQL连接"
    exit 1
fi
echo "现有数据库已删除"

echo
echo "[2/3] 创建新数据库和表结构..."
mysql -u root -p < SQL/create_database_enhanced.sql
if [ $? -ne 0 ]; then
    echo "错误：创建数据库失败，请检查SQL脚本"
    exit 1
fi
echo "数据库创建成功"

echo
echo "[3/5] 验证数据库结构..."
mysql -u root -p -e "USE bonesys; SELECT COUNT(*) as treatment_heads_count FROM treatment_heads; SELECT COUNT(*) as presets_count FROM treatment_parameter_presets;"
if [ $? -ne 0 ]; then
    echo "警告：验证数据库失败，但数据库可能已创建成功"
fi

echo
echo "[4/5] 插入测试数据..."
mysql -u root -p < SQL/insert_test_data.sql
if [ $? -ne 0 ]; then
    echo "警告：插入测试数据失败，但基础结构已创建"
fi

echo
echo "[5/5] 验证测试数据..."
mysql -u root -p < 验证数据库.sql
if [ $? -ne 0 ]; then
    echo "警告：验证测试数据失败"
fi

echo
echo "========================================"
echo "数据库重建完成！"
echo "========================================"
echo
echo "预期结果："
echo "- 20个治疗头记录（状态：TREATING）"
echo "- 10个治疗参数预设记录"
echo "- 5个患者，11个档案，27个进程"
echo "- 69个治疗详情，58个身体部位统计"
echo
echo "下一步："
echo "1. 运行 ./gradlew bootRun 启动应用"
echo "2. 观察启动日志确认自动同步成功"
echo "3. 访问 http://localhost:8080 验证系统功能"
echo
