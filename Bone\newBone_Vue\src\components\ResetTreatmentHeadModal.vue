<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="reset-modal">
        <!-- 关闭按钮 -->
        <img
          class="close-btn"
          :src="closeIcon"
          alt="关闭"
          @click="closeModal"
        />
        
        <!-- 槽位信息显示区域 -->
        <div class="slot-info-container">
          <div class="slot-info-text">
            {{ compartmentText }}{{ slotNumber }}号卡槽
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue'

// 导入图片
import resetBackground from '@/assets/images/复位某个治疗头.png'
import closeIcon from '@/assets/images/交互界面/参数设置/×.png'

// Props
const props = defineProps<{
  visible: boolean
  compartmentType?: string // 仓位类型：'上仓(浅部)' 或 '下仓(深部)'
  slotNumber?: number // 槽位号
  headNumber?: number // 治疗头编号（可能是99）
  failureCount?: number // 失败次数
}>()

// Emits
const emit = defineEmits<{
  close: []
  confirm: [] // 用户确认已复位
}>()

// 计算仓位显示文本
const compartmentText = computed(() => {
  if (!props.compartmentType) return ''
  return props.compartmentType === '上仓(浅部)' ? '上仓' : '下仓'
})

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const closeModal = () => {
  emit('close')
}

const confirmReset = () => {
  emit('confirm')
  emit('close')
}
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background: transparent;
  overflow: hidden;
  position: absolute;
  top: 147px;
  left: 658px;
}

/* 复位治疗头弹窗样式 */
.reset-modal {
  height: 752px;
  background: url('@/assets/images/复位某个治疗头.png') 0px 0px no-repeat;
  background-size: 631px 774px;
  width: 612px;
  position: relative;
}

/* 关闭按钮样式 */
.close-btn {
  width: 60px;
  height: 60px;
  margin: 46px 0 10px 510px;
  cursor: pointer;
  position: absolute;
  transition: transform 0.2s ease;
}

.close-btn:hover {
  transform: scale(1.1);
}

/* 槽位信息显示区域 */
.slot-info-container {
  position: absolute;
  top: 52%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slot-info-text {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 28px;
  font-family: 'Microsoft YaHei', sans-serif;
  font-weight: bold;
  padding: 15px 25px;
  border-radius: 8px;
  border: 2px solid #3db8a7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
  min-width: 200px;
}
</style> 