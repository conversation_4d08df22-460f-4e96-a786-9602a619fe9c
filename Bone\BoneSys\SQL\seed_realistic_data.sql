-- FREEBONE 医疗系统 - 模拟真实数据插入脚本
-- 使用 create_database_final.sql 创建数据库后执行本脚本

USE bonesys;

-- 1) 基础用户
INSERT INTO users (id, username, factory_password_hash, user_password_hash, last_updated_at) VALUES
(1, 'admin', '$2a$10$factory_hash_example', '$2a$10$user_hash_example', NOW())
ON DUPLICATE KEY UPDATE username=VALUES(username);

-- 2) 治疗头（1-20），全部完成状态，不包含治疗中状态
-- 上仓(浅部)：1-10；下仓(深部)：11-20
DELETE FROM treatment_heads;
INSERT INTO treatment_heads (head_number, slot_number, compartment_type, light_color, realtime_status, battery_level, total_usage_count, total_usage_minutes, max_usage_count)
VALUES
-- 上仓(浅部) - 全部完成状态
(1,  1, '上仓(浅部)', 3, 'CHARGED',  95,  12,  240, 500),
(2,  2, '上仓(浅部)', 3, 'CHARGED',  88,  30,  520, 500),
(3,  3, '上仓(浅部)', 3, 'CHARGED',  92,  50,  960, 500),
(4,  4, '上仓(浅部)', 1, 'CHARGING', 40,  18,  360, 500),
(5,  5, '上仓(浅部)', 2, 'ABNORMAL', 55,  22,  440, 500),
(6,  6, '上仓(浅部)', 3, 'CHARGED',  90,  10,  200, 500),
(7,  7, '上仓(浅部)', 3, 'CHARGED',  85,  80, 1520, 500),
(8,  8, '上仓(浅部)', 3, 'CHARGED',  97,   5,  100, 500),
(9,  9, '上仓(浅部)', 1, 'CHARGING', 30,  12,  240, 500),
(10,10, '上仓(浅部)', 3, 'CHARGED',  85,  40,  800, 500),
-- 下仓(深部) - 全部完成状态
(11, 1, '下仓(深部)', 3, 'CHARGED',  92,  20,  400, 500),
(12, 2, '下仓(深部)', 3, 'CHARGED',  80,  11,  220, 500),
(13, 3, '下仓(深部)', 3, 'CHARGED',  90,  60, 1200, 500),
(14, 4, '下仓(深部)', 1, 'CHARGING', 35,  14,  280, 500),
(15, 5, '下仓(深部)', 2, 'ABNORMAL', 50,  25,  500, 500),
(16, 6, '下仓(深部)', 3, 'CHARGED',  89,  33,  660, 500),
(17, 7, '下仓(深部)', 3, 'CHARGED',  88,  41,  820, 500),
(18, 8, '下仓(深部)', 3, 'CHARGED',  93,  19,  380, 500),
(19, 9, '下仓(深部)', 1, 'CHARGING', 28,  16,  320, 500),
(20,10, '下仓(深部)', 3, 'CHARGED',  87,  27,  540, 500);

-- 3) 参数预设（示例若干）
DELETE FROM treatment_parameter_presets;
INSERT INTO treatment_parameter_presets
(preset_name, body_part, default_duration, default_intensity, default_frequency, patch_type, recommended_count, min_patch_count, max_patch_count, is_default, is_active, created_at, updated_at)
VALUES
('肩部浅部方案', '肩部', 20, 45, 1000, 'SHALLOW', 2, 1, 4, 1, 1, NOW(), NOW()),
('腰部深部方案', '腰部', 30, 60, 100,  'DEEP',    4, 2, 6, 0, 1, NOW(), NOW()),
('膝关节浅部', '膝关节', 20, 45, 1000, 'SHALLOW', 2, 1, 4, 0, 1, NOW(), NOW()),
('颈肩深部',   '颈椎',   25, 45, 100,  'DEEP',    3, 1, 6, 0, 1, NOW(), NOW());

-- 4) 患者/档案/进程/详情 场景化数据
DELETE FROM body_part_stats;
DELETE FROM treatment_details;
DELETE FROM processes;
DELETE FROM records;
DELETE FROM patients;

-- 患者 A：存在一个远端治疗进程，全部详情到达“待取回/已终止”最终态，触发“待取回”弹窗
INSERT INTO patients (patient_card_id, name, gender, age, contact_info, created_at)
VALUES ('A001', '张三', '男', '35', '13800001111', NOW());
SET @patientA = LAST_INSERT_ID();
INSERT INTO records (record_number, patient_id, diagnosis_description, sessions_completed_count, created_at)
VALUES ('REC-A-001', @patientA, '骨质疏松-初诊', 1, CURDATE());
SET @recordA = LAST_INSERT_ID();
INSERT INTO processes (record_id, treatment_mode, status, start_time, end_time, remote_counted)
VALUES
(@recordA, 'TAKE_AWAY', 'IN_PROGRESS', DATE_SUB(NOW(), INTERVAL 60 MINUTE), NULL, 1);
SET @procA = LAST_INSERT_ID();
-- 详情：两个部位，其中一个待取回，一个终止（满足后端“最终态集合”且至少一个待取回）
INSERT INTO treatment_details (process_id, head_number_used, body_part, duration, intensity, frequency, patch_type, patch_quantity, status)
VALUES
(@procA, 3,  '肩部', 30, 45.00, 1000, 'SHALLOW', 2, 'AWAITING_RETURN'),
(@procA, 13, '腰部', 30, 45.00,  100, 'DEEP',    2, 'TERMINATED');

-- 患者 B：在院本地治疗已完成，触发“全部治疗完成”场景
INSERT INTO patients (patient_card_id, name, gender, age, contact_info, created_at)
VALUES ('B002', '李四', '女', '42', '13900002222', NOW());
SET @patientB = LAST_INSERT_ID();
INSERT INTO records (record_number, patient_id, diagnosis_description, sessions_completed_count, created_at)
VALUES ('REC-B-001', @patientB, '肩关节劳损', 1, CURDATE());
SET @recordB = LAST_INSERT_ID();
INSERT INTO processes (record_id, treatment_mode, status, start_time, end_time, remote_counted)
VALUES
(@recordB, 'ON_SITE', 'COMPLETED', DATE_SUB(NOW(), INTERVAL 50 MINUTE), DATE_SUB(NOW(), INTERVAL 30 MINUTE), 0);
SET @procB = LAST_INSERT_ID();
INSERT INTO treatment_details (process_id, head_number_used, body_part, duration, intensity, frequency, patch_type, patch_quantity, status)
VALUES
(@procB, 7,  '肩部', 20, 45.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(@procB, 17, '肘部', 20, 45.00, 1000, 'SHALLOW', 2, 'COMPLETED');

-- 患者 C：远端治疗已完成，档案完成次数已更新
INSERT INTO patients (patient_card_id, name, gender, age, contact_info, created_at)
VALUES ('C003', '王五', '男', '29', '13700003333', NOW());
SET @patientC = LAST_INSERT_ID();
INSERT INTO records (record_number, patient_id, diagnosis_description, sessions_completed_count, created_at)
VALUES ('REC-C-001', @patientC, '慢性腰痛', 1, CURDATE());
SET @recordC = LAST_INSERT_ID();
INSERT INTO processes (record_id, treatment_mode, status, start_time, end_time, remote_counted)
VALUES
(@recordC, 'TAKE_AWAY', 'COMPLETED', DATE_SUB(NOW(), INTERVAL 120 MINUTE), DATE_SUB(NOW(), INTERVAL 10 MINUTE), 1);
SET @procC = LAST_INSERT_ID();
INSERT INTO treatment_details (process_id, head_number_used, body_part, duration, intensity, frequency, patch_type, patch_quantity, status)
VALUES
(@procC, 8,  '腰部', 30, 45.00, 100, 'DEEP',    2, 'RETURNED'),
(@procC, 18, '骶髂', 30, 45.00, 100, 'DEEP',    2, 'RETURNED');

-- 患者 D：包含 RETURNED 状态，测试远端流程“最终态=待取回/已归还/已终止”的汇总逻辑
INSERT INTO patients (patient_card_id, name, gender, age, contact_info, created_at)
VALUES ('D004', '赵六', '男', '53', '13600004444', NOW());
SET @patientD = LAST_INSERT_ID();
INSERT INTO records (record_number, patient_id, diagnosis_description, sessions_completed_count, created_at)
VALUES ('REC-D-001', @patientD, '髌骨软化', 1, CURDATE());
SET @recordD = LAST_INSERT_ID();
INSERT INTO processes (record_id, treatment_mode, status, start_time, end_time, remote_counted)
VALUES
(@recordD, 'TAKE_AWAY', 'IN_PROGRESS', DATE_SUB(NOW(), INTERVAL 90 MINUTE), NULL, 1);
SET @procD = LAST_INSERT_ID();
INSERT INTO treatment_details (process_id, head_number_used, body_part, duration, intensity, frequency, patch_type, patch_quantity, status)
VALUES
(@procD, 2,  '膝关节', 30, 45.00, 1000, 'SHALLOW', 2, 'RETURNED'),
(@procD, 12, '踝关节', 30, 45.00, 1000, 'SHALLOW', 2, 'AWAITING_RETURN');

-- 补充：初始化部位统计（基于实际治疗详情）
DELETE FROM body_part_stats;
INSERT INTO body_part_stats (record_id, body_part, total_usage_count, total_duration_minutes)
VALUES
-- 患者A的部位统计（远端治疗，待取回状态会更新统计）
(@recordA, '肩部', 1, 30),  -- 待取回状态，已更新统计
-- 患者B的部位统计（本地治疗完成）
(@recordB, '肩部', 1, 20),
(@recordB, '肘部', 1, 20),
-- 患者C的部位统计（远端治疗已归还）
(@recordC, '腰部', 1, 30),
(@recordC, '骶髂', 1, 30),
-- 患者D的部位统计（远端治疗，待取回状态会更新统计）
(@recordD, '膝关节', 1, 30); -- 待取回状态，已更新统计

-- 验证提示
SELECT '模拟真实数据插入完成' AS info, NOW() AS ts;

