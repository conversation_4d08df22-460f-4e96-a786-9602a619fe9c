package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 治疗头查询调度服务
 * 实现智能查询策略：
 * - 全部治疗头在仓内时每2分钟查询
 * - 未放满时每30秒查询
 * - 特定操作时触发查询（开机、进入档案、确认创建）
 */
@Service
public class TreatmentHeadQuerySchedulerService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadQuerySchedulerService.class);
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private TreatmentHeadSyncService syncService;
    
    // 查询间隔配置（毫秒）
    private static final long FULL_COMPARTMENT_INTERVAL = 2 * 60 * 1000; // 2分钟
    private static final long PARTIAL_COMPARTMENT_INTERVAL = 30 * 1000;  // 30秒
    
    // 总治疗头数量
    private static final int TOTAL_TREATMENT_HEADS = 20;
    
    // 状态跟踪
    private final AtomicBoolean isFullCompartment = new AtomicBoolean(false);
    private final AtomicInteger currentHeadCount = new AtomicInteger(0);
    private volatile LocalDateTime lastQueryTime = LocalDateTime.now();
    private volatile LocalDateTime lastFullCheckTime = LocalDateTime.now();
    
    // 手动触发标志
    private final AtomicBoolean manualTriggerPending = new AtomicBoolean(false);
    
    /**
     * 定时查询任务 - 每30秒执行一次，内部根据状态决定是否实际查询
     */
    @Scheduled(fixedRate = 30000) // 30秒执行一次
    public void scheduledQuery() {
        try {
            LocalDateTime now = LocalDateTime.now();
            boolean shouldQuery = false;
            String reason = "";
            
            // 检查是否有手动触发的查询
            if (manualTriggerPending.getAndSet(false)) {
                shouldQuery = true;
                reason = "手动触发查询";
            } else {
                // 根据当前状态决定查询间隔
                long intervalSinceLastQuery = java.time.Duration.between(lastQueryTime, now).toMillis();
                
                if (isFullCompartment.get()) {
                    // 全部治疗头在仓内：每2分钟查询
                    if (intervalSinceLastQuery >= FULL_COMPARTMENT_INTERVAL) {
                        shouldQuery = true;
                        reason = "满仓状态定时查询（2分钟间隔）";
                    }
                } else {
                    // 未放满：每30秒查询
                    if (intervalSinceLastQuery >= PARTIAL_COMPARTMENT_INTERVAL) {
                        shouldQuery = true;
                        reason = "未满仓状态定时查询（30秒间隔）";
                    }
                }
            }
            
            if (shouldQuery) {
                performQuery(reason);
            }
            
        } catch (Exception e) {
            logger.error("定时查询任务执行失败", e);
        }
    }
    
    /**
     * 执行查询并更新状态
     */
    private void performQuery(String reason) {
        try {
            logger.debug("执行治疗头查询 - 原因: {}", reason);
            
            // 执行硬件查询
            List<TreatmentHeadInfo> headInfoList = hardwareService.syncAllTreatmentHeads();
            
            // 更新状态
            updateCompartmentStatus(headInfoList);
            
            // 记录查询时间
            lastQueryTime = LocalDateTime.now();
            
            logger.info("治疗头查询完成 - 原因: {}, 查询到 {} 个治疗头, 当前状态: {}", 
                       reason, headInfoList.size(), isFullCompartment.get() ? "满仓" : "未满仓");
            
        } catch (SerialCommunicationException e) {
            logger.error("治疗头查询失败 - 硬件通信异常: {}", e.getMessage());
        } catch (Exception e) {
            logger.error("治疗头查询失败 - 未知异常", e);
        }
    }
    
    /**
     * 更新仓位状态
     */
    private void updateCompartmentStatus(List<TreatmentHeadInfo> headInfoList) {
        int headCount = headInfoList.size();
        currentHeadCount.set(headCount);
        
        boolean wasFull = isFullCompartment.get();
        boolean isFull = (headCount >= TOTAL_TREATMENT_HEADS);
        isFullCompartment.set(isFull);
        
        // 记录状态变化
        if (wasFull != isFull) {
            if (isFull) {
                lastFullCheckTime = LocalDateTime.now();
                logger.info("治疗仓状态变更: 未满仓 -> 满仓 ({}/{})", headCount, TOTAL_TREATMENT_HEADS);
            } else {
                logger.info("治疗仓状态变更: 满仓 -> 未满仓 ({}/{})", headCount, TOTAL_TREATMENT_HEADS);
            }
        }
    }
    
    /**
     * 手动触发查询 - 开机启动时调用
     */
    public void triggerStartupQuery() {
        logger.info("触发开机启动查询");
        manualTriggerPending.set(true);
    }
    
    /**
     * 手动触发查询 - 进入档案时调用
     */
    public void triggerEnterRecordQuery() {
        logger.info("触发进入档案查询");
        manualTriggerPending.set(true);
    }
    
    /**
     * 手动触发查询 - 确认创建患者时调用
     */
    public void triggerCreatePatientQuery() {
        logger.info("触发确认创建患者查询");
        manualTriggerPending.set(true);
    }
    
    /**
     * 立即执行查询（同步方法）
     */
    public List<TreatmentHeadInfo> executeImmediateQuery(String reason) {
        try {
            logger.info("立即执行治疗头查询 - 原因: {}", reason);
            
            List<TreatmentHeadInfo> headInfoList = hardwareService.syncAllTreatmentHeads();
            updateCompartmentStatus(headInfoList);
            lastQueryTime = LocalDateTime.now();
            
            logger.info("立即查询完成 - 查询到 {} 个治疗头", headInfoList.size());
            return headInfoList;
            
        } catch (Exception e) {
            logger.error("立即查询失败", e);
            throw new RuntimeException("立即查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取当前仓位状态
     */
    public boolean isFullCompartment() {
        return isFullCompartment.get();
    }
    
    /**
     * 获取当前治疗头数量
     */
    public int getCurrentHeadCount() {
        return currentHeadCount.get();
    }
    
    /**
     * 获取上次查询时间
     */
    public LocalDateTime getLastQueryTime() {
        return lastQueryTime;
    }
    
    /**
     * 获取上次满仓检查时间
     */
    public LocalDateTime getLastFullCheckTime() {
        return lastFullCheckTime;
    }
    
    /**
     * 获取查询状态信息
     */
    public String getQueryStatusInfo() {
        return String.format("当前状态: %s (%d/%d), 上次查询: %s, 下次查询间隔: %s", 
                           isFullCompartment.get() ? "满仓" : "未满仓",
                           currentHeadCount.get(), 
                           TOTAL_TREATMENT_HEADS,
                           lastQueryTime.toString(),
                           isFullCompartment.get() ? "2分钟" : "30秒");
    }
    
    /**
     * 重置查询状态（用于测试或重新初始化）
     */
    public void resetQueryStatus() {
        isFullCompartment.set(false);
        currentHeadCount.set(0);
        lastQueryTime = LocalDateTime.now();
        lastFullCheckTime = LocalDateTime.now();
        manualTriggerPending.set(false);
        logger.info("查询状态已重置");
    }
}
