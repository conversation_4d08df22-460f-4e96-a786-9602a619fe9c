package com.Bone.BoneSys.config;

import com.Bone.BoneSys.service.TreatmentHeadQuerySchedulerService;
import com.Bone.BoneSys.service.ProcessStatusInitializationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 应用启动监听器
 * 在应用启动完成后执行初始化任务
 */
@Component
public class ApplicationStartupListener {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartupListener.class);

    @Autowired
    private TreatmentHeadQuerySchedulerService querySchedulerService;

    @Autowired
    private ProcessStatusInitializationService processStatusInitializationService;

    /**
     * 应用启动完成后的初始化任务
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logger.info("应用启动完成，开始执行初始化任务...");

        try {
            // 1. 检测并更新进程状态
            processStatusInitializationService.checkAndUpdateInProgressProcesses();
            logger.info("进程状态检测和更新已完成");

            // 2. 触发开机启动查询
            querySchedulerService.triggerStartupQuery();
            logger.info("开机启动查询已触发");

            // 可以在这里添加其他初始化任务

        } catch (Exception e) {
            logger.error("应用启动初始化任务执行失败", e);
        }

        logger.info("应用启动初始化任务完成");
    }
}
