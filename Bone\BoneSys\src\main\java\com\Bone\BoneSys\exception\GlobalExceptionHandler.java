package com.Bone.BoneSys.exception;

import com.Bone.BoneSys.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理治疗头推荐相关的异常
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理治疗头推荐异常
     */
    @ExceptionHandler(TreatmentHeadRecommendationException.class)
    public ResponseEntity<ApiResponse<Object>> handleTreatmentHeadRecommendationException(
            TreatmentHeadRecommendationException ex, WebRequest request) {
        
        logger.error("Treatment head recommendation exception: {}", ex.getTechnicalMessage(), ex);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", ex.getErrorCode());
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        
        // 根据异常类型设置不同的HTTP状态码
        HttpStatus status = determineHttpStatus(ex);
        
        ApiResponse<Object> response = ApiResponse.error(500, ex.getUserMessage(), errorDetails);
        
        return new ResponseEntity<>(response, status);
    }
    
    /**
     * 处理无效请求异常
     */
    @ExceptionHandler(InvalidRequestException.class)
    public ResponseEntity<ApiResponse<Object>> handleInvalidRequestException(
            InvalidRequestException ex, WebRequest request) {
        
        logger.warn("Invalid request: {}", ex.getFormattedErrors());
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", ex.getErrorCode());
        errorDetails.put("validationErrors", ex.getValidationErrors());
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        
        ApiResponse<Object> response = ApiResponse.error(400, ex.getUserMessage(), errorDetails);
        
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理治疗头数量不足异常
     */
    @ExceptionHandler(InsufficientTreatmentHeadsException.class)
    public ResponseEntity<ApiResponse<Object>> handleInsufficientTreatmentHeadsException(
            InsufficientTreatmentHeadsException ex, WebRequest request) {
        
        logger.info("Insufficient treatment heads: {}", ex.getDetailedShortageInfo());
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", ex.getErrorCode());
        errorDetails.put("availabilityDetail", ex.getAvailabilityDetail());
        errorDetails.put("shortageInfo", ex.getDetailedShortageInfo());
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        
        ApiResponse<Object> response = ApiResponse.error(409, ex.getUserMessage(), errorDetails);
        
        return new ResponseEntity<>(response, HttpStatus.CONFLICT);
    }
    
    /**
     * 处理指示灯控制异常
     */
    @ExceptionHandler(LightControlException.class)
    public ResponseEntity<ApiResponse<Object>> handleLightControlException(
            LightControlException ex, WebRequest request) {
        
        logger.warn("Light control failed: {}", ex.getFailureDetails());
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", ex.getErrorCode());
        errorDetails.put("failedHeadNumbers", ex.getFailedHeadNumbers());
        errorDetails.put("hardwareCommand", ex.getHardwareCommand());
        errorDetails.put("hardwareResponse", ex.getHardwareResponse());
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        
        ApiResponse<Object> response = ApiResponse.error(202, ex.getUserMessage(), errorDetails);
        
        // 指示灯控制失败通常不是严重错误，使用202 Accepted
        return new ResponseEntity<>(response, HttpStatus.ACCEPTED);
    }
    
    /**
     * 处理串口通信异常
     */
    @ExceptionHandler(SerialCommunicationException.class)
    public ResponseEntity<ApiResponse<Object>> handleSerialCommunicationException(
            SerialCommunicationException ex, WebRequest request) {
        
        logger.error("Serial communication error: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", "HARDWARE_COMMUNICATION_ERROR");
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        
        ApiResponse<Object> response = ApiResponse.error(503, 
            "硬件通信失败，请检查设备连接后重试", errorDetails);
        
        return new ResponseEntity<>(response, HttpStatus.SERVICE_UNAVAILABLE);
    }
    
    /**
     * 处理硬件异常
     */
    @ExceptionHandler(HardwareException.class)
    public ResponseEntity<ApiResponse<Object>> handleHardwareException(
            HardwareException ex, WebRequest request) {
        
        logger.error("Hardware error: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", "HARDWARE_ERROR");
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        
        ApiResponse<Object> response = ApiResponse.error(503, 
            "硬件设备异常，请检查设备状态或联系技术支持", errorDetails);
        
        return new ResponseEntity<>(response, HttpStatus.SERVICE_UNAVAILABLE);
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(
            Exception ex, WebRequest request) {
        
        logger.error("Unexpected error: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", "INTERNAL_SERVER_ERROR");
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        
        ApiResponse<Object> response = ApiResponse.error(500, 
            "系统发生意外错误，请稍后重试或联系技术支持", errorDetails);
        
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 根据异常类型确定HTTP状态码
     */
    private HttpStatus determineHttpStatus(TreatmentHeadRecommendationException ex) {
        if (ex instanceof InvalidRequestException) {
            return HttpStatus.BAD_REQUEST;
        } else if (ex instanceof InsufficientTreatmentHeadsException) {
            return HttpStatus.CONFLICT;
        } else if (ex instanceof LightControlException) {
            return HttpStatus.ACCEPTED;
        } else {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }
}