package com.Bone.BoneSys.exception;

import java.util.List;
import java.util.ArrayList;

/**
 * 指示灯控制异常
 * 当指示灯控制失败时抛出
 */
public class LightControlException extends TreatmentHeadRecommendationException {
    
    private List<Integer> failedHeadNumbers;
    private String hardwareCommand;
    private String hardwareResponse;
    
    public LightControlException(String message) {
        super("LIGHT_CONTROL_FAILED", 
              "指示灯控制失败，但不影响治疗头推荐结果", 
              message);
        this.failedHeadNumbers = new ArrayList<>();
    }
    
    public LightControlException(String message, Throwable cause) {
        super("LIGHT_CONTROL_FAILED", 
              "指示灯控制失败，但不影响治疗头推荐结果", 
              message, 
              cause);
        this.failedHeadNumbers = new ArrayList<>();
    }
    
    public LightControlException(String message, List<Integer> failedHeadNumbers) {
        super("LIGHT_CONTROL_FAILED", 
              generateUserMessage(failedHeadNumbers), 
              message);
        this.failedHeadNumbers = new ArrayList<>(failedHeadNumbers);
    }
    
    public LightControlException(String message, List<Integer> failedHeadNumbers, 
                               String hardwareCommand, String hardwareResponse) {
        super("LIGHT_CONTROL_FAILED", 
              generateUserMessage(failedHeadNumbers), 
              message);
        this.failedHeadNumbers = new ArrayList<>(failedHeadNumbers);
        this.hardwareCommand = hardwareCommand;
        this.hardwareResponse = hardwareResponse;
    }
    
    public List<Integer> getFailedHeadNumbers() {
        return new ArrayList<>(failedHeadNumbers);
    }
    
    public String getHardwareCommand() {
        return hardwareCommand;
    }
    
    public String getHardwareResponse() {
        return hardwareResponse;
    }
    
    /**
     * 添加失败的治疗头编号
     */
    public void addFailedHeadNumber(int headNumber) {
        if (failedHeadNumbers == null) {
            failedHeadNumbers = new ArrayList<>();
        }
        if (!failedHeadNumbers.contains(headNumber)) {
            failedHeadNumbers.add(headNumber);
        }
    }
    
    /**
     * 生成用户友好的错误消息
     */
    private static String generateUserMessage(List<Integer> failedHeadNumbers) {
        if (failedHeadNumbers == null || failedHeadNumbers.isEmpty()) {
            return "指示灯控制失败，但不影响治疗头推荐结果";
        }
        
        if (failedHeadNumbers.size() == 1) {
            return String.format("治疗头%d的指示灯控制失败，但不影响推荐结果", 
                               failedHeadNumbers.get(0));
        }
        
        return String.format("%d个治疗头的指示灯控制失败（编号：%s），但不影响推荐结果", 
                           failedHeadNumbers.size(), failedHeadNumbers.toString());
    }
    
    /**
     * 获取失败详情
     */
    public String getFailureDetails() {
        StringBuilder details = new StringBuilder();
        
        if (failedHeadNumbers != null && !failedHeadNumbers.isEmpty()) {
            details.append("失败的治疗头编号：").append(failedHeadNumbers).append("\n");
        }
        
        if (hardwareCommand != null) {
            details.append("硬件指令：").append(hardwareCommand).append("\n");
        }
        
        if (hardwareResponse != null) {
            details.append("硬件响应：").append(hardwareResponse).append("\n");
        }
        
        return details.toString().trim();
    }
    
    /**
     * 检查是否为部分失败（有些成功，有些失败）
     */
    public boolean isPartialFailure() {
        return failedHeadNumbers != null && !failedHeadNumbers.isEmpty();
    }
}