package com.Bone.BoneSys.dto.hardware;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 治疗头可用性响应DTO
 * 用于返回治疗头数量检查结果
 */
public class TreatmentHeadAvailabilityResponse {
    
    private boolean sufficient;           // 总体是否充足
    private int totalNeeded;             // 总共需要的数量
    private int totalAvailable;          // 总共可用的数量
    
    private int shallowNeeded;           // 浅层需要的数量
    private int shallowAvailable;        // 浅层可用的数量
    private boolean shallowSufficient;   // 浅层是否充足
    
    private int deepNeeded;              // 深层需要的数量
    private int deepAvailable;           // 深层可用的数量
    private boolean deepSufficient;      // 深层是否充足
    
    public TreatmentHeadAvailabilityResponse() {}
    
    // 兼容性构造器 - 为了保持向后兼容性
    public TreatmentHeadAvailabilityResponse(boolean sufficient, int availableCount, int requiredCount, 
                                           java.util.List<TreatmentHeadInfo> availableHeads, 
                                           java.util.List<TreatmentHeadRecommendation> recommendations, 
                                           String message) {
        this.sufficient = sufficient;
        this.totalAvailable = availableCount;
        this.totalNeeded = requiredCount;
        // 忽略其他参数，保持兼容性
    }
    
    public TreatmentHeadAvailabilityResponse(boolean sufficient, int availableCount, int requiredCount, 
                                           java.util.List<TreatmentHeadInfo> availableHeads, 
                                           java.util.List<TreatmentHeadRecommendation> recommendations, 
                                           String message, Object availabilityDetail) {
        this.sufficient = sufficient;
        this.totalAvailable = availableCount;
        this.totalNeeded = requiredCount;
        // 忽略其他参数，保持兼容性
    }
    
    // Getters and Setters
    public boolean isSufficient() {
        return sufficient;
    }
    
    public void setSufficient(boolean sufficient) {
        this.sufficient = sufficient;
    }
    
    public int getTotalNeeded() {
        return totalNeeded;
    }
    
    public void setTotalNeeded(int totalNeeded) {
        this.totalNeeded = totalNeeded;
    }
    
    public int getTotalAvailable() {
        return totalAvailable;
    }
    
    public void setTotalAvailable(int totalAvailable) {
        this.totalAvailable = totalAvailable;
    }
    
    public int getShallowNeeded() {
        return shallowNeeded;
    }
    
    public void setShallowNeeded(int shallowNeeded) {
        this.shallowNeeded = shallowNeeded;
    }
    
    public int getShallowAvailable() {
        return shallowAvailable;
    }
    
    public void setShallowAvailable(int shallowAvailable) {
        this.shallowAvailable = shallowAvailable;
    }
    
    public boolean isShallowSufficient() {
        return shallowSufficient;
    }
    
    public void setShallowSufficient(boolean shallowSufficient) {
        this.shallowSufficient = shallowSufficient;
    }
    
    public int getDeepNeeded() {
        return deepNeeded;
    }
    
    public void setDeepNeeded(int deepNeeded) {
        this.deepNeeded = deepNeeded;
    }
    
    public int getDeepAvailable() {
        return deepAvailable;
    }
    
    public void setDeepAvailable(int deepAvailable) {
        this.deepAvailable = deepAvailable;
    }
    
    public boolean isDeepSufficient() {
        return deepSufficient;
    }
    
    public void setDeepSufficient(boolean deepSufficient) {
        this.deepSufficient = deepSufficient;
    }
    
    // 兼容性方法 - 为了保持向后兼容性
    public String getMessage() {
        return sufficient ? "治疗头数量充足" : "治疗头数量不足";
    }
    
    public int getAvailableCount() {
        return totalAvailable;
    }
    
    public int getRequiredCount() {
        return totalNeeded;
    }
    
    public java.util.List<TreatmentHeadRecommendation> getRecommendations() {
        return new java.util.ArrayList<>();
    }
    
    public AvailabilityDetail getAvailabilityDetail() {
        return null;
    }
    
    public java.util.Map<String, java.util.List<TreatmentHeadRecommendation>> getRecommendationsByBodyPart() {
        return new java.util.HashMap<>();
    }
    
    @Override
    public String toString() {
        return "TreatmentHeadAvailabilityResponse{" +
                "sufficient=" + sufficient +
                ", totalNeeded=" + totalNeeded +
                ", totalAvailable=" + totalAvailable +
                ", shallowNeeded=" + shallowNeeded +
                ", shallowAvailable=" + shallowAvailable +
                ", shallowSufficient=" + shallowSufficient +
                ", deepNeeded=" + deepNeeded +
                ", deepAvailable=" + deepAvailable +
                ", deepSufficient=" + deepSufficient +
                '}';
    }
}