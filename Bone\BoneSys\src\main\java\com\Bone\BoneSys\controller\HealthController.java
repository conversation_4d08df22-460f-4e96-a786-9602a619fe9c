package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 用于测试系统是否正常运行
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {
    
    @GetMapping
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("application", "BoneSys");
        healthInfo.put("version", "1.0.0");
        
        return ApiResponse.success("系统运行正常", healthInfo);
    }
}