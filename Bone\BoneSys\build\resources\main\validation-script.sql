-- 数据一致性验证SQL脚本
-- 用于手动验证数据库中的数据是否与API返回的数据一致

-- 1. 检查患者基本信息的完整性
SELECT 
    p.id as patient_id,
    p.patient_card_id,
    p.name,
    p.age,
    p.gender,
    p.created_at,
    CASE 
        WHEN p.name IS NULL OR p.name = '' THEN '姓名缺失'
        WHEN p.patient_card_id IS NULL OR p.patient_card_id = '' THEN '卡号缺失'
        WHEN p.age IS NULL OR p.age = '' THEN '年龄缺失'
        WHEN p.gender IS NULL OR p.gender = '' THEN '性别缺失'
        ELSE '完整'
    END as data_completeness
FROM patients p
ORDER BY p.created_at DESC
LIMIT 20;

-- 2. 检查患者档案记录情况
SELECT 
    p.id as patient_id,
    p.name,
    p.patient_card_id,
    COUNT(r.id) as record_count,
    MAX(r.created_at) as latest_record_date,
    CASE 
        WHEN COUNT(r.id) = 0 THEN '无档案'
        ELSE CONCAT(COUNT(r.id), '个档案')
    END as record_status
FROM patients p
LEFT JOIN records r ON p.id = r.patient_id
GROUP BY p.id, p.name, p.patient_card_id
ORDER BY p.created_at DESC
LIMIT 20;

-- 3. 检查治疗部位统计情况
SELECT 
    p.id as patient_id,
    p.name,
    COUNT(DISTINCT bps.body_part) as body_part_count,
    GROUP_CONCAT(DISTINCT bps.body_part ORDER BY bps.body_part) as body_parts,
    SUM(bps.total_usage_count) as total_sessions,
    CASE 
        WHEN COUNT(DISTINCT bps.body_part) = 0 THEN '待确定'
        ELSE GROUP_CONCAT(DISTINCT bps.body_part ORDER BY bps.body_part)
    END as treatment_parts_display
FROM patients p
LEFT JOIN records r ON p.id = r.patient_id
LEFT JOIN body_part_stats bps ON r.id = bps.record_id
GROUP BY p.id, p.name
ORDER BY p.created_at DESC
LIMIT 20;

-- 4. 综合数据一致性检查
SELECT 
    p.id as patient_id,
    p.name,
    p.patient_card_id,
    p.age,
    p.gender,
    -- 就诊时间（最新档案建档日期）
    COALESCE(
        DATE_FORMAT(MAX(r.created_at), '%Y.%m.%d'),
        '无档案'
    ) as visit_time,
    -- 治疗部位
    COALESCE(
        GROUP_CONCAT(DISTINCT bps.body_part ORDER BY bps.body_part SEPARATOR ', '),
        '待确定'
    ) as treatment_parts,
    -- 总治疗次数
    COALESCE(SUM(bps.total_usage_count), 0) as total_sessions,
    -- 数据完整性评估
    CASE 
        WHEN p.name IS NULL OR p.name = '' THEN '基本信息缺失'
        WHEN COUNT(r.id) = 0 THEN '无档案记录'
        WHEN COUNT(bps.id) = 0 THEN '无治疗记录'
        ELSE '数据完整'
    END as data_status
FROM patients p
LEFT JOIN records r ON p.id = r.patient_id
LEFT JOIN body_part_stats bps ON r.id = bps.record_id
GROUP BY p.id, p.name, p.patient_card_id, p.age, p.gender
ORDER BY p.created_at DESC
LIMIT 20;

-- 5. 检查数据质量统计
SELECT 
    '患者总数' as metric,
    COUNT(*) as count
FROM patients
UNION ALL
SELECT 
    '有档案的患者数',
    COUNT(DISTINCT r.patient_id)
FROM records r
UNION ALL
SELECT 
    '有治疗记录的患者数',
    COUNT(DISTINCT r.patient_id)
FROM records r
JOIN body_part_stats bps ON r.id = bps.record_id
UNION ALL
SELECT 
    '姓名缺失的患者数',
    COUNT(*)
FROM patients p
WHERE p.name IS NULL OR p.name = ''
UNION ALL
SELECT 
    '卡号缺失的患者数',
    COUNT(*)
FROM patients p
WHERE p.patient_card_id IS NULL OR p.patient_card_id = '';

-- 6. 检查可能的数据不一致问题
-- 检查是否有患者的年龄格式不统一
SELECT 
    'age_format_issues' as issue_type,
    COUNT(*) as count
FROM patients p
WHERE p.age IS NOT NULL 
  AND p.age != '' 
  AND p.age NOT LIKE '%岁'
UNION ALL
-- 检查是否有重复的就诊卡号
SELECT 
    'duplicate_card_ids',
    COUNT(*) - COUNT(DISTINCT patient_card_id)
FROM patients
WHERE patient_card_id IS NOT NULL AND patient_card_id != ''
UNION ALL
-- 检查是否有档案但没有患者的情况
SELECT 
    'orphaned_records',
    COUNT(*)
FROM records r
LEFT JOIN patients p ON r.patient_id = p.id
WHERE p.id IS NULL;

-- 7. 性能检查 - 验证索引是否生效
EXPLAIN SELECT p.*, r.created_at, bps.body_part, bps.total_usage_count
FROM patients p
LEFT JOIN records r ON p.id = r.patient_id
LEFT JOIN body_part_stats bps ON r.id = bps.record_id
WHERE p.name LIKE '%张%'
ORDER BY p.created_at DESC
LIMIT 10;