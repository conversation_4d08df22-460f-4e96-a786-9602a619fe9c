-- ========================================
-- 调试治疗头服务数据获取问题
-- 检查后端服务实际获取的数据与数据库数据是否一致
-- ========================================

USE bonesys;

-- 1. 检查数据库连接和当前数据
SELECT '=== 当前数据库连接信息 ===' as message;
SELECT CONNECTION_ID(), USER(), DATABASE();

-- 2. 检查治疗头表的实际数据
SELECT '=== 数据库中的治疗头数据 ===' as message;
SELECT 
    head_id,
    head_number,
    slot_number,
    compartment_type,
    battery_level,
    realtime_status,
    light_color,
    total_usage_count,
    total_usage_minutes,
    max_usage_count
FROM treatment_heads 
ORDER BY head_number;

-- 3. 检查是否有null值
SELECT '=== 检查null值 ===' as message;
SELECT 
    head_number,
    CASE WHEN battery_level IS NULL THEN 'NULL' ELSE CAST(battery_level AS CHAR) END as battery_level,
    CASE WHEN realtime_status IS NULL THEN 'NULL' ELSE realtime_status END as realtime_status,
    CASE WHEN slot_number IS NULL THEN 'NULL' ELSE CAST(slot_number AS CHAR) END as slot_number,
    CASE WHEN compartment_type IS NULL THEN 'NULL' ELSE compartment_type END as compartment_type
FROM treatment_heads 
WHERE battery_level IS NULL 
   OR realtime_status IS NULL 
   OR slot_number IS NULL 
   OR compartment_type IS NULL
ORDER BY head_number;

-- 4. 检查数据类型和约束
SELECT '=== 表结构信息 ===' as message;
DESCRIBE treatment_heads;

-- 5. 检查是否有重复的治疗头编号
SELECT '=== 检查重复编号 ===' as message;
SELECT head_number, COUNT(*) as count
FROM treatment_heads 
GROUP BY head_number 
HAVING COUNT(*) > 1;

-- 6. 检查事务隔离级别
SELECT '=== 事务隔离级别 ===' as message;
SELECT @@transaction_isolation;

-- 7. 强制刷新数据（清除可能的缓存）
SELECT '=== 强制刷新数据 ===' as message;
FLUSH TABLES treatment_heads;

-- 8. 再次检查数据
SELECT '=== 刷新后的治疗头数据 ===' as message;
SELECT 
    head_number,
    slot_number,
    compartment_type,
    battery_level,
    realtime_status,
    light_color
FROM treatment_heads 
ORDER BY head_number;

-- 9. 检查最近的数据修改时间（如果有时间戳字段）
SELECT '=== 检查数据修改时间 ===' as message;
SELECT 
    head_number,
    battery_level,
    realtime_status,
    'No timestamp column' as last_modified
FROM treatment_heads 
ORDER BY head_number;

-- 10. 模拟后端查询逻辑
SELECT '=== 模拟后端查询逻辑 ===' as message;
SELECT 
    head_number,
    CASE WHEN battery_level IS NULL THEN 0 ELSE battery_level END as converted_battery_level,
    CASE WHEN slot_number IS NULL THEN 0 ELSE slot_number END as converted_slot_number,
    realtime_status,
    compartment_type,
    total_usage_count
FROM treatment_heads 
ORDER BY head_number;

SELECT '=== 调试完成 ===' as message;
