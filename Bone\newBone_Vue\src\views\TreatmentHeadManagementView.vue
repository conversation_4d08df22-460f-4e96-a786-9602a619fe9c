<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { getTreatmentHeads } from '@/api';
import LifecyclePieChart from '../components/LifecyclePieChart.vue';

// 静态导入所有需要的图片资源
import bgImage1 from '@/assets/images/treatmentheadmanagement/135e4e4155500e59dba1e98ac8f5392e.png';
import bgImage2 from '@/assets/images/treatmentheadmanagement/e0ce312b6c888c0419c82f1d70452b33.png';
import separator1 from '@/assets/images/treatmentheadmanagement/d6437a0322154d4e74159100ffc60b0f.png';
import separator2 from '@/assets/images/treatmentheadmanagement/efc3f7ab56b90859ce0e71ea32f9a279.png';
import separator3 from '@/assets/images/treatmentheadmanagement/4ac32063b4c3393c930cc3e11e828bc6.png';
import separator4 from '@/assets/images/treatmentheadmanagement/d989410f0eba87629a33ff15b7eae566.png';
import navPrev from '@/assets/images/treatmentheadmanagement/2d02b03af5a1c7ba4a54fc40853940ff.png';
import navNext from '@/assets/images/treatmentheadmanagement/de3017b06ea855f5bf422eb2bbe1b3dc.png';
import iconAvailable from '@/assets/images/treatmentheadmanagement/91c6cd547d00aaa77cc5586211bc0001.png';
import iconCharging from '@/assets/images/treatmentheadmanagement/f92c779064ed50b444c052a4f103ad8c.png';
import iconInUse from '@/assets/images/treatmentheadmanagement/2de1768cca6436628b45c34be14393ac.png';
import bgMain from '@/assets/images/treatmentheadmanagement/a05eb3d383cf70e0da6d25beca0e335d.png';
import bgBox from '@/assets/images/treatmentheadmanagement/a64bc209b8c81865ebcc45621fe19bbe.png';
import bgContainer from '@/assets/images/treatmentheadmanagement/7165f76fa0b053e05201a568aa4fc31f.png';
import separatorBg1 from '@/assets/images/treatmentheadmanagement/5ff61b1d884414dd419a6d37f974583e.png';
import separatorBg2 from '@/assets/images/treatmentheadmanagement/372e95f51139b9c892d32fe3b3ae2b6e.png';

const router = useRouter();

// 数据响应式变量
const treatmentHeads = ref<any[]>([]);
const loading = ref(false);
const currentPage = ref(0); // 恢复从0开始，用于客户端分页
const pageSize = ref(7); // 每页显示7个数据

// 状态映射
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'AVAILABLE':
    case 'CHARGED': // 新增CHARGED状态支持
      return {
        icon: iconAvailable,
        text: '充电结束'
      };
    case 'CHARGING':
      return {
        icon: iconCharging,
        text: '充电中'
      };
    case 'IN_USE':
    case 'TREATING': // TREATING状态使用治疗中图标
      return {
        icon: iconInUse,
        text: '治疗中' // 修复：明确显示为"治疗中"
      };
    case 'LOW_BATTERY':
      return {
        icon: iconCharging, // 低电量使用充电图标
        text: '电量不足'
      };
    default:
      console.warn('未知状态:', status);
      return {
        icon: iconAvailable,
        text: '充电结束'
      };
  }
};

// 恢复客户端分页计算
const currentPageData = computed(() => {
  const start = currentPage.value * pageSize.value;
  const end = start + pageSize.value;
  return treatmentHeads.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(treatmentHeads.value.length / pageSize.value);
});

// 获取治疗头数据
const fetchTreatmentHeads = async () => {
  try {
    loading.value = true;
    // 获取所有治疗头数据，用于客户端分页
    const response = await getTreatmentHeads({
      page: 1,
      size: 20 // 获取所有20个治疗头
    });
    
    // 适配新的API响应格式
    if (response.data && response.data.heads && Array.isArray(response.data.heads)) {
      treatmentHeads.value = response.data.heads.map((head: any) => ({
        headId: head.headNumber, // 使用 headNumber 作为 headId
        headNumber: head.headNumber,
        status: head.iconStatus, // 使用 iconStatus 作为状态
        textStatus: head.textStatus,
        iconStatus: head.iconStatus,
        totalUsageCount: head.usageCount, // 映射 usageCount 到 totalUsageCount
        usageCount: head.usageCount,
        totalUsageDurationH: head.totalUsageTime, // 映射 totalUsageTime 到 totalUsageDurationH
        totalUsageTime: head.totalUsageTime,
        batteryLevel: head.batteryLevel,
        lightColor: head.lightColor
      }));
      
      console.log('治疗头数据映射成功:', {
        heads: treatmentHeads.value.length,
        currentPage: currentPage.value,
        totalPages: totalPages.value
      });
    } else {
      treatmentHeads.value = [];
      console.warn('治疗头数据格式异常:', response.data);
    }
  } catch (error: any) {
    console.error('获取治疗头数据失败:', error);
    treatmentHeads.value = []; // 失败时显示空列表
    MessagePlugin.error('获取治疗头列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 翻页功能 - 客户端分页不需要重新获取数据
const goToPreviousPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--;
    // 客户端分页，不需要重新获取数据
  }
};

const goToNextPage = () => {
  if (currentPage.value < totalPages.value - 1) {
    currentPage.value++;
    // 客户端分页，不需要重新获取数据
  }
};

// 返回首页
const goBack = () => {
  router.push('/');
};

// 格式化使用时间 - 将"X小时Y分钟"格式转换为"X.Xh"格式
const formatUsageTime = (timeString: string): string => {
  if (!timeString || typeof timeString !== 'string') {
    return '0.0h';
  }
  
  try {
    // 解析"X小时Y分钟"格式
    const hourMatch = timeString.match(/(\d+)小时/);
    const minuteMatch = timeString.match(/(\d+)分钟/);
    
    const hours = hourMatch ? parseInt(hourMatch[1]) : 0;
    const minutes = minuteMatch ? parseInt(minuteMatch[1]) : 0;
    
    // 转换为小时数（保留1位小数）
    const totalHours = hours + (minutes / 60);
    return `${totalHours.toFixed(1)}h`;
  } catch (error) {
    console.warn('时间格式解析错误:', timeString, error);
    return '0.0h';
  }
};

// 页面加载时获取数据
onMounted(() => {
  fetchTreatmentHeads();
});
</script>

<template>
  <div class="page flex-col">
    <div class="image-wrapper_1 flex-col">
      <img
        class="image_1"
        referrerpolicy="no-referrer"
        :src="bgImage1"
      />
    </div>
    <div class="box_1 flex-col">
      <div class="group_1 flex-row justify-between">
        <img
          class="image_2 button"
          referrerpolicy="no-referrer"
          :src="bgImage2"
          @click="goBack"
        />
        <span class="text_1">治疗头管理</span>
      </div>
      <div class="group_2 flex-row justify-end">
        <div class="box_2 flex-col">
          <div class="text-wrapper_1 flex-row">
            <span class="text_2">编号</span>
            <span class="text_3">状态</span>
            <span class="text_4">次数</span>
            <span class="text_5">时间/h</span>
            <span class="text_6">电池电量</span>
            <span class="text_7">治疗头状态</span>
          </div>
          <img
            class="image_3"
            referrerpolicy="no-referrer"
            :src="separator1"
          />
          
          <!-- 治疗头数据行 -->
          <template v-for="(head, index) in currentPageData" :key="head.headId">
            <div :class="`box_${index + 3} flex-row align-items-center`">
              <span class="text-number">{{ head.headId.toString().padStart(2, '0') }}</span>
              <div class="status-wrapper">
                <img
                  class="status-icon"
                  referrerpolicy="no-referrer"
                  :src="getStatusInfo(head.status).icon"
                />
                <span class="status-text">{{ getStatusInfo(head.status).text }}</span>
              </div>
              <span class="text-count">{{ head.totalUsageCount }}</span>
              <span class="text-time">{{ formatUsageTime(head.totalUsageDurationH) }}</span>
              <span class="text-battery">{{ head.batteryLevel }}%</span>
              <div class="pie-chart-wrapper">
                <LifecyclePieChart 
                  :usageCount="head.totalUsageCount" 
                  :maxUsage="500" 
                  :size="42" 
                />
              </div>
            </div>
            
            <!-- 每行下面的横线 -->
            <img
              v-if="index < currentPageData.length - 1"
              :class="`image_${index + 5}`"
              referrerpolicy="no-referrer"
              :src="separator2"
            />
          </template>
          
          <!-- 最后一行下面的横线 - 始终显示 -->
          <img
            v-if="currentPageData.length > 0"
            :class="`image_${currentPageData.length + 4}`"
            referrerpolicy="no-referrer"
            :src="separator4"
          />
          
          <!-- 空数据占位 -->
          <template v-if="currentPageData.length === 0 && !loading">
            <div v-for="i in pageSize" :key="i">
              <div :class="`box_${i + 2} flex-row align-items-center empty-row`">
                <span class="text-number">--</span>
                <div class="status-wrapper">
                  <img
                    class="status-icon"
                    referrerpolicy="no-referrer"
                    :src="iconAvailable"
                  />
                  <span class="status-text">充电结束</span>
                </div>
                <span class="text-count">--</span>
                <span class="text-time">--</span>
                <span class="text-battery">--</span>
                <div class="pie-chart-wrapper">
                  <LifecyclePieChart :usageCount="0" :maxUsage="500" :size="42" />
                </div>
              </div>
              
              <!-- 每行下面的横线 -->
              <img
                v-if="i < pageSize"
                :class="`image_${i + 4}`"
                referrerpolicy="no-referrer"
                :src="separator2"
              />
            </div>
            
            <!-- 空数据时的最后一行横线 -->
            <img
              v-if="pageSize > 0"
              :class="`image_${pageSize + 4}`"
              referrerpolicy="no-referrer"
              :src="separator4"
            />
          </template>
        </div>
        
        <img
          class="image_90 button"
          referrerpolicy="no-referrer"
          :src="navPrev"
          @click="goToPreviousPage"
          :class="{ disabled: currentPage === 0 }"
        />
        <img
          class="image_100 button"
          referrerpolicy="no-referrer"
          :src="navNext"
          @click="goToNextPage"
          :class="{ disabled: currentPage >= totalPages - 1 }"
        />
      </div>
      <div class="text-wrapper_2 flex-row">
        <span class="text_23">({{ currentPage + 1 }}/{{ totalPages || 1 }})</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.image-wrapper_1 {
  height: 1080px;
  background: url('@/assets/images/treatmentheadmanagement/a05eb3d383cf70e0da6d25beca0e335d.png')
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 3px;
  width: 1917px;
}

.image_1 {
  width: 1910px;
  height: 993px;
  margin: 87px 0 0 7px;
}

.box_1 {
  height: 1080px;
  background: url('@/assets/images/treatmentheadmanagement/a64bc209b8c81865ebcc45621fe19bbe.png')
    100% no-repeat;
  background-size: 100% 100%;
  width: 1919px;
  position: absolute;
  left: 1px;
  top: 0;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.group_1 {
  width: 1044px;
  height: 70px;
  margin: 20px 0 0 105px;
}

.image_2 {
  width: 169px;
  height: 70px;
  cursor: pointer;
}

.text_1 {
  width: 376px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
  position: absolute;
  top: 30px;
  left: 755px;
}

.group_2 {
  position: relative;
  width: 1844px;
  height: 812px;
  margin: 98px 0 0 33px;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.box_2 {
  width: 1532px;
  height: 812px;
  background: url('@/assets/images/treatmentheadmanagement/7165f76fa0b053e05201a568aa4fc31f.png')
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 156px;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.text-wrapper_1 {
  width: 1350px;
  height: 33px;
  margin: 116px 0 0 129px;
  display: flex;
  align-items: center;
  padding-top: 90px;
}

.text_2 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text_3 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 163px;
}

.text_4 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 171px;
}

.text_5 {
  width: 121px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 96px;
}

.text_6 {
  width: 155px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 110px;
}

.text_7 {
  width: 197px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 112px;
}

.image_3 {
  width: 1382px;
  height: 1px;
  margin: 40px 0 0 84px;
}

/* 数据行样式 */
.box_3, .box_4, .box_5, .box_6, .box_7, .box_8, .box_9 {
  width: 1258px;
  height: 42px;
  margin: 12px 0 0 144px;
  display: flex;
  align-items: center;
}

.align-items-center {
  align-items: center;
}

.text-number {
  width: 39px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 0;
}

.status-wrapper {
  display: flex;
  align-items: center;
  margin: 0 0 0 84px;
  width: 200px;
}

.status-icon {
  width: 58px;
  height: 33px;
  margin: 0;
}

.status-text {
  width: 157px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0 0 0 40px;
}

.text-count {
  width: 16px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0 0 0 170px;
}

.text-time {
  width: 15px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0 0 0 178px;
}

.text-battery {
  width: 107px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 0 0 0 187px;
}

.pie-chart-wrapper {
  width: 42px;
  height: 42px;
  margin-left: 213px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分隔线样式 */
.image_5, .image_6, .image_7, .image_8, .image_9, .image_10, .image_11 {
  width: 1382px;
  height: 1px;
  margin: 16px 0 0 84px;
}

.image-wrapper_2 {
  height: 1px;
  background: url('@/assets/images/treatmentheadmanagement/5ff61b1d884414dd419a6d37f974583e.png')
    0px -1px no-repeat;
  background-size: 1382px 2px;
  width: 1382px;
  margin: 9px 0 0 84px;
}

.image_12 {
  width: 1382px;
  height: 1px;
}

.image-wrapper_3, .image-wrapper_4, .image-wrapper_5, .image-wrapper_6, 
.image-wrapper_7, .image-wrapper_8, .image-wrapper_9 {
  height: 1px;
  background: url('@/assets/images/treatmentheadmanagement/372e95f51139b9c892d32fe3b3ae2b6e.png')
    0px -1px no-repeat;
  background-size: 1382px 2px;
  width: 1382px;
  margin: 12px 0 0 84px;
}

.image_13, .image_14, .image_15, .image_16, .image_17, .image_18, .image_19 {
  width: 1382px;
  height: 1px;
}

.image_90 {
  width: 169px;
  height: 172px;
  top:300px;
  cursor: pointer;
  position: absolute;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.image_100 {
  width: 169px;
  height: 172px;
  top:300px;
  right:0;
  cursor: pointer;
  position: absolute;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.text-wrapper_2 {
  width: 61px;
  height: 26px;
  margin: 2px 0 52px 942px;
}

.text_23 {
  width: 61px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 27px;
  font-family: Adobe Heiti Std R;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 49px;
}

/* 空数据行样式 */
.empty-row {
  opacity: 0.3;
}

/* 禁用状态样式 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

/* 按钮动画效果 */
.button {
  transition: transform 0.1s ease;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.button:active {
  transform: scale(0.95);
}

.button:active.image_2 {
  transform: scale(0.98);
}
</style> 