-- 🔧 修复治疗头状态脚本
-- 解决参数下载页面显示"可用治疗头数量不足"的问题

USE bonesys;

-- 1. 查看当前治疗头状态
SELECT 
    '=== 修复前的治疗头状态 ===' as info,
    COUNT(*) as total_heads,
    SUM(CASE WHEN realtime_status = 'CHARGED' THEN 1 ELSE 0 END) as charged_heads,
    SUM(CASE WHEN realtime_status = 'CHARGING' THEN 1 ELSE 0 END) as charging_heads,
    SUM(CASE WHEN realtime_status = 'TREATING' THEN 1 ELSE 0 END) as treating_heads
FROM treatment_heads;

-- 2. 修复治疗头状态 - 将充电中的治疗头改为充电完成
UPDATE treatment_heads 
SET realtime_status = 'CHARGED' 
WHERE realtime_status = 'CHARGING' 
  AND battery_level >= 80;

-- 3. 修复正在治疗的治疗头 - 将其改为充电完成（模拟治疗结束）
UPDATE treatment_heads 
SET realtime_status = 'CHARGED' 
WHERE realtime_status = 'TREATING';

-- 4. 确保电量充足
UPDATE treatment_heads 
SET battery_level = 100 
WHERE battery_level < 80;

-- 5. 修复仓位类型 - 确保1-10号为浅部，11-20号为深部
UPDATE treatment_heads 
SET compartment_type = '上仓(浅部)' 
WHERE head_number BETWEEN 1 AND 10;

UPDATE treatment_heads 
SET compartment_type = '下仓(深部)' 
WHERE head_number BETWEEN 11 AND 20;

-- 6. 删除无效的治疗头编号（0号和其他异常编号）
DELETE FROM treatment_heads 
WHERE head_number <= 0 OR head_number > 20;

-- 7. 确保有足够的浅部和深部治疗头
-- 如果缺少治疗头，插入标准的20个治疗头
INSERT IGNORE INTO treatment_heads (
    head_number, 
    compartment_type, 
    slot_number, 
    battery_level, 
    realtime_status, 
    total_usage_count,
    max_usage_count,
    light_color
) VALUES
-- 浅部治疗头 (1-10)
(1, '上仓(浅部)', 1, 100, 'CHARGED', 0, 1000, 0),
(2, '上仓(浅部)', 2, 100, 'CHARGED', 0, 1000, 0),
(3, '上仓(浅部)', 3, 100, 'CHARGED', 0, 1000, 0),
(4, '上仓(浅部)', 4, 100, 'CHARGED', 0, 1000, 0),
(5, '上仓(浅部)', 5, 100, 'CHARGED', 0, 1000, 0),
(6, '上仓(浅部)', 6, 100, 'CHARGED', 0, 1000, 0),
(7, '上仓(浅部)', 7, 100, 'CHARGED', 0, 1000, 0),
(8, '上仓(浅部)', 8, 100, 'CHARGED', 0, 1000, 0),
(9, '上仓(浅部)', 9, 100, 'CHARGED', 0, 1000, 0),
(10, '上仓(浅部)', 10, 100, 'CHARGED', 0, 1000, 0),
-- 深部治疗头 (11-20)
(11, '下仓(深部)', 1, 100, 'CHARGED', 0, 1000, 0),
(12, '下仓(深部)', 2, 100, 'CHARGED', 0, 1000, 0),
(13, '下仓(深部)', 3, 100, 'CHARGED', 0, 1000, 0),
(14, '下仓(深部)', 4, 100, 'CHARGED', 0, 1000, 0),
(15, '下仓(深部)', 5, 100, 'CHARGED', 0, 1000, 0),
(16, '下仓(深部)', 6, 100, 'CHARGED', 0, 1000, 0),
(17, '下仓(深部)', 7, 100, 'CHARGED', 0, 1000, 0),
(18, '下仓(深部)', 8, 100, 'CHARGED', 0, 1000, 0),
(19, '下仓(深部)', 9, 100, 'CHARGED', 0, 1000, 0),
(20, '下仓(深部)', 10, 100, 'CHARGED', 0, 1000, 0);

-- 8. 查看修复后的治疗头状态
SELECT 
    '=== 修复后的治疗头状态 ===' as info,
    COUNT(*) as total_heads,
    SUM(CASE WHEN realtime_status = 'CHARGED' THEN 1 ELSE 0 END) as charged_heads,
    SUM(CASE WHEN realtime_status = 'CHARGING' THEN 1 ELSE 0 END) as charging_heads,
    SUM(CASE WHEN realtime_status = 'TREATING' THEN 1 ELSE 0 END) as treating_heads
FROM treatment_heads;

-- 9. 按仓位类型统计
SELECT 
    '=== 按仓位类型统计 ===' as info,
    compartment_type,
    COUNT(*) as count,
    SUM(CASE WHEN realtime_status = 'CHARGED' AND battery_level >= 20 THEN 1 ELSE 0 END) as available_count
FROM treatment_heads 
GROUP BY compartment_type;

-- 10. 显示所有治疗头的详细信息
SELECT 
    head_number,
    compartment_type,
    slot_number,
    battery_level,
    realtime_status,
    total_usage_count
FROM treatment_heads 
ORDER BY head_number;

-- 修复完成提示
SELECT '🎉 治疗头状态修复完成！现在应该有足够的可用治疗头了。' as result;
